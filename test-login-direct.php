<?php
/**
 * اختبار تسجيل دخول مباشر بدون جلسة
 */

// تحميل الملفات الأساسية
require_once 'core/Environment.php';
Environment::load();
require_once 'core/helpers.php';
require_once 'core/Database.php';

echo "🧪 اختبار تسجيل الدخول المباشر\n";
echo "================================\n\n";

try {
    // اختبار قاعدة البيانات أولاً
    echo "1. اختبار قاعدة البيانات...\n";
    $db = Database::getInstance();
    echo "   ✅ تم الاتصال بقاعدة البيانات\n";
    
    // البحث عن المستخدم
    echo "2. البحث عن المستخدم admin...\n";
    $user = $db->selectOne("SELECT * FROM users WHERE username = ? AND is_active = 1", ['admin']);
    
    if (!$user) {
        echo "   ❌ المستخدم غير موجود\n";
        exit(1);
    }
    
    echo "   ✅ المستخدم موجود: " . $user['username'] . " (" . $user['email'] . ")\n";
    
    // اختبار كلمة المرور
    echo "3. اختبار كلمة المرور...\n";
    if (password_verify('admin123', $user['password_hash'])) {
        echo "   ✅ كلمة المرور صحيحة\n";
    } else {
        echo "   ❌ كلمة المرور خاطئة\n";
        exit(1);
    }
    
    // اختبار تحديث آخر تسجيل دخول
    echo "4. تحديث آخر تسجيل دخول...\n";
    $updated = $db->update('users', ['last_login_at' => date('Y-m-d H:i:s')], ['id' => $user['id']]);
    
    if ($updated) {
        echo "   ✅ تم تحديث آخر تسجيل دخول\n";
    } else {
        echo "   ❌ فشل تحديث آخر تسجيل دخول\n";
    }
    
    echo "\n🎉 جميع اختبارات تسجيل الدخول نجحت!\n";
    echo "المشكلة قد تكون في إدارة الجلسات أو التوجيه.\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?>
