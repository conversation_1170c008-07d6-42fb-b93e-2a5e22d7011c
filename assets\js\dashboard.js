/**
 * SeaSystem Dashboard JavaScript
 * سكريبت لوحة التحكم الرئيسية
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

/**
 * تهيئة لوحة التحكم
 */
function initializeDashboard() {
    initSidebar();
    initCurrentTime();
    initTooltips();
    initMobileMenu();
    initNotifications();
    loadDashboardData();
}

/**
 * تهيئة القائمة الجانبية
 */
function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('toggleBtn');
    const mainContent = document.getElementById('mainContent');
    
    // زر طي/فتح القائمة الجانبية
    toggleBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        
        // حفظ حالة القائمة في localStorage
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    });
    
    // استرجاع حالة القائمة المحفوظة
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        sidebar.classList.add('collapsed');
    }
    
    // معالجة القوائم الفرعية
    const menuItems = document.querySelectorAll('.menu-item.has-submenu');
    
    menuItems.forEach(item => {
        const menuLink = item.querySelector('.menu-link');
        const submenu = item.querySelector('.submenu');
        
        menuLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إغلاق القوائم الفرعية الأخرى
            menuItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('open');
                }
            });
            
            // تبديل حالة القائمة الفرعية الحالية
            item.classList.toggle('open');
        });
    });
    
    // إغلاق القوائم الفرعية عند طي القائمة الجانبية
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                if (sidebar.classList.contains('collapsed')) {
                    menuItems.forEach(item => {
                        item.classList.remove('open');
                    });
                }
            }
        });
    });
    
    observer.observe(sidebar, { attributes: true });
    
    // تفعيل الرابط الحالي
    const currentPath = window.location.pathname;
    const menuLinks = document.querySelectorAll('.menu-link, .submenu a');
    
    menuLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '#') {
            // إزالة التفعيل من جميع الروابط
            menuLinks.forEach(l => l.closest('.menu-item')?.classList.remove('active'));
            
            // تفعيل الرابط الحالي
            const menuItem = link.closest('.menu-item');
            if (menuItem) {
                menuItem.classList.add('active');
                
                // فتح القائمة الفرعية إذا كان الرابط فيها
                const parentSubmenu = link.closest('.submenu');
                if (parentSubmenu) {
                    const parentMenuItem = parentSubmenu.closest('.menu-item');
                    if (parentMenuItem) {
                        parentMenuItem.classList.add('open');
                    }
                }
            }
        }
    });
}

/**
 * تهيئة عرض الوقت الحالي
 */
function initCurrentTime() {
    const timeElement = document.getElementById('currentTime');
    
    function updateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        const arabicTime = now.toLocaleDateString('ar-SA', options);
        timeElement.textContent = arabicTime;
    }
    
    // تحديث الوقت كل دقيقة
    updateTime();
    setInterval(updateTime, 60000);
}

/**
 * تهيئة التلميحات
 */
function initTooltips() {
    const elementsWithTooltips = document.querySelectorAll('[title]');
    
    elementsWithTooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            showTooltip(this, this.getAttribute('title'));
        });
        
        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

/**
 * عرض التلميح
 */
function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10000;
        pointer-events: none;
        white-space: nowrap;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.top = (rect.top - tooltip.offsetHeight - 8) + 'px';
    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
}

/**
 * إخفاء التلميح
 */
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

/**
 * تهيئة القائمة للهواتف المحمولة
 */
function initMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('toggleBtn');
    
    // إضافة overlay للهواتف المحمولة
    const overlay = document.createElement('div');
    overlay.className = 'mobile-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    `;
    document.body.appendChild(overlay);
    
    // معالجة النقر على زر القائمة في الهواتف المحمولة
    if (window.innerWidth <= 768) {
        toggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
            overlay.style.display = sidebar.classList.contains('mobile-open') ? 'block' : 'none';
        });
        
        // إغلاق القائمة عند النقر على الـ overlay
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('mobile-open');
            overlay.style.display = 'none';
        });
    }
    
    // معالجة تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('mobile-open');
            overlay.style.display = 'none';
        }
    });
}

/**
 * تهيئة الإشعارات
 */
function initNotifications() {
    const notificationBtn = document.querySelector('.action-btn[title="الإشعارات"]');
    
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            showNotifications();
        });
    }
}

/**
 * عرض الإشعارات
 */
function showNotifications() {
    // يمكن تطوير نافذة الإشعارات هنا
    alert('سيتم تطوير نظام الإشعارات قريباً');
}

/**
 * تحميل بيانات لوحة التحكم
 */
function loadDashboardData() {
    // تحديث الإحصائيات
    updateStatistics();
    
    // تحديث الرسوم البيانية
    updateCharts();
    
    // تحديث الأنشطة الأخيرة
    updateRecentActivities();
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics() {
    // محاكاة تحديث الإحصائيات
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
        animateNumber(stat, 0, currentValue, 2000);
    });
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const originalText = element.textContent;
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        
        // الحفاظ على تنسيق النص الأصلي
        if (originalText.includes('ر.س')) {
            element.textContent = current.toLocaleString('ar-SA') + ' ر.س';
        } else {
            element.textContent = current.toLocaleString('ar-SA');
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * تحديث الرسوم البيانية
 */
function updateCharts() {
    // يمكن إضافة مكتبة رسوم بيانية مثل Chart.js هنا
    console.log('تحديث الرسوم البيانية...');
}

/**
 * تحديث الأنشطة الأخيرة
 */
function updateRecentActivities() {
    // محاكاة تحديث الأنشطة
    const activityTimes = document.querySelectorAll('.activity-time');
    
    activityTimes.forEach(time => {
        // تحديث الأوقات النسبية
        updateRelativeTime(time);
    });
}

/**
 * تحديث الوقت النسبي
 */
function updateRelativeTime(element) {
    // محاكاة تحديث الوقت النسبي
    const times = ['منذ دقيقة', 'منذ 5 دقائق', 'منذ 30 دقيقة', 'منذ ساعة', 'منذ ساعتين'];
    const randomTime = times[Math.floor(Math.random() * times.length)];
    // element.textContent = randomTime;
}

/**
 * دالة مساعدة لإرسال طلبات AJAX
 */
function sendAjaxRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(method, url);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    resolve(xhr.responseText);
                }
            } else {
                reject(new Error('خطأ في الطلب: ' + xhr.status));
            }
        };
        
        xhr.onerror = function() {
            reject(new Error('خطأ في الشبكة'));
        };
        
        xhr.send(data ? JSON.stringify(data) : null);
    });
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#cce5ff'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#004085'};
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        max-width: 400px;
        text-align: center;
    `;
    alert.textContent = message;
    
    document.body.appendChild(alert);
    
    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

/**
 * تحديث دوري لبيانات لوحة التحكم
 */
setInterval(() => {
    if (document.visibilityState === 'visible') {
        updateRecentActivities();
    }
}, 60000); // كل دقيقة

/**
 * معالجة الأخطاء العامة
 */
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
});

/**
 * معالجة الأخطاء غير المعالجة في الـ Promises
 */
window.addEventListener('unhandledrejection', function(e) {
    console.error('خطأ في Promise:', e.reason);
    e.preventDefault();
});
