<?php
/**
 * CustomerController
 * تحكم في إدارة العملاء
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class CustomerController extends Controller
{
    private $auth;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // التحقق من تسجيل الدخول
        if (!$this->auth->check()) {
            $this->redirect('/login');
        }
    }

    /**
     * عرض قائمة العملاء
     */
    public function index()
    {
        $customers = $this->db->select("
            SELECT * FROM customers 
            ORDER BY created_at DESC
        ");

        $this->render('sales/customers/index', [
            'customers' => $customers,
            'title' => 'إدارة العملاء'
        ]);
    }

    /**
     * عرض نموذج إضافة عميل جديد
     */
    public function create()
    {
        $this->render('sales/customers/create', [
            'title' => 'إضافة عميل جديد'
        ]);
    }

    /**
     * حفظ عميل جديد
     */
    public function store()
    {
        try {
            // التحقق من البيانات
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $customerType = $_POST['customer_type'] ?? 'individual';

            if (empty($name)) {
                throw new Exception('اسم العميل مطلوب');
            }

            // إنشاء كود العميل
            $lastCustomer = $this->db->selectOne("SELECT customer_code FROM customers ORDER BY id DESC LIMIT 1");
            $nextNumber = 1;
            if ($lastCustomer) {
                $lastNumber = intval(substr($lastCustomer['customer_code'], 4));
                $nextNumber = $lastNumber + 1;
            }
            $customerCode = 'CUST' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);

            // إنشاء العميل
            $customerId = $this->db->insert('customers', [
                'customer_code' => $customerCode,
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address,
                'customer_type' => $customerType,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->jsonResponse(['success' => true, 'message' => 'تم إنشاء العميل بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * عرض تفاصيل عميل
     */
    public function show($id)
    {
        $customer = $this->db->selectOne("SELECT * FROM customers WHERE id = ?", [$id]);
        
        if (!$customer) {
            $this->showError('العميل غير موجود');
            return;
        }

        // جلب الطلبات الأخيرة
        $orders = $this->db->select("
            SELECT * FROM sales_orders 
            WHERE customer_id = ? 
            ORDER BY order_date DESC 
            LIMIT 10
        ", [$id]);

        // جلب الفواتير
        $invoices = $this->db->select("
            SELECT * FROM invoices 
            WHERE customer_id = ? 
            ORDER BY invoice_date DESC 
            LIMIT 10
        ", [$id]);

        $this->render('sales/customers/show', [
            'customer' => $customer,
            'orders' => $orders,
            'invoices' => $invoices,
            'title' => 'تفاصيل العميل'
        ]);
    }

    /**
     * عرض نموذج تعديل عميل
     */
    public function edit($id)
    {
        $customer = $this->db->selectOne("SELECT * FROM customers WHERE id = ?", [$id]);

        if (!$customer) {
            $this->showError('العميل غير موجود');
            return;
        }

        $this->render('sales/customers/edit', [
            'customer' => $customer,
            'title' => 'تعديل العميل'
        ]);
    }

    /**
     * تحديث بيانات عميل
     */
    public function update($id)
    {
        try {
            $customer = $this->db->selectOne("SELECT * FROM customers WHERE id = ?", [$id]);
            if (!$customer) {
                throw new Exception('العميل غير موجود');
            }

            // التحقق من البيانات
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $customerType = $_POST['customer_type'] ?? 'individual';
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            if (empty($name)) {
                throw new Exception('اسم العميل مطلوب');
            }

            // تحديث البيانات
            $this->db->update('customers', [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address,
                'customer_type' => $customerType,
                'is_active' => $isActive,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $id]);

            $this->jsonResponse(['success' => true, 'message' => 'تم تحديث العميل بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * حذف عميل
     */
    public function delete($id)
    {
        try {
            $customer = $this->db->selectOne("SELECT * FROM customers WHERE id = ?", [$id]);
            if (!$customer) {
                throw new Exception('العميل غير موجود');
            }

            // التحقق من وجود طلبات أو فواتير
            $hasOrders = $this->db->selectOne("SELECT id FROM sales_orders WHERE customer_id = ?", [$id]);
            $hasInvoices = $this->db->selectOne("SELECT id FROM invoices WHERE customer_id = ?", [$id]);

            if ($hasOrders || $hasInvoices) {
                throw new Exception('لا يمكن حذف العميل لوجود طلبات أو فواتير مرتبطة به');
            }

            // حذف العميل
            $this->db->delete('customers', ['id' => $id]);

            $this->jsonResponse(['success' => true, 'message' => 'تم حذف العميل بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * البحث في العملاء (للـ API)
     */
    public function search()
    {
        $query = $_GET['q'] ?? '';
        
        if (strlen($query) < 2) {
            $this->jsonResponse(['customers' => []]);
            return;
        }

        $customers = $this->db->select("
            SELECT id, customer_code, name, email, phone 
            FROM customers 
            WHERE (name LIKE ? OR customer_code LIKE ? OR email LIKE ?) 
            AND is_active = 1 
            ORDER BY name 
            LIMIT 20
        ", ["%{$query}%", "%{$query}%", "%{$query}%"]);

        $this->jsonResponse(['customers' => $customers]);
    }
}
?>
