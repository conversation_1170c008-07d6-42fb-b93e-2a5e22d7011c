<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - SeaSystem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تسجيل الدخول - SeaSystem</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="remember"> تذكرني
                </label>
            </div>
            
            <button type="submit">تسجيل الدخول</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>📋 معلومات الاختبار:</h3>
        <ul>
            <li><strong>الخادم:</strong> http://localhost:8000</li>
            <li><strong>اسم المستخدم:</strong> admin</li>
            <li><strong>كلمة المرور:</strong> admin123</li>
            <li><strong>طريقة الإرسال:</strong> POST</li>
        </ul>
        
        <h3>🔗 روابط مفيدة:</h3>
        <ul>
            <li><a href="http://localhost:8000" target="_blank">صفحة تسجيل الدخول</a></li>
            <li><a href="http://localhost:8000/dashboard" target="_blank">لوحة التحكم</a></li>
        </ul>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const formData = new FormData(this);
            
            try {
                // أولاً، احصل على رمز CSRF من صفحة تسجيل الدخول
                const loginPageResponse = await fetch('http://localhost:8000');
                const loginPageText = await loginPageResponse.text();
                
                // استخراج رمز CSRF من HTML
                const csrfMatch = loginPageText.match(/name="csrf_token" value="([^"]+)"/);
                const csrfToken = csrfMatch ? csrfMatch[1] : '';
                
                if (csrfToken) {
                    formData.append('csrf_token', csrfToken);
                }
                
                // إرسال طلب تسجيل الدخول
                const response = await fetch('http://localhost:8000/login', {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                
                resultDiv.style.display = 'block';
                
                if (response.ok) {
                    if (responseText.includes('لوحة التحكم') || responseText.includes('dashboard')) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <h4>✅ نجح تسجيل الدخول!</h4>
                            <p>تم تسجيل الدخول بنجاح وتم التوجيه إلى لوحة التحكم.</p>
                            <a href="http://localhost:8000/dashboard" target="_blank">فتح لوحة التحكم</a>
                        `;
                    } else if (responseText.includes('تسجيل الدخول')) {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `
                            <h4>❌ فشل تسجيل الدخول</h4>
                            <p>تم إرجاعك إلى صفحة تسجيل الدخول. تحقق من البيانات.</p>
                        `;
                    } else {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <h4>📄 استجابة الخادم</h4>
                            <p>الحالة: ${response.status} ${response.statusText}</p>
                            <details>
                                <summary>عرض المحتوى</summary>
                                <pre style="max-height: 200px; overflow: auto; background: #f8f9fa; padding: 10px; margin-top: 10px;">${responseText.substring(0, 1000)}...</pre>
                            </details>
                        `;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ خطأ في الخادم</h4>
                        <p>الحالة: ${response.status} ${response.statusText}</p>
                    `;
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ خطأ في الاتصال</h4>
                    <p>${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
