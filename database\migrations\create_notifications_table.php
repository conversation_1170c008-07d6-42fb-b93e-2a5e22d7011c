<?php
/**
 * إنشاء جدول الإشعارات والجداول المتقدمة
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📢 إنشاء جداول الإشعارات والميزات المتقدمة...\n";
    echo "===============================================\n\n";

    // جدول الإشعارات
    $sql = "
    CREATE TABLE IF NOT EXISTS notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error', 'reminder', 'welcome') DEFAULT 'info',
        data JSON,
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول notifications\n";

    // جدول إعدادات النظام
    $sql = "
    CREATE TABLE IF NOT EXISTS system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        updated_by INT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_setting_key (setting_key),
        INDEX idx_is_public (is_public)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول system_settings\n";

    // جدول API tokens
    $sql = "
    CREATE TABLE IF NOT EXISTS api_tokens (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        token_name VARCHAR(100) NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        permissions JSON,
        last_used_at TIMESTAMP NULL,
        expires_at TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_token_hash (token_hash),
        INDEX idx_user_id (user_id),
        INDEX idx_is_active (is_active)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول api_tokens\n";

    // جدول ملفات النظام
    $sql = "
    CREATE TABLE IF NOT EXISTS system_files (
        id INT PRIMARY KEY AUTO_INCREMENT,
        original_name VARCHAR(255) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100),
        file_type ENUM('image', 'document', 'spreadsheet', 'archive', 'other') DEFAULT 'other',
        uploaded_by INT,
        entity_type VARCHAR(50),
        entity_id INT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_entity (entity_type, entity_id),
        INDEX idx_uploaded_by (uploaded_by),
        INDEX idx_file_type (file_type)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول system_files\n";

    // جدول النسخ الاحتياطي
    $sql = "
    CREATE TABLE IF NOT EXISTS backups (
        id INT PRIMARY KEY AUTO_INCREMENT,
        backup_name VARCHAR(255) NOT NULL,
        backup_type ENUM('full', 'database', 'files') NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_backup_type (backup_type),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول backups\n";

    // جدول سجل الأخطاء
    $sql = "
    CREATE TABLE IF NOT EXISTS error_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        error_level ENUM('emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug') NOT NULL,
        error_message TEXT NOT NULL,
        error_context JSON,
        file_path VARCHAR(500),
        line_number INT,
        user_id INT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_error_level (error_level),
        INDEX idx_created_at (created_at),
        INDEX idx_user_id (user_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول error_logs\n";

    // جدول جلسات المستخدمين
    $sql = "
    CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(128) PRIMARY KEY,
        user_id INT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        payload TEXT,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_last_activity (last_activity)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول user_sessions\n";

    // إدراج إعدادات النظام الافتراضية
    $defaultSettings = [
        ['company_name', 'شركة SeaSystem', 'string', 'اسم الشركة', 1],
        ['company_email', '<EMAIL>', 'string', 'البريد الإلكتروني للشركة', 1],
        ['company_phone', '+966123456789', 'string', 'هاتف الشركة', 1],
        ['company_address', 'الرياض، المملكة العربية السعودية', 'string', 'عنوان الشركة', 1],
        ['default_currency', 'SAR', 'string', 'العملة الافتراضية', 1],
        ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', 0],
        ['date_format', 'Y-m-d', 'string', 'تنسيق التاريخ', 0],
        ['items_per_page', '20', 'number', 'عدد العناصر في الصفحة', 0],
        ['enable_notifications', 'true', 'boolean', 'تفعيل الإشعارات', 0],
        ['enable_email_notifications', 'true', 'boolean', 'تفعيل إشعارات البريد', 0],
        ['low_stock_threshold', '10', 'number', 'حد تنبيه المخزون المنخفض', 0],
        ['backup_retention_days', '30', 'number', 'مدة الاحتفاظ بالنسخ الاحتياطية', 0],
        ['session_timeout', '120', 'number', 'انتهاء صلاحية الجلسة (دقيقة)', 0],
        ['max_login_attempts', '5', 'number', 'عدد محاولات تسجيل الدخول القصوى', 0],
        ['enable_api', 'true', 'boolean', 'تفعيل واجهة برمجة التطبيقات', 0]
    ];

    foreach ($defaultSettings as $setting) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO system_settings 
            (setting_key, setting_value, setting_type, description, is_public) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute($setting);
    }
    echo "✅ تم إدراج الإعدادات الافتراضية\n";

    // إدراج بعض الإشعارات التجريبية
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO notifications 
        (user_id, title, message, type, created_at) 
        VALUES 
        (1, 'مرحباً بك في النظام', 'تم تحديث النظام بنجاح وإضافة ميزات جديدة', 'welcome', NOW()),
        (1, 'تحديث النظام', 'تم إضافة نظام الإشعارات المتقدم', 'info', NOW()),
        (1, 'ميزة جديدة', 'تم إضافة واجهة برمجة التطبيقات (API)', 'success', NOW())
    ");
    $stmt->execute();
    echo "✅ تم إدراج إشعارات تجريبية\n";

    echo "\n🎉 تم إنشاء جميع الجداول المتقدمة بنجاح!\n";
    echo "\n📊 ملخص الجداول الجديدة:\n";
    echo "- notifications: نظام الإشعارات\n";
    echo "- system_settings: إعدادات النظام\n";
    echo "- api_tokens: رموز API\n";
    echo "- system_files: إدارة الملفات\n";
    echo "- backups: النسخ الاحتياطي\n";
    echo "- error_logs: سجل الأخطاء\n";
    echo "- user_sessions: جلسات المستخدمين\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
