<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - SeaSystem</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border: none;
            margin-bottom: 20px;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }
        
        .bg-gradient-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .bg-gradient-success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .bg-gradient-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .bg-gradient-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        
        .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stats-title {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .stats-change {
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .header-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 30px;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="dashboard-container">
            <!-- الترحيب -->
            <div class="welcome-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h2 mb-2">مرحباً بك في نظام SeaSystem</h1>
                        <p class="mb-0">لوحة التحكم الرئيسية لإدارة نظام تخطيط موارد المؤسسة</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="bi bi-speedometer2" style="font-size: 4rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
            
            <!-- العنوان -->
            <h2 class="header-title">
                <i class="bi bi-graph-up me-2"></i>
                الإحصائيات السريعة
            </h2>
            
            <!-- البطاقات الإحصائية -->
            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon bg-gradient-primary">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-value text-primary">1,234</div>
                        <div class="stats-title">إجمالي العملاء</div>
                        <div class="stats-change text-success">
                            <i class="bi bi-arrow-up"></i> +12% من الشهر الماضي
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon bg-gradient-success">
                            <i class="bi bi-box"></i>
                        </div>
                        <div class="stats-value text-success">567</div>
                        <div class="stats-title">المنتجات النشطة</div>
                        <div class="stats-change text-info">
                            <i class="bi bi-plus-circle"></i> +8 منتجات جديدة
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon bg-gradient-warning">
                            <i class="bi bi-cart"></i>
                        </div>
                        <div class="stats-value text-warning">89</div>
                        <div class="stats-title">طلبات اليوم</div>
                        <div class="stats-change text-warning">
                            <i class="bi bi-clock"></i> 15 قيد المعالجة
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon bg-gradient-info">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stats-value text-info">45,680</div>
                        <div class="stats-title">مبيعات اليوم (ر.س)</div>
                        <div class="stats-change text-success">
                            <i class="bi bi-arrow-up"></i> +25% من أمس
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الإجراءات السريعة -->
            <div class="row mt-4">
                <div class="col-12">
                    <h3 class="h5 mb-3">
                        <i class="bi bi-lightning me-2"></i>
                        الإجراءات السريعة
                    </h3>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-custom">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة عميل جديد
                        </button>
                        <button class="btn btn-custom">
                            <i class="bi bi-box-seam me-2"></i>
                            إضافة منتج
                        </button>
                        <button class="btn btn-custom">
                            <i class="bi bi-cart-plus me-2"></i>
                            طلب جديد
                        </button>
                        <button class="btn btn-custom">
                            <i class="bi bi-graph-up me-2"></i>
                            عرض التقارير
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>
                            حالة النظام
                        </h6>
                        <p class="mb-0">
                            ✅ النظام يعمل بشكل طبيعي<br>
                            📊 آخر تحديث للبيانات: <?= date('Y-m-d H:i:s') ?><br>
                            🔗 <a href="/test.php" class="alert-link">اختبار النظام</a> |
                            <a href="/" class="alert-link">النظام الكامل</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تأثيرات بسيطة -->
    <script>
        // تحريك العدادات
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.stats-value');
            
            counters.forEach(counter => {
                const target = parseInt(counter.textContent.replace(/,/g, ''));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    if (target > 1000) {
                        counter.textContent = new Intl.NumberFormat('ar-SA').format(Math.floor(current));
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 16);
            });
        });
    </script>
</body>
</html>
