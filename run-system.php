<?php
/**
 * SeaSystem Final Launch Script
 * سكريبت التشغيل النهائي لنظام SeaSystem
 */

echo "🚀 تشغيل نظام SeaSystem ERP\n";
echo "============================\n\n";

// التحقق من المتطلبات
echo "📋 التحقق من المتطلبات...\n";

$requirements = [
    'PHP >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'MBString' => extension_loaded('mbstring'),
    'JSON' => extension_loaded('json'),
    'OpenSSL' => extension_loaded('openssl'),
];

$allGood = true;
foreach ($requirements as $req => $met) {
    echo "   " . ($met ? '✅' : '❌') . " {$req}\n";
    if (!$met) $allGood = false;
}

if (!$allGood) {
    echo "\n❌ بعض المتطلبات غير متوفرة!\n";
    exit(1);
}

echo "\n✅ جميع المتطلبات متوفرة!\n\n";

// التحقق من قاعدة البيانات
echo "🗄️  التحقق من قاعدة البيانات...\n";

try {
    require_once 'core/Environment.php';
    Environment::load();
    
    $host = Environment::get('DB_HOST', 'localhost');
    $port = Environment::get('DB_PORT', 3306);
    $dbname = Environment::get('DB_NAME', 'R1');
    $username = Environment::get('DB_USERNAME', 'root');
    $password = Environment::get('DB_PASSWORD', '');
    
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "   ✅ الاتصال بقاعدة البيانات نجح\n";
    
    // التحقق من الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "   ✅ عدد الجداول: " . count($tables) . "\n";
    
    // التحقق من المستخدم التجريبي
    $userCheck = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'")->fetchColumn();
    echo "   ✅ المستخدم التجريبي: " . ($userCheck > 0 ? 'موجود' : 'غير موجود') . "\n";
    
} catch (Exception $e) {
    echo "   ❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
    echo "\n💡 قم بتشغيل: php quick-setup.php\n";
    exit(1);
}

echo "\n✅ قاعدة البيانات جاهزة!\n\n";

// التحقق من الملفات الأساسية
echo "📄 التحقق من الملفات الأساسية...\n";

$coreFiles = [
    'core/Environment.php',
    'core/Database.php',
    'core/Auth.php',
    'core/Session.php',
    'core/RBAC.php',
    'public/index.php',
    'modules/dashboard/views/index.php',
    'modules/users/views/auth/login.php',
    'assets/css/dashboard.css',
    'assets/js/dashboard.js'
];

foreach ($coreFiles as $file) {
    $exists = file_exists($file);
    echo "   " . ($exists ? '✅' : '❌') . " {$file}\n";
    if (!$exists) $allGood = false;
}

if (!$allGood) {
    echo "\n❌ بعض الملفات مفقودة!\n";
    exit(1);
}

echo "\n✅ جميع الملفات موجودة!\n\n";

// معلومات النظام
echo "ℹ️  معلومات النظام:\n";
echo "   📍 مسار المشروع: " . __DIR__ . "\n";
echo "   🐘 إصدار PHP: " . PHP_VERSION . "\n";
echo "   🗄️  قاعدة البيانات: {$dbname} على {$host}:{$port}\n";
echo "   👤 المستخدم التجريبي: admin\n";
echo "   🔑 كلمة المرور: admin123\n\n";

// بدء الخادم
echo "🌐 بدء خادم التطوير...\n";
echo "   🔗 الرابط: http://localhost:8000\n";
echo "   📊 لوحة التحكم: http://localhost:8000/dashboard\n";
echo "   🛑 للإيقاف: اضغط Ctrl+C\n\n";

echo "🎉 النظام جاهز للاستخدام!\n";
echo "============================\n\n";

// تشغيل الخادم
echo "بدء الخادم...\n";
$command = "php -S localhost:8000 -t public";

if (PHP_OS_FAMILY === 'Windows') {
    // في Windows
    echo "تشغيل الأمر: {$command}\n";
    echo "افتح متصفح جديد وتوجه إلى: http://localhost:8000\n\n";
    
    // فتح المتصفح تلقائياً
    $openBrowser = true;
    if ($openBrowser) {
        echo "فتح المتصفح تلقائياً...\n";
        sleep(2);
        exec('start http://localhost:8000');
    }
    
    // تشغيل الخادم
    passthru($command);
} else {
    // في Linux/Mac
    echo "تشغيل الأمر: {$command}\n";
    echo "افتح متصفح جديد وتوجه إلى: http://localhost:8000\n\n";
    
    // فتح المتصفح تلقائياً
    $openBrowser = true;
    if ($openBrowser) {
        echo "فتح المتصفح تلقائياً...\n";
        sleep(2);
        exec('xdg-open http://localhost:8000 2>/dev/null || open http://localhost:8000 2>/dev/null');
    }
    
    // تشغيل الخادم
    passthru($command);
}
?>
