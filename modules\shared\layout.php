<?php
/**
 * القالب الأساسي الموحد
 * Unified Layout Template
 */

$pageTitle = $title ?? 'SeaSystem ERP';
$currentUser = $user ?? $auth->user();
$currentRoute = $_SERVER['REQUEST_URI'] ?? '/';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    <title><?= htmlspecialchars($pageTitle) ?> - SeaSystem ERP</title>
    
    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- المكتبات الخارجية -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- الأنماط المخصصة -->
    <link href="/assets/css/theme.css" rel="stylesheet">
    
    <!-- أنماط إضافية للصفحة -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?= $css ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        .main-wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar-wrapper {
            width: 280px;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar-collapsed .sidebar-wrapper {
            width: 80px;
        }
        
        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .top-navbar {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 2rem;
            box-shadow: var(--shadow-sm);
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
            background: var(--gray-50);
        }
        
        .sidebar-toggle {
            display: none;
        }
        
        @media (max-width: 768px) {
            .sidebar-wrapper {
                position: fixed;
                top: 0;
                right: -280px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar-open .sidebar-wrapper {
                right: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body class="<?= isset($bodyClass) ? $bodyClass : '' ?>">
    <div class="main-wrapper" id="mainWrapper">
        <!-- الشريط الجانبي -->
        <div class="sidebar-wrapper">
            <div class="sidebar">
                <!-- رأس الشريط الجانبي -->
                <div class="sidebar-header p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="logo d-flex align-items-center">
                            <i class="bi bi-water fs-2 me-2"></i>
                            <span class="logo-text fs-4 fw-bold">SeaSystem</span>
                        </div>
                        <button class="btn btn-link text-white d-md-none" onclick="toggleSidebar()">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                
                <!-- قائمة التنقل -->
                <nav class="sidebar-nav px-3">
                    <?php include 'navigation.php'; ?>
                </nav>
                
                <!-- معلومات المستخدم -->
                <div class="user-info p-3 mt-auto">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            <div class="bg-white bg-opacity-20 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="bi bi-person text-white"></i>
                            </div>
                        </div>
                        <div class="user-details flex-grow-1">
                            <div class="user-name fw-semibold">
                                <?= htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']) ?>
                            </div>
                            <div class="user-role small opacity-75">
                                <?= htmlspecialchars($currentUser['username']) ?>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link text-white p-0" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i> الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear me-2"></i> الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="content-wrapper">
            <!-- الشريط العلوي -->
            <div class="top-navbar">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-link sidebar-toggle me-3" onclick="toggleSidebar()">
                            <i class="bi bi-list"></i>
                        </button>
                        
                        <!-- مسار التنقل -->
                        <?php if (isset($breadcrumbs)): ?>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                        <?php if ($index === count($breadcrumbs) - 1): ?>
                                            <li class="breadcrumb-item active"><?= htmlspecialchars($crumb['title']) ?></li>
                                        <?php else: ?>
                                            <li class="breadcrumb-item">
                                                <a href="<?= htmlspecialchars($crumb['url']) ?>"><?= htmlspecialchars($crumb['title']) ?></a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ol>
                            </nav>
                        <?php else: ?>
                            <h1 class="h4 mb-0"><?= htmlspecialchars($pageTitle) ?></h1>
                        <?php endif; ?>
                    </div>
                    
                    <!-- أدوات الشريط العلوي -->
                    <div class="d-flex align-items-center">
                        <!-- البحث السريع -->
                        <div class="search-box me-3">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="البحث السريع..." id="quickSearch">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- الإشعارات -->
                        <div class="dropdown me-3">
                            <button class="btn btn-link position-relative" data-bs-toggle="dropdown">
                                <i class="bi bi-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                </span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">الإشعارات</h6></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-info-circle me-2"></i> إشعار جديد</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="/notifications">عرض جميع الإشعارات</a></li>
                            </ul>
                        </div>
                        
                        <!-- ملف المستخدم -->
                        <div class="dropdown">
                            <button class="btn btn-link d-flex align-items-center" data-bs-toggle="dropdown">
                                <div class="user-avatar me-2">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <span class="text-white small fw-bold">
                                            <?= strtoupper(substr($currentUser['first_name'], 0, 1)) ?>
                                        </span>
                                    </div>
                                </div>
                                <span class="d-none d-md-inline"><?= htmlspecialchars($currentUser['first_name']) ?></span>
                                <i class="bi bi-chevron-down ms-1"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i> الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear me-2"></i> الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المحتوى -->
            <main class="main-content">
                <?php if (isset($content)): ?>
                    <?= $content ?>
                <?php else: ?>
                    <!-- المحتوى الافتراضي -->
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <!-- سيتم إدراج المحتوى هنا -->
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <!-- حاوي الإشعارات -->
    <div id="notifications-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1060;"></div>
    
    <!-- المكتبات الخارجية -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- المكتبة الأساسية -->
    <script src="/assets/js/seasystem.js"></script>
    
    <!-- سكريبت إضافية للصفحة -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?= $js ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // تبديل الشريط الجانبي
        function toggleSidebar() {
            document.getElementById('mainWrapper').classList.toggle('sidebar-open');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه في الشاشات الصغيرة
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.sidebar-wrapper');
                const toggle = document.querySelector('.sidebar-toggle');
                
                if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                    document.getElementById('mainWrapper').classList.remove('sidebar-open');
                }
            }
        });
        
        // البحث السريع
        document.getElementById('quickSearch').addEventListener('input', function(e) {
            const query = e.target.value;
            if (query.length > 2) {
                // تنفيذ البحث السريع
                console.log('البحث عن:', query);
            }
        });
    </script>
    
    <!-- سكريبت مخصص للصفحة -->
    <?php if (isset($pageScript)): ?>
        <script><?= $pageScript ?></script>
    <?php endif; ?>
</body>
</html>
