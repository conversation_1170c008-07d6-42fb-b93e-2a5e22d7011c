<?php
/**
 * صفحة تشخيص مشاكل النظام
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 تشخيص مشاكل النظام</h1>";

// اختبار تحميل الملفات الأساسية
echo "<h2>📁 اختبار تحميل الملفات:</h2>";

$rootPath = dirname(__DIR__);
echo "<p>المسار الجذر: {$rootPath}</p>";

// قائمة الملفات المطلوبة
$requiredFiles = [
    '/core/Environment.php',
    '/core/helpers.php', 
    '/core/Database.php',
    '/core/Session.php',
    '/core/Auth.php',
    '/core/RBAC.php',
    '/core/Middleware.php',
    '/core/Logger.php',
    '/config/app.php',
    '/modules/dashboard/views/index.php'
];

echo "<ul>";
foreach ($requiredFiles as $file) {
    $fullPath = $rootPath . $file;
    $exists = file_exists($fullPath);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file}</li>";
    
    if (!$exists) {
        echo "<li style='color: red; margin-left: 20px;'>المسار الكامل: {$fullPath}</li>";
    }
}
echo "</ul>";

// اختبار تحميل Environment
echo "<h2>🌍 اختبار Environment:</h2>";
try {
    if (file_exists($rootPath . '/core/Environment.php')) {
        require_once $rootPath . '/core/Environment.php';
        echo "<p>✅ تم تحميل Environment بنجاح</p>";
        
        // اختبار تحميل متغيرات البيئة
        Environment::load();
        echo "<p>✅ تم تحميل متغيرات البيئة</p>";
        
        // عرض بعض المتغيرات
        echo "<ul>";
        echo "<li>DB_HOST: " . Environment::get('DB_HOST', 'غير محدد') . "</li>";
        echo "<li>DB_NAME: " . Environment::get('DB_NAME', 'غير محدد') . "</li>";
        echo "<li>APP_NAME: " . Environment::get('APP_NAME', 'غير محدد') . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p>❌ ملف Environment غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل Environment: " . $e->getMessage() . "</p>";
}

// اختبار تحميل helpers
echo "<h2>🛠️ اختبار helpers:</h2>";
try {
    if (file_exists($rootPath . '/core/helpers.php')) {
        require_once $rootPath . '/core/helpers.php';
        echo "<p>✅ تم تحميل helpers بنجاح</p>";
        
        // اختبار دالة config
        if (function_exists('config')) {
            echo "<p>✅ دالة config متوفرة</p>";
        } else {
            echo "<p>❌ دالة config غير متوفرة</p>";
        }
        
    } else {
        echo "<p>❌ ملف helpers غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل helpers: " . $e->getMessage() . "</p>";
}

// اختبار تحميل Database
echo "<h2>🗄️ اختبار Database:</h2>";
try {
    if (file_exists($rootPath . '/core/Database.php')) {
        require_once $rootPath . '/core/Database.php';
        echo "<p>✅ تم تحميل Database class بنجاح</p>";
        
        // محاولة إنشاء اتصال
        try {
            $db = Database::getInstance();
            echo "<p>✅ تم إنشاء اتصال قاعدة البيانات بنجاح</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>❌ ملف Database غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل Database: " . $e->getMessage() . "</p>";
}

// اختبار محاكاة لوحة التحكم
echo "<h2>🎛️ اختبار لوحة التحكم:</h2>";
try {
    // محاكاة البيانات
    $currentUser = [
        'id' => 1,
        'username' => 'admin',
        'first_name' => 'مدير',
        'last_name' => 'النظام',
        'email' => '<EMAIL>'
    ];
    
    $stats = [
        [
            'title' => 'إجمالي العملاء',
            'value' => '1,234',
            'icon' => 'bi-people',
            'color' => 'primary',
            'change' => '+12%',
            'url' => '/customers'
        ],
        [
            'title' => 'المنتجات',
            'value' => '567',
            'icon' => 'bi-box',
            'color' => 'success',
            'change' => '+5%',
            'url' => '/products'
        ]
    ];
    
    echo "<p>✅ تم إعداد البيانات الوهمية</p>";
    echo "<p>المستخدم: " . $currentUser['first_name'] . " " . $currentUser['last_name'] . "</p>";
    echo "<p>عدد الإحصائيات: " . count($stats) . "</p>";
    
    // اختبار تحميل ملف لوحة التحكم
    $dashboardFile = $rootPath . '/modules/dashboard/views/index.php';
    if (file_exists($dashboardFile)) {
        echo "<p>✅ ملف لوحة التحكم موجود</p>";
        
        // محاولة تحميل المحتوى
        ob_start();
        try {
            include $dashboardFile;
            $content = ob_get_contents();
            ob_end_clean();
            
            if (!empty($content)) {
                echo "<p>✅ تم تحميل محتوى لوحة التحكم بنجاح</p>";
                echo "<p>حجم المحتوى: " . strlen($content) . " بايت</p>";
            } else {
                echo "<p>⚠️ محتوى لوحة التحكم فارغ</p>";
            }
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p>❌ خطأ في تحميل لوحة التحكم: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>❌ ملف لوحة التحكم غير موجود: {$dashboardFile}</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في اختبار لوحة التحكم: " . $e->getMessage() . "</p>";
}

// اختبار الجلسة
echo "<h2>🔐 اختبار الجلسة:</h2>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p>✅ تم بدء الجلسة بنجاح</p>";
    echo "<p>معرف الجلسة: " . session_id() . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في الجلسة: " . $e->getMessage() . "</p>";
}

// معلومات PHP
echo "<h2>🐘 معلومات PHP:</h2>";
echo "<ul>";
echo "<li>إصدار PHP: " . PHP_VERSION . "</li>";
echo "<li>الذاكرة المتاحة: " . ini_get('memory_limit') . "</li>";
echo "<li>الحد الأقصى لوقت التنفيذ: " . ini_get('max_execution_time') . " ثانية</li>";
echo "<li>عرض الأخطاء: " . (ini_get('display_errors') ? 'مفعل' : 'معطل') . "</li>";
echo "</ul>";

// روابط مفيدة
echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<ul>";
echo "<li><a href='/test.php'>اختبار شامل</a></li>";
echo "<li><a href='/dashboard-simple.php'>لوحة تحكم مبسطة</a></li>";
echo "<li><a href='/dashboard'>لوحة التحكم الأصلية</a></li>";
echo "<li><a href='/'>الصفحة الرئيسية</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>تاريخ التشخيص:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1, h2 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 10px 0;
}

li {
    margin: 8px 0;
    padding: 5px;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 5px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>
