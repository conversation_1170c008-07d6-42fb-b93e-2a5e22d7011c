# SeaSystem ERP - نظام تخطيط موارد المؤسسات

![SeaSystem Logo](assets/images/logo.png)

## نظرة عامة

SeaSystem هو نظام ERP (تخطيط موارد المؤسسات) متكامل ومتطور، مصمم خصيصاً للشركات العربية. تم تطويره باستخدام PHP وMySQL مع التركيز على الأمان، الأداء، والقابلية للتوسع.

### المميزات الرئيسية

- ✅ **نظام أمان متقدم**: مصادقة متعددة المستويات مع نظام RBAC
- ✅ **واجهة عربية**: دعم كامل للغة العربية مع RTL
- ✅ **معمارية معيارية**: وحدات منفصلة قابلة للتخصيص
- ✅ **أداء عالي**: تحسينات قاعدة البيانات والكاش
- ✅ **تقارير شاملة**: تقارير مالية ومحاسبية متقدمة
- ✅ **API متكامل**: واجهات برمجية للتكامل مع الأنظمة الأخرى
- ✅ **نسخ احتياطي تلقائي**: حماية البيانات مع استعادة سريعة

## هيكل المشروع

```
seasystem-R1/
├── 📁 config/                 # ملفات التكوين والإعدادات
│   ├── 📄 app.php            # إعدادات التطبيق الرئيسية
│   ├── 📄 database.php       # إعدادات قاعدة البيانات
│   └── 📄 .env.example       # مثال متغيرات البيئة
├── 📁 core/                  # النواة الأساسية للنظام
│   ├── 📄 Database.php       # إدارة قاعدة البيانات (PDO)
│   ├── 📄 Auth.php          # نظام المصادقة والتحقق
│   ├── 📄 Session.php       # إدارة الجلسات الآمنة
│   ├── 📄 RBAC.php          # نظام التحكم في الوصول
│   ├── 📄 Middleware.php    # وسطاء الأمان والتحقق
│   ├── 📄 Logger.php        # نظام التسجيل والمراقبة
│   ├── 📄 Backup.php        # نظام النسخ الاحتياطي
│   └── 📄 Model.php         # النموذج الأساسي للبيانات
├── 📁 modules/              # وحدات النظام المختلفة
│   ├── 📁 hr/              # 👥 الموارد البشرية
│   │   ├── 📁 models/      # نماذج بيانات الموظفين
│   │   ├── 📁 controllers/ # متحكمات إدارة الموظفين
│   │   └── 📁 views/       # واجهات المستخدم
│   ├── 📁 finance/         # 💰 الإدارة المالية
│   │   ├── 📁 models/      # نماذج المحاسبة
│   │   ├── 📁 controllers/ # متحكمات المالية
│   │   └── 📁 views/       # واجهات التقارير المالية
│   ├── 📁 inventory/       # 📦 إدارة المخزون
│   │   ├── 📁 models/      # نماذج المنتجات والمخزون
│   │   ├── 📁 controllers/ # متحكمات المخزون
│   │   └── 📁 views/       # واجهات إدارة المخزون
│   ├── 📁 sales/          # 🛒 إدارة المبيعات
│   │   ├── 📁 models/      # نماذج العملاء والطلبات
│   │   ├── 📁 controllers/ # متحكمات المبيعات
│   │   └── 📁 views/       # واجهات المبيعات
│   └── 📁 users/          # 👤 إدارة المستخدمين
│       ├── 📁 models/      # نماذج المستخدمين والأدوار
│       ├── 📁 controllers/ # متحكمات المستخدمين
│       └── 📁 views/       # واجهات إدارة المستخدمين
├── 📁 api/                 # واجهات API للتكامل الخارجي
├── 📁 assets/             # الملفات الثابتة والموارد
│   ├── 📁 css/            # ملفات التنسيق
│   ├── 📁 js/             # ملفات JavaScript
│   └── 📁 images/         # الصور والأيقونات
├── 📁 database/           # ملفات قاعدة البيانات
│   ├── 📁 migrations/     # ملفات إنشاء الجداول
│   └── 📁 seeds/         # البيانات الأولية
├── 📁 docs/              # الوثائق والأدلة
├── 📁 logs/              # ملفات السجلات والمراقبة
├── 📁 uploads/           # الملفات المرفوعة من المستخدمين
└── 📁 public/            # المجلد العام المتاح للويب
    └── 📄 index.php      # نقطة الدخول الرئيسية
```

## المتطلبات التقنية

### متطلبات الخادم
- **PHP**: 7.4 أو أحدث (يُفضل 8.0+)
- **MySQL**: 5.7 أو أحدث (يُفضل 8.0+)
- **خادم الويب**: Apache 2.4+ أو Nginx 1.18+
- **الذاكرة**: 512MB كحد أدنى (يُفضل 2GB+)
- **مساحة القرص**: 1GB كحد أدنى

### إضافات PHP المطلوبة
```bash
php-mysql
php-pdo
php-mbstring
php-json
php-curl
php-zip
php-gd
php-xml
```

### قواعد البيانات المدعومة
- MySQL 5.7+
- MariaDB 10.3+

## دليل التثبيت السريع

### 1. تحضير البيئة

```bash
# تحميل المشروع
git clone https://github.com/seasystem/seasystem-R1.git
cd seasystem-R1

# إعداد الصلاحيات
chmod -R 755 .
chmod -R 777 logs/
chmod -R 777 uploads/
```

### 2. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE R1 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'seasystem'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON R1.* TO 'seasystem'@'localhost';
FLUSH PRIVILEGES;

-- تشغيل ملفات الترحيل
SOURCE database/migrations/001_create_database_schema.sql;
SOURCE database/seeds/001_initial_data.sql;
```

### 3. إعداد التكوين

```bash
# نسخ ملف البيئة
cp config/.env.example config/.env

# تعديل الإعدادات
nano config/.env
```

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=R1
DB_USERNAME=seasystem
DB_PASSWORD=strong_password

# إعدادات التطبيق
APP_NAME="SeaSystem ERP"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# مفتاح التشفير (32 حرف)
APP_KEY=your-32-character-secret-key-here

# إعدادات الأمان
SESSION_LIFETIME=3600
MAX_LOGIN_ATTEMPTS=5
PASSWORD_MIN_LENGTH=8
```

### 4. إعداد خادم الويب

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/seasystem-R1/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # إعدادات الأمان
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
```

## الوحدات والمميزات

### 👥 وحدة الموارد البشرية (HR)
- **إدارة الموظفين**: ملفات شخصية كاملة مع الصور والوثائق
- **الهيكل التنظيمي**: أقسام ومناصب مع التسلسل الإداري
- **الحضور والانصراف**: تتبع ساعات العمل والإجازات
- **الرواتب والمكافآت**: حساب الرواتب مع البدلات والخصومات
- **التقارير**: تقارير شاملة عن الموظفين والأداء

### 💰 وحدة الإدارة المالية (Finance)
- **دليل الحسابات**: نظام محاسبي متكامل مع التصنيفات
- **القيود المحاسبية**: إدخال وإدارة القيود اليومية
- **التقارير المالية**: ميزان المراجعة، قائمة الدخل، الميزانية
- **إدارة النقدية**: متابعة التدفقات النقدية والبنوك
- **الضرائب**: حساب وإدارة الضرائب المختلفة

### 📦 وحدة إدارة المخزون (Inventory)
- **إدارة المنتجات**: كتالوج شامل مع الصور والمواصفات
- **تتبع المخزون**: مراقبة المستويات والحركات
- **إدارة المخازن**: مخازن متعددة مع التحكم في الوصول
- **الباركود**: دعم أنظمة الباركود للتتبع السريع
- **التقارير**: تقارير المخزون والحركات والتقييم

### 🛒 وحدة إدارة المبيعات (Sales)
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **أوامر البيع**: إنشاء ومتابعة أوامر البيع
- **الفواتير**: إصدار الفواتير مع الضرائب
- **المتابعة**: تتبع حالة الطلبات والتسليم
- **التقارير**: تحليل المبيعات والأرباح

### 👤 وحدة إدارة المستخدمين (Users)
- **المستخدمين**: إدارة حسابات المستخدمين
- **الأدوار والصلاحيات**: نظام RBAC متقدم
- **سجل الأنشطة**: تتبع جميع العمليات
- **الأمان**: مصادقة متعددة العوامل (اختياري)

## الأمان والحماية

### مميزات الأمان
- 🔐 **تشفير كلمات المرور**: باستخدام bcrypt
- 🛡️ **حماية CSRF**: رموز أمان لجميع النماذج
- 🚫 **منع SQL Injection**: استخدام Prepared Statements
- 🔒 **جلسات آمنة**: إدارة متقدمة للجلسات
- 📊 **تسجيل الأنشطة**: مراقبة جميع العمليات
- 🚨 **كشف التسلل**: مراقبة المحاولات المشبوهة

### أفضل الممارسات المطبقة
- تحديث دوري لمعرفات الجلسات
- تشفير البيانات الحساسة
- التحقق من صحة جميع المدخلات
- حماية من هجمات XSS
- إعدادات أمان للـ headers

## الأداء والتحسين

### تحسينات قاعدة البيانات
- فهارس محسنة للاستعلامات السريعة
- تحسين الاستعلامات المعقدة
- تجميع البيانات للتقارير
- تنظيف دوري للبيانات القديمة

### تحسينات التطبيق
- نظام كاش متقدم
- ضغط الملفات والصور
- تحميل تدريجي للبيانات
- تحسين استهلاك الذاكرة

## النسخ الاحتياطي والاستعادة

### النسخ الاحتياطي التلقائي
```php
// تشغيل النسخ الاحتياطي
$backup = new Backup();
$result = $backup->createFullBackup();

// جدولة النسخ الاحتياطي (cron job)
0 2 * * * /usr/bin/php /path/to/seasystem/scripts/backup.php
```

### الاستعادة
```php
// استعادة نسخة احتياطية
$backup = new Backup();
$result = $backup->restoreBackup('backup_name');
```

## API والتكامل

### واجهات API المتاحة
- **المصادقة**: `/api/auth`
- **الموظفين**: `/api/employees`
- **المنتجات**: `/api/products`
- **العملاء**: `/api/customers`
- **التقارير**: `/api/reports`

### مثال على الاستخدام
```javascript
// تسجيل الدخول
const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        username: 'admin',
        password: 'password'
    })
});

// الحصول على قائمة الموظفين
const employees = await fetch('/api/employees', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
});
```

## التطوير والمساهمة

### إعداد بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/seasystem/seasystem-R1.git
cd seasystem-R1

# إعداد بيئة التطوير
cp config/.env.example config/.env
# تعديل الإعدادات للتطوير
sed -i 's/APP_ENV=production/APP_ENV=development/' config/.env
sed -i 's/APP_DEBUG=false/APP_DEBUG=true/' config/.env
```

### معايير البرمجة

#### معايير PHP
- اتباع معايير PSR-1 و PSR-12
- استخدام camelCase للمتغيرات والدوال
- استخدام PascalCase للكلاسات
- توثيق الكود باستخدام phpDoc

```php
/**
 * مثال على توثيق الدالة
 *
 * @param int $employeeId معرف الموظف
 * @param string $month الشهر
 * @return array بيانات الراتب
 * @throws Exception إذا لم يوجد الموظف
 */
public function calculateSalary($employeeId, $month)
{
    // الكود هنا
}
```

#### معايير قاعدة البيانات
- استخدام snake_case لأسماء الجداول والأعمدة
- إضافة فهارس للأعمدة المستخدمة في البحث
- استخدام Foreign Keys للحفاظ على سلامة البيانات

### هيكل الكود

#### إنشاء وحدة جديدة
```bash
# إنشاء هيكل الوحدة
mkdir -p modules/new_module/{models,controllers,views}

# إنشاء النموذج
touch modules/new_module/models/NewModel.php

# إنشاء المتحكم
touch modules/new_module/controllers/NewController.php
```

#### مثال على النموذج
```php
<?php
require_once dirname(dirname(dirname(__DIR__))) . '/core/Model.php';

class NewModel extends Model
{
    protected $table = 'new_table';
    protected $fillable = ['field1', 'field2'];

    // الدوال المخصصة هنا
}
```

## الاختبار

### اختبارات الوحدة
```php
// مثال على اختبار النموذج
class EmployeeTest
{
    public function testCreateEmployee()
    {
        $employee = new Employee([
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>'
        ]);

        $this->assertTrue($employee->save());
        $this->assertNotNull($employee->id);
    }
}
```

### اختبارات التكامل
```bash
# تشغيل الاختبارات
php tests/run_tests.php

# اختبار وحدة معينة
php tests/run_tests.php --module=hr
```

## النشر والإنتاج

### قائمة مراجعة النشر

#### الأمان
- [ ] تغيير كلمات المرور الافتراضية
- [ ] تعيين APP_ENV=production
- [ ] تعيين APP_DEBUG=false
- [ ] إنشاء مفتاح تشفير قوي
- [ ] تكوين HTTPS
- [ ] إعداد جدار الحماية

#### الأداء
- [ ] تفعيل ضغط gzip
- [ ] تحسين إعدادات PHP
- [ ] تكوين الكاش
- [ ] تحسين قاعدة البيانات
- [ ] إعداد CDN (اختياري)

#### المراقبة
- [ ] إعداد مراقبة الخادم
- [ ] تكوين تنبيهات الأخطاء
- [ ] إعداد النسخ الاحتياطي التلقائي
- [ ] مراقبة الأداء

### إعدادات الإنتاج

#### PHP (php.ini)
```ini
; إعدادات الأمان
expose_php = Off
display_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; إعدادات الأداء
memory_limit = 256M
max_execution_time = 30
max_input_time = 60
post_max_size = 50M
upload_max_filesize = 10M

; إعدادات الجلسة
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
```

#### MySQL (my.cnf)
```ini
[mysqld]
# إعدادات الأداء
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 128M
max_connections = 200

# إعدادات الأمان
bind-address = 127.0.0.1
skip-networking = false
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ الاتصال بقاعدة البيانات
```bash
# التحقق من حالة MySQL
sudo systemctl status mysql

# التحقق من إعدادات الاتصال
php -r "
$pdo = new PDO('mysql:host=localhost;dbname=R1', 'username', 'password');
echo 'اتصال ناجح';
"
```

#### مشاكل الصلاحيات
```bash
# إعداد صلاحيات الملفات
sudo chown -R www-data:www-data /path/to/seasystem
sudo chmod -R 755 /path/to/seasystem
sudo chmod -R 777 /path/to/seasystem/logs
sudo chmod -R 777 /path/to/seasystem/uploads
```

#### مشاكل الذاكرة
```php
// زيادة حد الذاكرة مؤقتاً
ini_set('memory_limit', '512M');

// تحسين الاستعلامات
$employees = Employee::select(['id', 'name', 'email'])
    ->limit(100)
    ->get();
```

### سجلات النظام

#### مراقبة السجلات
```bash
# مراقبة سجل التطبيق
tail -f logs/app.log

# مراقبة سجل قاعدة البيانات
tail -f logs/database.log

# مراقبة سجل الأخطاء
tail -f logs/error.log
```

#### تحليل الأداء
```php
// قياس وقت تنفيذ الاستعلام
$start = microtime(true);
$result = $db->select("SELECT * FROM employees");
$time = microtime(true) - $start;

Logger::logPerformance('employee_query', $time);
```

## الدعم والمساعدة

### الحصول على المساعدة

#### الوثائق
- [دليل المستخدم](docs/USER_GUIDE.md)
- [دليل المطور](docs/DEVELOPER_GUIDE.md)
- [دليل API](docs/API_GUIDE.md)
- [دليل Augment](docs/AUGMENT_GUIDE.md)

#### المجتمع
- [منتدى المطورين](https://forum.seasystem.com)
- [قناة Telegram](https://t.me/seasystem)
- [مجموعة Facebook](https://facebook.com/groups/seasystem)

#### الدعم التقني
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567
- نظام التذاكر: [support.seasystem.com](https://support.seasystem.com)

### المساهمة في المشروع

#### كيفية المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة الكود مع الاختبارات
4. إرسال Pull Request

#### إرشادات المساهمة
- اتباع معايير البرمجة المحددة
- كتابة اختبارات للكود الجديد
- توثيق التغييرات
- اختبار التوافق مع الإصدارات المختلفة

## الترخيص والحقوق

### معلومات الترخيص
- **الترخيص**: MIT License
- **حقوق الطبع**: © 2024 SeaSystem Development Team
- **الاستخدام التجاري**: مسموح
- **التعديل والتوزيع**: مسموح مع ذكر المصدر

### إخلاء المسؤولية
هذا البرنامج مقدم "كما هو" دون أي ضمانات. فريق التطوير غير مسؤول عن أي أضرار قد تنتج عن استخدام البرنامج.

## خارطة الطريق

### الإصدار الحالي (v1.0)
- ✅ النواة الأساسية والأمان
- ✅ وحدة الموارد البشرية
- ✅ وحدة الإدارة المالية
- ✅ وحدة إدارة المخزون
- ✅ وحدة إدارة المبيعات
- ✅ نظام التقارير الأساسي

### الإصدارات القادمة

#### v1.1 (Q2 2024)
- 🔄 تطبيق الهاتف المحمول
- 🔄 تحسينات الواجهة
- 🔄 تقارير متقدمة
- 🔄 تكامل مع البنوك

#### v1.2 (Q3 2024)
- 🔄 الذكاء الاصطناعي للتنبؤات
- 🔄 تكامل مع منصات التجارة الإلكترونية
- 🔄 نظام CRM متقدم
- 🔄 تحليلات الأعمال

#### v2.0 (Q4 2024)
- 🔄 إعادة تصميم كاملة
- 🔄 دعم الشركات متعددة الفروع
- 🔄 نظام الموافقات المتقدم
- 🔄 تكامل مع الأنظمة الحكومية

---

## شكر وتقدير

نتقدم بالشكر لجميع المساهمين في تطوير SeaSystem:

- **فريق التطوير الأساسي**
- **مجتمع المطورين**
- **المختبرين والمراجعين**
- **مقدمي التغذية الراجعة**

---

**SeaSystem ERP** - نظام تخطيط موارد المؤسسات الأكثر تطوراً للشركات العربية

🌐 **الموقع الرسمي**: [www.seasystem.com](https://www.seasystem.com)
📧 **البريد الإلكتروني**: <EMAIL>
📱 **الهاتف**: +966-11-1234567

---

*تم التطوير بـ ❤️ في المملكة العربية السعودية*
