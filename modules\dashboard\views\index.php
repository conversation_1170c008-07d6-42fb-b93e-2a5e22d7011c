<?php
/**
 * لوحة التحكم الرئيسية
 */

// إعداد البيانات للقالب
$title = 'لوحة التحكم';
$breadcrumbs = [
    ['title' => 'الرئيسية', 'url' => '/dashboard']
];

// بيانات الإحصائيات (مؤقتة)
$stats = [
    [
        'title' => 'إجمالي العملاء',
        'value' => '1,234',
        'icon' => 'bi-people',
        'color' => 'primary',
        'change' => '+12%',
        'url' => '/customers'
    ],
    [
        'title' => 'المنتجات',
        'value' => '567',
        'icon' => 'bi-box',
        'color' => 'success',
        'change' => '+5%',
        'url' => '/products'
    ],
    [
        'title' => 'الطلبات اليوم',
        'value' => '89',
        'icon' => 'bi-cart',
        'color' => 'warning',
        'change' => '+23%',
        'url' => '/sales/orders'
    ],
    [
        'title' => 'إجمالي المبيعات',
        'value' => '₹ 45,678',
        'icon' => 'bi-currency-dollar',
        'color' => 'info',
        'change' => '+18%',
        'url' => '/finance/reports'
    ]
];

// بدء المحتوى
ob_start();
?>
<!-- بطاقات الإحصائيات المحسنة -->
<div class="row g-4 mb-4">
    <?php foreach ($stats as $index => $stat): ?>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card h-100 border-0 shadow-sm position-relative overflow-hidden">
                <!-- خلفية متدرجة -->
                <div class="position-absolute top-0 end-0 w-100 h-100 opacity-10">
                    <div class="bg-gradient-<?= $stat['color'] ?> w-100 h-100"></div>
                </div>

                <div class="card-body p-4 position-relative">
                    <!-- الرأس مع القائمة -->
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="stats-icon bg-gradient-<?= $stat['color'] ?> shadow-sm">
                            <i class="<?= $stat['icon'] ?>"></i>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light dropdown-toggle border-0 shadow-sm" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><a class="dropdown-item" href="<?= $stat['url'] ?>">
                                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                </a></li>
                                <li><a class="dropdown-item" href="<?= $stat['url'] ?>/create">
                                    <i class="bi bi-plus-circle me-2"></i>إضافة جديد
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/reports">
                                    <i class="bi bi-graph-up me-2"></i>التقارير
                                </a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- المحتوى -->
                    <div class="stats-content">
                        <h6 class="stats-title text-muted mb-2 fw-normal"><?= $stat['title'] ?></h6>
                        <h2 class="stats-value text-<?= $stat['color'] ?> mb-2 fw-bold" data-counter="<?= $stat['value'] ?>">0</h2>
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-<?= $stat['color'] ?>-subtle text-<?= $stat['color'] ?> me-2">
                                <i class="bi bi-arrow-up me-1"></i><?= $stat['change'] ?>
                            </span>
                            <small class="text-muted">من الفترة السابقة</small>
                        </div>
                    </div>

                    <!-- شريط التقدم -->
                    <div class="progress mb-3" style="height: 6px;">
                        <div class="progress-bar bg-<?= $stat['color'] ?> progress-bar-animated"
                             style="width: <?= 60 + ($index * 10) ?>%"
                             data-progress="<?= 60 + ($index * 10) ?>"></div>
                    </div>

                    <!-- رابط سريع -->
                    <a href="<?= $stat['url'] ?>" class="btn btn-<?= $stat['color'] ?> btn-sm w-100 d-flex align-items-center justify-content-center">
                        <i class="bi bi-arrow-right me-2"></i>
                        عرض التفاصيل
                    </a>
                </div>

                <!-- تأثير الهوفر -->
                <div class="position-absolute top-0 start-0 w-100 h-100 bg-<?= $stat['color'] ?> opacity-0 hover-overlay"></div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- الرسوم البيانية والتقارير -->
<div class="row mb-4">
    <div class="col-xl-8 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    مخطط المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    توزيع المبيعات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="distributionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- النشاط الأخير والإشعارات -->
<div class="row">
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="activity-timeline">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">تم إضافة عميل جديد</div>
                            <div class="activity-description">أحمد محمد - شركة التقنية المتقدمة</div>
                            <div class="activity-time">منذ 5 دقائق</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="bi bi-cart-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">طلب جديد #SO-001</div>
                            <div class="activity-description">طلب بقيمة 15,000 ريال</div>
                            <div class="activity-time">منذ 15 دقيقة</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">تحذير مخزون منخفض</div>
                            <div class="activity-description">منتج لابتوب ديل - متبقي 5 قطع</div>
                            <div class="activity-time">منذ 30 دقيقة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bell me-2"></i>
                    الإشعارات
                </h5>
            </div>
            <div class="card-body">
                <div class="notification-list">
                    <div class="notification-item">
                        <div class="notification-icon bg-info">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">مرحباً بك في النظام</div>
                            <div class="notification-time">اليوم</div>
                        </div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-icon bg-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">يرجى تحديث معلومات الشركة</div>
                            <div class="notification-time">أمس</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// إنهاء المحتوى
$content = ob_get_clean();

// إضافة الأنماط المخصصة
$additionalCSS = [
    'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css'
];

// إضافة السكريبت المخصص
$additionalJS = [
    'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js',
    '/assets/js/dashboard-enhancements.js'
];

$pageScript = '
// تهيئة لوحة التحكم المحسنة
document.addEventListener("DOMContentLoaded", function() {
    // تحريك العدادات
    animateCounters();

    // تحريك أشرطة التقدم
    animateProgressBars();

    // تهيئة الرسوم البيانية
    initializeCharts();

    // تحديث الوقت الحالي
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // كل دقيقة
});

// تحريك العدادات
function animateCounters() {
    const counters = document.querySelectorAll("[data-counter]");

    counters.forEach((counter, index) => {
        const target = parseInt(counter.getAttribute("data-counter"));
        const duration = 2000 + (index * 200); // تأخير متدرج
        const increment = target / (duration / 16);
        let current = 0;

        setTimeout(() => {
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // تنسيق الرقم
                if (target > 1000) {
                    counter.textContent = new Intl.NumberFormat("ar-SA").format(Math.floor(current));
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 16);
        }, index * 300);
    });
}

// تحريك أشرطة التقدم
function animateProgressBars() {
    const progressBars = document.querySelectorAll(".progress-bar[data-progress]");

    progressBars.forEach((bar, index) => {
        const targetWidth = bar.getAttribute("data-progress");

        setTimeout(() => {
            bar.style.width = "0%";
            setTimeout(() => {
                bar.style.transition = "width 1.5s cubic-bezier(0.4, 0, 0.2, 1)";
                bar.style.width = targetWidth + "%";
            }, 100);
        }, index * 200 + 1000); // بعد العدادات
    });
}

// تهيئة الرسوم البيانية المحسنة
function initializeCharts() {
    // رسم بياني للمبيعات مع تدرج
    const salesCtx = document.getElementById("salesChart");
    if (salesCtx) {
        const gradient = salesCtx.getContext("2d").createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, "rgba(99, 102, 241, 0.3)");
        gradient.addColorStop(1, "rgba(99, 102, 241, 0.05)");

        new Chart(salesCtx, {
            type: "line",
            data: {
                labels: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو"],
                datasets: [{
                    label: "المبيعات (ر.س)",
                    data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                    borderColor: "rgb(99, 102, 241)",
                    backgroundColor: gradient,
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: "rgb(99, 102, 241)",
                    pointBorderColor: "#fff",
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: "index"
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: "rgba(0, 0, 0, 0.8)",
                        titleColor: "#fff",
                        bodyColor: "#fff",
                        borderColor: "rgb(99, 102, 241)",
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return "المبيعات: " + new Intl.NumberFormat("ar-SA").format(context.parsed.y) + " ر.س";
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: "rgba(0,0,0,0.05)",
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat("ar-SA", {
                                    notation: "compact",
                                    compactDisplay: "short"
                                }).format(value) + " ر.س";
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: "easeInOutQuart"
                }
            }
        });
    }

    // رسم بياني دائري محسن
    const distributionCtx = document.getElementById("distributionChart");
    if (distributionCtx) {
        new Chart(distributionCtx, {
            type: "doughnut",
            data: {
                labels: ["إلكترونيات", "ملابس", "كتب", "أخرى"],
                datasets: [{
                    data: [40, 25, 20, 15],
                    backgroundColor: [
                        "rgb(99, 102, 241)",
                        "rgb(34, 197, 94)",
                        "rgb(251, 146, 60)",
                        "rgb(239, 68, 68)"
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: "70%",
                plugins: {
                    legend: {
                        position: "bottom",
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: "circle",
                            font: {
                                size: 12,
                                family: "Cairo, sans-serif"
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgba(0, 0, 0, 0.8)",
                        titleColor: "#fff",
                        bodyColor: "#fff",
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.label + ": " + context.parsed + "%";
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });
    }
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const timeElement = document.getElementById("currentTime");
    if (timeElement) {
        const now = new Date();
        const options = {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit"
        };
        timeElement.textContent = now.toLocaleDateString("ar-SA", options);
    }
}
';

// تضمين القالب
include dirname(__DIR__, 2) . '/shared/layout.php';
?>
