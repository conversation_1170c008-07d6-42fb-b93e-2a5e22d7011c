<?php
/**
 * اختبار تسجيل دخول بدون جلسة
 */

// بدء الجلسة أولاً
session_start();

echo "🧪 اختبار تسجيل الدخول مع الجلسة\n";
echo "==================================\n\n";

try {
    require_once 'core/Environment.php';
    Environment::load();
    require_once 'core/helpers.php';
    require_once 'core/Database.php';
    require_once 'core/Session.php';
    require_once 'core/Auth.php';
    
    echo "1. تم تحميل جميع الملفات\n";
    
    $auth = new Auth();
    echo "2. تم إنشاء مثيل Auth\n";
    
    $result = $auth->login('admin', 'admin123', false);
    echo "3. نتيجة تسجيل الدخول:\n";
    print_r($result);
    
    if ($result['success']) {
        echo "\n✅ تم تسجيل الدخول بنجاح!\n";
        
        // التحقق من الجلسة
        echo "\n4. معلومات الجلسة:\n";
        echo "   معرف المستخدم: " . ($_SESSION['user_id'] ?? 'غير محدد') . "\n";
        echo "   مصادق: " . ($_SESSION['user_authenticated'] ?? 'لا') . "\n";
        echo "   اسم المستخدم: " . ($_SESSION['user_username'] ?? 'غير محدد') . "\n";
        
        // التحقق من المستخدم الحالي
        $currentUser = $auth->user();
        if ($currentUser) {
            echo "\n5. المستخدم الحالي:\n";
            echo "   الاسم: " . $currentUser['first_name'] . " " . $currentUser['last_name'] . "\n";
            echo "   البريد: " . $currentUser['email'] . "\n";
        }
        
    } else {
        echo "\n❌ فشل تسجيل الدخول: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?>
