<?php
/**
 * SeaSystem Session Manager
 * إدارة الجلسات والأمان
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Environment.php';

class Session
{
    private static $started = false;
    private static $config = [];

    /**
     * بدء الجلسة
     */
    public static function start()
    {
        if (self::$started || session_status() === PHP_SESSION_ACTIVE) {
            self::$started = true;
            return;
        }

        // تحميل التكوين
        self::loadConfig();

        // إعداد معاملات الجلسة
        self::configureSession();

        // بدء الجلسة
        if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }

        self::$started = true;

        // التحقق من صحة الجلسة
        self::validateSession();

        // تجديد معرف الجلسة دورياً
        self::regenerateIdIfNeeded();
    }

    /**
     * تحميل إعدادات الجلسة
     */
    private static function loadConfig()
    {
        Environment::load();
        
        self::$config = [
            'lifetime' => Environment::get('SESSION_LIFETIME', 3600),
            'name' => 'SEASYSTEM_SESSION',
            'cookie_lifetime' => 0,
            'cookie_path' => '/',
            'cookie_domain' => '',
            'cookie_secure' => isset($_SERVER['HTTPS']),
            'cookie_httponly' => true,
            'cookie_samesite' => 'Strict',
            'use_strict_mode' => true,
            'use_cookies' => true,
            'use_only_cookies' => true,
            'regenerate_interval' => 300 // 5 دقائق
        ];
    }

    /**
     * إعداد معاملات الجلسة
     */
    private static function configureSession()
    {
        // التحقق من أن الـ headers لم يتم إرسالها بعد
        if (headers_sent()) {
            return; // تجاهل الإعدادات إذا تم إرسال الـ headers
        }

        // تعيين اسم الجلسة
        session_name(self::$config['name']);

        // إعداد معاملات الكوكيز
        session_set_cookie_params([
            'lifetime' => self::$config['cookie_lifetime'],
            'path' => self::$config['cookie_path'],
            'domain' => self::$config['cookie_domain'],
            'secure' => self::$config['cookie_secure'],
            'httponly' => self::$config['cookie_httponly'],
            'samesite' => self::$config['cookie_samesite']
        ]);

        // إعدادات إضافية
        ini_set('session.use_strict_mode', self::$config['use_strict_mode']);
        ini_set('session.use_cookies', self::$config['use_cookies']);
        ini_set('session.use_only_cookies', self::$config['use_only_cookies']);
        ini_set('session.gc_maxlifetime', self::$config['lifetime']);
    }

    /**
     * التحقق من صحة الجلسة
     */
    private static function validateSession()
    {
        // التحقق من انتهاء صلاحية الجلسة
        if (self::has('last_activity')) {
            $lastActivity = self::get('last_activity');
            if (time() - $lastActivity > self::$config['lifetime']) {
                self::destroy();
                return;
            }
        }

        // التحقق من عنوان IP (اختياري)
        if (self::has('ip_address')) {
            $sessionIp = self::get('ip_address');
            $currentIp = self::getClientIp();
            if ($sessionIp !== $currentIp) {
                // يمكن تسجيل هذا كمحاولة اختراق
                self::destroy();
                return;
            }
        }

        // التحقق من User Agent (اختياري)
        if (self::has('user_agent')) {
            $sessionAgent = self::get('user_agent');
            $currentAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            if ($sessionAgent !== $currentAgent) {
                self::destroy();
                return;
            }
        }

        // تحديث وقت النشاط الأخير
        self::set('last_activity', time());
    }

    /**
     * تجديد معرف الجلسة إذا لزم الأمر
     */
    private static function regenerateIdIfNeeded()
    {
        $lastRegenerate = self::get('last_regenerate', 0);
        
        if (time() - $lastRegenerate > self::$config['regenerate_interval']) {
            session_regenerate_id(true);
            self::set('last_regenerate', time());
        }
    }

    /**
     * تعيين قيمة في الجلسة
     * 
     * @param string $key المفتاح
     * @param mixed $value القيمة
     */
    public static function set($key, $value)
    {
        self::start();
        $_SESSION[$key] = $value;
    }

    /**
     * الحصول على قيمة من الجلسة
     * 
     * @param string $key المفتاح
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * التحقق من وجود مفتاح في الجلسة
     * 
     * @param string $key المفتاح
     * @return bool
     */
    public static function has($key)
    {
        self::start();
        return isset($_SESSION[$key]);
    }

    /**
     * حذف مفتاح من الجلسة
     * 
     * @param string $key المفتاح
     */
    public static function remove($key)
    {
        self::start();
        unset($_SESSION[$key]);
    }

    /**
     * مسح جميع بيانات الجلسة
     */
    public static function clear()
    {
        self::start();
        $_SESSION = [];
    }

    /**
     * تدمير الجلسة
     */
    public static function destroy()
    {
        self::start();
        
        // مسح بيانات الجلسة
        $_SESSION = [];
        
        // حذف كوكي الجلسة
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // تدمير الجلسة
        session_destroy();
        self::$started = false;
    }

    /**
     * تجديد معرف الجلسة
     */
    public static function regenerateId()
    {
        self::start();
        session_regenerate_id(true);
        self::set('last_regenerate', time());
    }

    /**
     * الحصول على معرف الجلسة
     * 
     * @return string
     */
    public static function getId()
    {
        self::start();
        return session_id();
    }

    /**
     * تعيين رسالة فلاش
     * 
     * @param string $key المفتاح
     * @param mixed $value القيمة
     */
    public static function flash($key, $value)
    {
        self::set('flash_' . $key, $value);
    }

    /**
     * الحصول على رسالة فلاش وحذفها
     * 
     * @param string $key المفتاح
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    public static function getFlash($key, $default = null)
    {
        $flashKey = 'flash_' . $key;
        $value = self::get($flashKey, $default);
        self::remove($flashKey);
        return $value;
    }

    /**
     * التحقق من وجود رسالة فلاش
     * 
     * @param string $key المفتاح
     * @return bool
     */
    public static function hasFlash($key)
    {
        return self::has('flash_' . $key);
    }

    /**
     * الحصول على عنوان IP الخاص بالعميل
     * 
     * @return string
     */
    private static function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // في حالة وجود عدة عناوين IP
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * تسجيل معلومات الجلسة الأساسية
     */
    public static function initializeSession()
    {
        self::start();
        
        if (!self::has('initialized')) {
            self::set('initialized', true);
            self::set('created_at', time());
            self::set('ip_address', self::getClientIp());
            self::set('user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
            self::set('last_activity', time());
            self::set('last_regenerate', time());
        }
    }

    /**
     * الحصول على معلومات الجلسة
     * 
     * @return array
     */
    public static function getInfo()
    {
        self::start();
        
        return [
            'id' => self::getId(),
            'created_at' => self::get('created_at'),
            'last_activity' => self::get('last_activity'),
            'ip_address' => self::get('ip_address'),
            'user_agent' => self::get('user_agent'),
            'lifetime' => self::$config['lifetime'],
            'time_remaining' => self::$config['lifetime'] - (time() - self::get('last_activity', time()))
        ];
    }

    /**
     * التحقق من انتهاء صلاحية الجلسة
     * 
     * @return bool
     */
    public static function isExpired()
    {
        if (!self::has('last_activity')) {
            return false;
        }
        
        $lastActivity = self::get('last_activity');
        return (time() - $lastActivity) > self::$config['lifetime'];
    }

    /**
     * تمديد الجلسة
     */
    public static function extend()
    {
        self::set('last_activity', time());
    }

    /**
     * الحصول على جميع بيانات الجلسة
     * 
     * @return array
     */
    public static function all()
    {
        self::start();
        return $_SESSION;
    }

    /**
     * التحقق من بدء الجلسة
     * 
     * @return bool
     */
    public static function isStarted()
    {
        return self::$started;
    }
}
