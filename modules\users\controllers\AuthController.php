<?php
/**
 * SeaSystem Authentication Controller
 * متحكم المصادقة
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(dirname(dirname(__DIR__))) . '/core/Auth.php';
require_once dirname(dirname(dirname(__DIR__))) . '/core/Session.php';

class AuthController
{
    private $auth;

    public function __construct()
    {
        $this->auth = new Auth();
        Session::start();
    }

    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLoginForm()
    {
        // إعادة التوجيه إذا كان المستخدم مسجل دخوله بالفعل
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->render('auth/login', [
            'title' => 'تسجيل الدخول - SeaSystem',
            'error' => Session::getFlash('error'),
            'success' => Session::getFlash('success'),
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * معالجة تسجيل الدخول
     */
    public function login()
    {
        try {
            // التحقق من طريقة الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            // التحقق من رمز CSRF (معطل مؤقتاً للاختبار)
            // if (!$this->validateCsrfToken()) {
            //     throw new Exception('رمز الأمان غير صحيح');
            // }

            // الحصول على البيانات
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);

            // التحقق من صحة البيانات
            if (empty($username) || empty($password)) {
                throw new Exception('يرجى إدخال اسم المستخدم وكلمة المرور');
            }

            // محاولة تسجيل الدخول
            $logFile = dirname(dirname(dirname(__DIR__))) . '/logs/debug.log';
            if (!is_dir(dirname($logFile))) {
                mkdir(dirname($logFile), 0755, true);
            }
            file_put_contents($logFile, "محاولة تسجيل الدخول للمستخدم: {$username}\n", FILE_APPEND);
            $result = $this->auth->login($username, $password, $remember);
            file_put_contents($logFile, "نتيجة تسجيل الدخول: " . print_r($result, true) . "\n", FILE_APPEND);

            if ($result['success']) {
                // تسجيل الدخول بنجاح
                error_log("تسجيل الدخول نجح - إعادة التوجيه إلى /dashboard");
                Session::flash('success', $result['message']);

                // إعادة التوجيه إلى لوحة التحكم
                $this->redirect('/dashboard');
            } else {
                // فشل تسجيل الدخول
                error_log("فشل تسجيل الدخول: " . $result['message']);
                Session::flash('error', $result['message']);
                $this->redirect('/login');
            }

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/login');
        }
    }

    /**
     * تسجيل الخروج
     */
    public function logout()
    {
        $this->auth->logout();
        Session::flash('success', 'تم تسجيل الخروج بنجاح');
        $this->redirect('/login');
    }

    /**
     * عرض صفحة إعادة تعيين كلمة المرور
     */
    public function showResetForm()
    {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->render('auth/reset', [
            'title' => 'إعادة تعيين كلمة المرور - SeaSystem',
            'error' => Session::getFlash('error'),
            'success' => Session::getFlash('success')
        ]);
    }

    /**
     * معالجة طلب إعادة تعيين كلمة المرور
     */
    public function sendResetLink()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            if (!$this->validateCsrfToken()) {
                throw new Exception('رمز الأمان غير صحيح');
            }

            $email = trim($_POST['email'] ?? '');

            if (empty($email)) {
                throw new Exception('يرجى إدخال البريد الإلكتروني');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('البريد الإلكتروني غير صحيح');
            }

            $result = $this->auth->resetPassword($email);

            Session::flash($result['success'] ? 'success' : 'error', $result['message']);
            $this->redirect('/reset-password');

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/reset-password');
        }
    }

    /**
     * عرض صفحة تغيير كلمة المرور
     */
    public function showChangePasswordForm()
    {
        if (!$this->auth->check()) {
            Session::set('intended_url', '/change-password');
            $this->redirect('/login');
            return;
        }

        $this->render('auth/change-password', [
            'title' => 'تغيير كلمة المرور - SeaSystem',
            'user' => $this->auth->user(),
            'error' => Session::getFlash('error'),
            'success' => Session::getFlash('success')
        ]);
    }

    /**
     * معالجة تغيير كلمة المرور
     */
    public function changePassword()
    {
        try {
            if (!$this->auth->check()) {
                throw new Exception('يجب تسجيل الدخول أولاً');
            }

            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            if (!$this->validateCsrfToken()) {
                throw new Exception('رمز الأمان غير صحيح');
            }

            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new Exception('جميع الحقول مطلوبة');
            }

            if ($newPassword !== $confirmPassword) {
                throw new Exception('كلمة المرور الجديدة وتأكيدها غير متطابقتين');
            }

            $result = $this->auth->changePassword($currentPassword, $newPassword);

            Session::flash($result['success'] ? 'success' : 'error', $result['message']);
            $this->redirect('/change-password');

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/change-password');
        }
    }

    /**
     * عرض الملف الشخصي
     */
    public function profile()
    {
        if (!$this->auth->check()) {
            Session::set('intended_url', '/profile');
            $this->redirect('/login');
            return;
        }

        $user = $this->auth->user();
        
        $this->render('auth/profile', [
            'title' => 'الملف الشخصي - SeaSystem',
            'user' => $user,
            'error' => Session::getFlash('error'),
            'success' => Session::getFlash('success')
        ]);
    }

    /**
     * تحديث الملف الشخصي
     */
    public function updateProfile()
    {
        try {
            if (!$this->auth->check()) {
                throw new Exception('يجب تسجيل الدخول أولاً');
            }

            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            if (!$this->validateCsrfToken()) {
                throw new Exception('رمز الأمان غير صحيح');
            }

            $user = $this->auth->user();
            $db = Database::getInstance();

            // تنظيف البيانات
            $data = [
                'first_name' => trim($_POST['first_name'] ?? ''),
                'last_name' => trim($_POST['last_name'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'phone' => trim($_POST['phone'] ?? '')
            ];

            // التحقق من البيانات
            if (empty($data['first_name']) || empty($data['last_name'])) {
                throw new Exception('الاسم الأول واسم العائلة مطلوبان');
            }

            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('البريد الإلكتروني غير صحيح');
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            $existingUser = $db->selectOne(
                "SELECT id FROM users WHERE email = ? AND id != ?",
                [$data['email'], $user['id']]
            );

            if ($existingUser) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً');
            }

            // تحديث البيانات
            $db->update('users', $data, ['id' => $user['id']]);

            Session::flash('success', 'تم تحديث الملف الشخصي بنجاح');
            $this->redirect('/profile');

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/profile');
        }
    }

    /**
     * التحقق من رمز CSRF
     * 
     * @return bool
     */
    private function validateCsrfToken()
    {
        $token = $_POST['csrf_token'] ?? '';
        $sessionToken = Session::get('csrf_token');
        
        return !empty($token) && !empty($sessionToken) && hash_equals($sessionToken, $token);
    }

    /**
     * إنشاء رمز CSRF
     * 
     * @return string
     */
    public function generateCsrfToken()
    {
        $token = bin2hex(random_bytes(32));
        Session::set('csrf_token', $token);
        return $token;
    }

    /**
     * عرض الصفحة
     * 
     * @param string $view اسم العرض
     * @param array $data البيانات
     */
    private function render($view, $data = [])
    {
        // إضافة رمز CSRF للبيانات
        $data['csrf_token'] = $this->generateCsrfToken();
        
        extract($data);
        
        // تضمين ملف العرض
        $viewFile = dirname(__DIR__) . "/views/{$view}.php";
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("ملف العرض غير موجود: {$view}");
        }
    }

    /**
     * إعادة التوجيه
     * 
     * @param string $url الرابط
     */
    private function redirect($url)
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * إرجاع استجابة JSON
     * 
     * @param array $data البيانات
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * التحقق من حالة تسجيل الدخول (AJAX)
     */
    public function checkAuth()
    {
        $this->jsonResponse([
            'authenticated' => $this->auth->check(),
            'user' => $this->auth->user()
        ]);
    }

    /**
     * تمديد الجلسة (AJAX)
     */
    public function extendSession()
    {
        if ($this->auth->check()) {
            Session::extend();
            $this->jsonResponse(['success' => true, 'message' => 'تم تمديد الجلسة']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'الجلسة منتهية']);
        }
    }
}
