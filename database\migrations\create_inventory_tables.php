<?php
/**
 * إنشاء جداول المخزون
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📦 إنشاء جداول المخزون...\n";
    echo "========================\n\n";

    // جدول فئات المنتجات
    $sql = "
    CREATE TABLE IF NOT EXISTS product_categories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        parent_id INT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,
        INDEX idx_name (name)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول product_categories\n";

    // جدول وحدات القياس
    $sql = "
    CREATE TABLE IF NOT EXISTS units (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        symbol VARCHAR(10) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_name (name),
        UNIQUE KEY unique_symbol (symbol)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول units\n";

    // جدول المنتجات
    $sql = "
    CREATE TABLE IF NOT EXISTS products (
        id INT PRIMARY KEY AUTO_INCREMENT,
        sku VARCHAR(100) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id INT,
        unit_id INT,
        cost_price DECIMAL(15,2) DEFAULT 0,
        selling_price DECIMAL(15,2) DEFAULT 0,
        min_stock_level INT DEFAULT 0,
        max_stock_level INT DEFAULT 0,
        current_stock INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        image VARCHAR(255),
        barcode VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
        FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL,
        INDEX idx_sku (sku),
        INDEX idx_name (name),
        INDEX idx_category (category_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول products\n";

    // جدول المخازن
    $sql = "
    CREATE TABLE IF NOT EXISTS warehouses (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(50) UNIQUE NOT NULL,
        address TEXT,
        manager_id INT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL,
        INDEX idx_code (code)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول warehouses\n";

    // جدول مخزون المنتجات في المخازن
    $sql = "
    CREATE TABLE IF NOT EXISTS warehouse_stock (
        id INT PRIMARY KEY AUTO_INCREMENT,
        warehouse_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 0,
        reserved_quantity INT NOT NULL DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_warehouse_product (warehouse_id, product_id),
        INDEX idx_warehouse (warehouse_id),
        INDEX idx_product (product_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول warehouse_stock\n";

    // جدول حركات المخزون
    $sql = "
    CREATE TABLE IF NOT EXISTS stock_movements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        product_id INT NOT NULL,
        warehouse_id INT NOT NULL,
        movement_type ENUM('in', 'out', 'transfer', 'adjustment') NOT NULL,
        quantity INT NOT NULL,
        unit_cost DECIMAL(15,2) DEFAULT 0,
        reference_type VARCHAR(50),
        reference_id INT,
        notes TEXT,
        movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by INT,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_product (product_id),
        INDEX idx_warehouse (warehouse_id),
        INDEX idx_movement_date (movement_date),
        INDEX idx_movement_type (movement_type)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول stock_movements\n";

    // جدول الموردين
    $sql = "
    CREATE TABLE IF NOT EXISTS suppliers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        tax_number VARCHAR(100),
        payment_terms INT DEFAULT 30,
        credit_limit DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_email (email)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول suppliers\n";

    // جدول طلبات الشراء
    $sql = "
    CREATE TABLE IF NOT EXISTS purchase_orders (
        id INT PRIMARY KEY AUTO_INCREMENT,
        po_number VARCHAR(50) UNIQUE NOT NULL,
        supplier_id INT NOT NULL,
        order_date DATE NOT NULL,
        expected_date DATE,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'sent', 'confirmed', 'received', 'cancelled') DEFAULT 'draft',
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_po_number (po_number),
        INDEX idx_order_date (order_date),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول purchase_orders\n";

    // جدول تفاصيل طلبات الشراء
    $sql = "
    CREATE TABLE IF NOT EXISTS purchase_order_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        purchase_order_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL,
        unit_price DECIMAL(15,2) NOT NULL,
        total_price DECIMAL(15,2) NOT NULL,
        received_quantity INT DEFAULT 0,
        FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
        INDEX idx_purchase_order (purchase_order_id),
        INDEX idx_product (product_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول purchase_order_items\n";

    echo "\n🎉 تم إنشاء جميع جداول المخزون بنجاح!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
