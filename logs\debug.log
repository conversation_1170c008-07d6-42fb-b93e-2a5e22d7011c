handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تحديث البيانات: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'field list'
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 1
    [message] => تم تسجيل الدخول بنجاح
    [user] => Array
        (
            [id] => 1
            [username] => admin
            [email] => <EMAIL>
            [password_hash] => $2y$10$LKoWWmT7/fed5MPwYOMvEOor/vSZLvgXgcie/AcXMHn0BfSkzpx9G
            [first_name] => مدير
            [last_name] => النظام
            [phone] => 
            [avatar] => 
            [is_active] => 1
            [email_verified_at] => 
            [last_login_at] => 2025-07-04 14:36:42
            [failed_login_attempts] => 0
            [locked_until] => 
            [created_at] => 2025-07-04 12:59:08
            [updated_at] => 2025-07-04 14:36:42
        )

)

