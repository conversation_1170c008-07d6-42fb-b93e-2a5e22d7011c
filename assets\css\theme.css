/**
 * SeaSystem ERP - نظام الألوان والتصميم الموحد
 * Modern Design System with CSS Variables
 */

:root {
  /* الألوان الأساسية */
  --primary-50: #f0f4ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;

  /* الألوان الثانوية */
  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;

  /* ألوان النجاح */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  /* ألوان التحذير */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* ألوان الخطر */
  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-200: #fecaca;
  --danger-300: #fca5a5;
  --danger-400: #f87171;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;
  --danger-800: #991b1b;
  --danger-900: #7f1d1d;

  /* الألوان المحايدة */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* الخطوط */
  --font-family-sans: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* أحجام الخطوط */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* المسافات */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* الانحناءات */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* التحولات */
  --transition-all: all 0.15s ease-in-out;
  --transition-colors: color, background-color, border-color, text-decoration-color, fill, stroke 0.15s ease-in-out;
  --transition-transform: transform 0.15s ease-in-out;
}

/* إعادة تعيين الأساسيات */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  direction: rtl;
  text-align: right;
}

/* الروابط */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover {
  color: var(--primary-700);
}

/* الأزرار */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--text-sm);
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: var(--transition-all);
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.btn-success {
  background-color: var(--success-600);
  color: white;
  border-color: var(--success-600);
}

.btn-success:hover {
  background-color: var(--success-700);
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
  border-color: var(--warning-500);
}

.btn-warning:hover {
  background-color: var(--warning-600);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--danger-600);
  color: white;
  border-color: var(--danger-600);
}

.btn-danger:hover {
  background-color: var(--danger-700);
  transform: translateY(-1px);
}

/* البطاقات */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50), white);
}

.card-body {
  padding: var(--spacing-6);
}

/* الشريط الجانبي */
.sidebar {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  min-height: 100vh;
  color: white;
  box-shadow: var(--shadow-xl);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: var(--spacing-3) var(--spacing-5);
  border-radius: var(--radius-lg);
  margin: var(--spacing-1) 0;
  transition: var(--transition-all);
  display: flex;
  align-items: center;
}

.sidebar .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-5px);
  backdrop-filter: blur(10px);
}

.sidebar .nav-link.active {
  color: white;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  box-shadow: var(--shadow);
}

/* الجداول */
.table {
  background-color: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
  border: none;
  font-weight: 600;
  color: var(--gray-700);
  padding: var(--spacing-4);
}

.table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: var(--primary-50);
  transition: var(--transition-colors);
}

/* النماذج */
.form-control {
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  transition: var(--transition-all);
  background-color: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: var(--primary-50);
}

/* الشارات */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-full);
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background-color: var(--success-100);
  color: var(--success-800);
}

.badge-warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.badge-danger {
  background-color: var(--danger-100);
  color: var(--danger-800);
}

.badge-secondary {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* الاستجابة للشاشات */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: var(--transition-transform);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .card-body {
    padding: var(--spacing-4);
  }
  
  .table-responsive {
    font-size: var(--text-sm);
  }
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* بطاقات الإحصائيات */
.stats-card {
  transition: var(--transition-all);
  border: none;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stats-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.stats-title {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0.5rem;
}

.stats-change {
  font-size: 0.75rem;
  font-weight: 500;
}

/* النشاط والإشعارات */
.activity-timeline {
  position: relative;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.activity-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20px;
  top: 40px;
  width: 2px;
  height: calc(100% + 0.5rem);
  background: var(--gray-200);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-left: 1rem;
  position: relative;
  z-index: 1;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.activity-description {
  color: var(--gray-600);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.activity-time {
  color: var(--gray-500);
  font-size: 0.75rem;
}

.notification-list {
  space-y: 1rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  margin-bottom: 0.75rem;
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-left: 0.75rem;
  font-size: 0.875rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.notification-time {
  color: var(--gray-500);
  font-size: 0.75rem;
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }

  body {
    background-color: var(--gray-900);
    color: var(--gray-100);
  }

  .card {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
  }
}
