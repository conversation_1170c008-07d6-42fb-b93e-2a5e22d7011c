<?php
/**
 * اختبار نهائي للنظام
 */

// بدء الجلسة
session_start();

echo "🧪 اختبار النظام النهائي\n";
echo "======================\n\n";

try {
    require_once 'core/Environment.php';
    Environment::load();
    require_once 'core/helpers.php';
    require_once 'core/Database.php';
    require_once 'core/Session.php';
    require_once 'core/Auth.php';
    
    echo "1. ✅ تم تحميل جميع الملفات\n";
    
    $auth = new Auth();
    echo "2. ✅ تم إنشاء مثيل Auth\n";
    
    $result = $auth->login('admin', 'admin123', false);
    echo "3. نتيجة تسجيل الدخول: ";
    
    if ($result['success']) {
        echo "✅ نجح\n";
        echo "   الرسالة: " . $result['message'] . "\n";
        
        // التحقق من الجلسة
        echo "\n4. معلومات الجلسة:\n";
        echo "   معرف المستخدم: " . ($_SESSION['user_id'] ?? 'غير محدد') . "\n";
        echo "   مصادق: " . ($_SESSION['user_authenticated'] ? 'نعم' : 'لا') . "\n";
        echo "   اسم المستخدم: " . ($_SESSION['user_username'] ?? 'غير محدد') . "\n";
        
        // التحقق من المستخدم الحالي
        if ($auth->check()) {
            echo "\n5. ✅ المستخدم مسجل دخول\n";
            
            $currentUser = $auth->user();
            if ($currentUser) {
                echo "   الاسم: " . $currentUser['first_name'] . " " . $currentUser['last_name'] . "\n";
                echo "   البريد: " . $currentUser['email'] . "\n";
                echo "   آخر تسجيل دخول: " . ($currentUser['last_login_at'] ?? 'غير محدد') . "\n";
            }
        } else {
            echo "\n❌ المستخدم غير مسجل دخول\n";
        }
        
    } else {
        echo "❌ فشل\n";
        echo "   الرسالة: " . $result['message'] . "\n";
    }
    
    echo "\n🎉 انتهى الاختبار بنجاح!\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?>
