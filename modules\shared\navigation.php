<?php
/**
 * قائمة التنقل الموحدة
 * Unified Navigation Menu
 */

$currentRoute = $_SERVER['REQUEST_URI'] ?? '/';

// تعريف عناصر القائمة
$menuItems = [
    [
        'title' => 'لوحة التحكم',
        'icon' => 'bi-speedometer2',
        'url' => '/dashboard',
        'permission' => 'dashboard_view'
    ],
    [
        'title' => 'إدارة المستخدمين',
        'icon' => 'bi-people',
        'permission' => 'users_view',
        'children' => [
            [
                'title' => 'المستخدمين',
                'icon' => 'bi-person',
                'url' => '/users',
                'permission' => 'users_view'
            ],
            [
                'title' => 'الأدوار والصلاحيات',
                'icon' => 'bi-shield-check',
                'url' => '/users/roles',
                'permission' => 'roles_view'
            ],
            [
                'title' => 'سجل الأنشطة',
                'icon' => 'bi-clock-history',
                'url' => '/users/activity',
                'permission' => 'activity_view'
            ]
        ]
    ],
    [
        'title' => 'الموارد البشرية',
        'icon' => 'bi-person-badge',
        'permission' => 'hr_view',
        'children' => [
            [
                'title' => 'الموظفين',
                'icon' => 'bi-people',
                'url' => '/hr/employees',
                'permission' => 'employees_view'
            ],
            [
                'title' => 'الأقسام',
                'icon' => 'bi-building',
                'url' => '/hr/departments',
                'permission' => 'departments_view'
            ],
            [
                'title' => 'المناصب',
                'icon' => 'bi-briefcase',
                'url' => '/hr/positions',
                'permission' => 'positions_view'
            ],
            [
                'title' => 'الحضور والانصراف',
                'icon' => 'bi-clock',
                'url' => '/hr/attendance',
                'permission' => 'attendance_view'
            ],
            [
                'title' => 'الرواتب',
                'icon' => 'bi-cash-stack',
                'url' => '/hr/payroll',
                'permission' => 'payroll_view'
            ],
            [
                'title' => 'الإجازات',
                'icon' => 'bi-calendar-x',
                'url' => '/hr/leaves',
                'permission' => 'leaves_view'
            ]
        ]
    ],
    [
        'title' => 'الإدارة المالية',
        'icon' => 'bi-graph-up',
        'permission' => 'finance_view',
        'children' => [
            [
                'title' => 'دليل الحسابات',
                'icon' => 'bi-list-ul',
                'url' => '/finance/accounts',
                'permission' => 'accounts_view'
            ],
            [
                'title' => 'القيود اليومية',
                'icon' => 'bi-journal-text',
                'url' => '/finance/journal',
                'permission' => 'journal_view'
            ],
            [
                'title' => 'الفواتير',
                'icon' => 'bi-receipt',
                'url' => '/finance/invoices',
                'permission' => 'invoices_view'
            ],
            [
                'title' => 'المدفوعات',
                'icon' => 'bi-credit-card',
                'url' => '/finance/payments',
                'permission' => 'payments_view'
            ],
            [
                'title' => 'التقارير المالية',
                'icon' => 'bi-bar-chart',
                'url' => '/finance/reports',
                'permission' => 'finance_reports_view'
            ]
        ]
    ],
    [
        'title' => 'إدارة المخزون',
        'icon' => 'bi-boxes',
        'permission' => 'inventory_view',
        'children' => [
            [
                'title' => 'المنتجات',
                'icon' => 'bi-box',
                'url' => '/products',
                'permission' => 'products_view'
            ],
            [
                'title' => 'فئات المنتجات',
                'icon' => 'bi-tags',
                'url' => '/inventory/categories',
                'permission' => 'categories_view'
            ],
            [
                'title' => 'المخازن',
                'icon' => 'bi-building',
                'url' => '/inventory/warehouses',
                'permission' => 'warehouses_view'
            ],
            [
                'title' => 'حركات المخزون',
                'icon' => 'bi-arrow-left-right',
                'url' => '/inventory/movements',
                'permission' => 'movements_view'
            ],
            [
                'title' => 'الموردين',
                'icon' => 'bi-truck',
                'url' => '/inventory/suppliers',
                'permission' => 'suppliers_view'
            ],
            [
                'title' => 'طلبات الشراء',
                'icon' => 'bi-cart-plus',
                'url' => '/inventory/purchase-orders',
                'permission' => 'purchase_orders_view'
            ]
        ]
    ],
    [
        'title' => 'إدارة المبيعات',
        'icon' => 'bi-cart',
        'permission' => 'sales_view',
        'children' => [
            [
                'title' => 'العملاء',
                'icon' => 'bi-person-heart',
                'url' => '/customers',
                'permission' => 'customers_view'
            ],
            [
                'title' => 'عروض الأسعار',
                'icon' => 'bi-file-earmark-text',
                'url' => '/sales/quotations',
                'permission' => 'quotations_view'
            ],
            [
                'title' => 'أوامر البيع',
                'icon' => 'bi-cart-check',
                'url' => '/sales/orders',
                'permission' => 'sales_orders_view'
            ],
            [
                'title' => 'إشعارات التسليم',
                'icon' => 'bi-truck',
                'url' => '/sales/deliveries',
                'permission' => 'deliveries_view'
            ],
            [
                'title' => 'المرتجعات',
                'icon' => 'bi-arrow-return-left',
                'url' => '/sales/returns',
                'permission' => 'returns_view'
            ]
        ]
    ],
    [
        'title' => 'التقارير',
        'icon' => 'bi-graph-up-arrow',
        'permission' => 'reports_view',
        'children' => [
            [
                'title' => 'تقارير المبيعات',
                'icon' => 'bi-bar-chart',
                'url' => '/reports/sales',
                'permission' => 'sales_reports_view'
            ],
            [
                'title' => 'تقارير المخزون',
                'icon' => 'bi-boxes',
                'url' => '/reports/inventory',
                'permission' => 'inventory_reports_view'
            ],
            [
                'title' => 'تقارير العملاء',
                'icon' => 'bi-people',
                'url' => '/reports/customers',
                'permission' => 'customer_reports_view'
            ],
            [
                'title' => 'تقارير مخصصة',
                'icon' => 'bi-gear',
                'url' => '/reports/custom',
                'permission' => 'custom_reports_view'
            ]
        ]
    ],
    [
        'title' => 'الإعدادات',
        'icon' => 'bi-gear',
        'permission' => 'settings_view',
        'children' => [
            [
                'title' => 'إعدادات الشركة',
                'icon' => 'bi-building',
                'url' => '/settings/company',
                'permission' => 'company_settings_view'
            ],
            [
                'title' => 'إعدادات النظام',
                'icon' => 'bi-sliders',
                'url' => '/settings/system',
                'permission' => 'system_settings_view'
            ],
            [
                'title' => 'النسخ الاحتياطي',
                'icon' => 'bi-cloud-download',
                'url' => '/settings/backup',
                'permission' => 'backup_view'
            ],
            [
                'title' => 'السجلات',
                'icon' => 'bi-file-text',
                'url' => '/settings/logs',
                'permission' => 'logs_view'
            ]
        ]
    ]
];

/**
 * التحقق من الصلاحية
 */
function hasPermission($permission) {
    // في وضع التطوير، إرجاع true دائماً
    if (file_exists(dirname(__DIR__, 2) . '/config/development.php')) {
        $devConfig = include dirname(__DIR__, 2) . '/config/development.php';
        if ($devConfig['disable_auth_check'] ?? false) {
            return true;
        }
    }
    
    // التحقق الفعلي من الصلاحية
    global $auth;
    return $auth && $auth->hasPermission($permission);
}

/**
 * التحقق من النشاط
 */
function isActive($url) {
    global $currentRoute;
    return strpos($currentRoute, $url) === 0;
}

/**
 * عرض عنصر القائمة
 */
function renderMenuItem($item, $level = 0) {
    // التحقق من الصلاحية
    if (isset($item['permission']) && !hasPermission($item['permission'])) {
        return;
    }
    
    $hasChildren = isset($item['children']) && !empty($item['children']);
    $isActive = isset($item['url']) && isActive($item['url']);
    $hasActiveChild = false;
    
    // التحقق من وجود عنصر فرعي نشط
    if ($hasChildren) {
        foreach ($item['children'] as $child) {
            if (isset($child['url']) && isActive($child['url'])) {
                $hasActiveChild = true;
                break;
            }
        }
    }
    
    $activeClass = ($isActive || $hasActiveChild) ? 'active' : '';
    $expandedClass = $hasActiveChild ? 'show' : '';
    
    echo '<li class="nav-item">';
    
    if ($hasChildren) {
        // عنصر مع قائمة فرعية
        echo '<a class="nav-link ' . $activeClass . '" data-bs-toggle="collapse" href="#menu-' . md5($item['title']) . '" role="button" aria-expanded="' . ($hasActiveChild ? 'true' : 'false') . '">';
        echo '<i class="' . $item['icon'] . ' me-2"></i>';
        echo '<span class="nav-text">' . htmlspecialchars($item['title']) . '</span>';
        echo '<i class="bi bi-chevron-down ms-auto"></i>';
        echo '</a>';
        
        echo '<div class="collapse ' . $expandedClass . '" id="menu-' . md5($item['title']) . '">';
        echo '<ul class="nav flex-column ms-3">';
        
        foreach ($item['children'] as $child) {
            renderMenuItem($child, $level + 1);
        }
        
        echo '</ul>';
        echo '</div>';
    } else {
        // عنصر بسيط
        $url = $item['url'] ?? '#';
        echo '<a class="nav-link ' . $activeClass . '" href="' . htmlspecialchars($url) . '">';
        echo '<i class="' . $item['icon'] . ' me-2"></i>';
        echo '<span class="nav-text">' . htmlspecialchars($item['title']) . '</span>';
        echo '</a>';
    }
    
    echo '</li>';
}
?>

<ul class="nav flex-column">
    <?php foreach ($menuItems as $item): ?>
        <?php renderMenuItem($item); ?>
    <?php endforeach; ?>
</ul>

<style>
.nav-item .nav-link {
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-1);
    transition: var(--transition-all);
}

.nav-item .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(-3px);
}

.nav-item .nav-link.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    box-shadow: var(--shadow-sm);
}

.nav-item .collapse .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
}

.nav-item .collapse .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateX(-2px);
}

.nav-item .bi-chevron-down {
    transition: transform 0.3s ease;
}

.nav-item .nav-link[aria-expanded="true"] .bi-chevron-down {
    transform: rotate(180deg);
}
</style>
