/**
 * تحسينات لوحة التحكم المتقدمة
 * Advanced Dashboard Enhancements
 */

// تهيئة التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboardEnhancements();
});

function initializeDashboardEnhancements() {
    // تفعيل تأثيرات البطاقات
    enhanceStatsCards();
    
    // تفعيل البحث السريع
    initializeQuickSearch();
    
    // تفعيل الإشعارات المباشرة
    initializeLiveNotifications();
    
    // تفعيل تحديث البيانات التلقائي
    initializeAutoRefresh();
    
    // تفعيل اختصارات لوحة المفاتيح
    initializeKeyboardShortcuts();
}

// تحسين البطاقات الإحصائية
function enhanceStatsCards() {
    const statsCards = document.querySelectorAll('.stats-card');
    
    statsCards.forEach((card, index) => {
        // تأثير الهوفر المحسن
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.03)';
            this.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
            
            // تأثير الضوء
            const overlay = this.querySelector('.hover-overlay');
            if (overlay) {
                overlay.style.opacity = '0.1';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            
            const overlay = this.querySelector('.hover-overlay');
            if (overlay) {
                overlay.style.opacity = '0';
            }
        });
        
        // تأثير النقر
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown') && !e.target.closest('.btn')) {
                const link = this.querySelector('a[href]');
                if (link) {
                    // تأثير الضغط
                    this.style.transform = 'translateY(-8px) scale(0.98)';
                    setTimeout(() => {
                        window.location.href = link.href;
                    }, 150);
                }
            }
        });
        
        // تحريك البطاقة عند الظهور
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
}

// البحث السريع المحسن
function initializeQuickSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            hideSearchResults();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            performQuickSearch(query);
        }, 300);
    });
    
    // إخفاء النتائج عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-box')) {
            hideSearchResults();
        }
    });
}

function performQuickSearch(query) {
    // محاكاة البحث - في الإنتاج يجب استخدام API
    const mockResults = [
        { title: 'العملاء', url: '/customers', icon: 'bi-people' },
        { title: 'المنتجات', url: '/products', icon: 'bi-box' },
        { title: 'الطلبات', url: '/orders', icon: 'bi-cart' },
        { title: 'التقارير', url: '/reports', icon: 'bi-graph-up' },
        { title: 'الإعدادات', url: '/settings', icon: 'bi-gear' }
    ].filter(item => item.title.includes(query));
    
    showSearchResults(mockResults);
}

function showSearchResults(results) {
    let resultsContainer = document.getElementById('searchResults');
    
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'searchResults';
        resultsContainer.className = 'search-results position-absolute bg-white border rounded shadow-lg';
        resultsContainer.style.cssText = `
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 5px;
        `;
        
        const searchBox = document.querySelector('.search-box');
        searchBox.style.position = 'relative';
        searchBox.appendChild(resultsContainer);
    }
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="p-3 text-muted text-center">لا توجد نتائج</div>';
    } else {
        resultsContainer.innerHTML = results.map(result => `
            <a href="${result.url}" class="d-block p-3 text-decoration-none border-bottom search-result-item">
                <i class="${result.icon} me-2"></i>
                ${result.title}
            </a>
        `).join('');
    }
    
    resultsContainer.style.display = 'block';
}

function hideSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

// الإشعارات المباشرة
function initializeLiveNotifications() {
    // محاكاة الإشعارات المباشرة
    setInterval(() => {
        if (Math.random() < 0.1) { // 10% احتمال كل 30 ثانية
            showLiveNotification();
        }
    }, 30000);
}

function showLiveNotification() {
    const notifications = [
        { title: 'طلب جديد', message: 'تم استلام طلب جديد من العميل أحمد محمد', type: 'info' },
        { title: 'مخزون منخفض', message: 'المنتج "لابتوب ديل" وصل إلى الحد الأدنى', type: 'warning' },
        { title: 'دفعة جديدة', message: 'تم استلام دفعة بقيمة 15,000 ر.س', type: 'success' }
    ];
    
    const notification = notifications[Math.floor(Math.random() * notifications.length)];
    
    // إنشاء عنصر الإشعار
    const notificationEl = document.createElement('div');
    notificationEl.className = `alert alert-${notification.type} alert-dismissible fade show position-fixed`;
    notificationEl.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notificationEl.innerHTML = `
        <strong>${notification.title}</strong><br>
        ${notification.message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notificationEl);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notificationEl.parentNode) {
            notificationEl.remove();
        }
    }, 5000);
    
    // تحديث عداد الإشعارات
    updateNotificationBadge();
}

function updateNotificationBadge() {
    const badge = document.querySelector('.btn[data-bs-toggle="dropdown"] .badge');
    if (badge) {
        const currentCount = parseInt(badge.textContent) || 0;
        badge.textContent = currentCount + 1;
        
        // تأثير النبض
        badge.style.animation = 'pulse 0.5s ease-in-out';
        setTimeout(() => {
            badge.style.animation = '';
        }, 500);
    }
}

// تحديث البيانات التلقائي
function initializeAutoRefresh() {
    // تحديث البيانات كل 5 دقائق
    setInterval(() => {
        refreshDashboardData();
    }, 300000);
}

function refreshDashboardData() {
    // محاكاة تحديث البيانات
    const counters = document.querySelectorAll('[data-counter]');
    
    counters.forEach(counter => {
        const currentValue = parseInt(counter.textContent.replace(/,/g, ''));
        const change = Math.floor(Math.random() * 10) - 5; // تغيير عشوائي
        const newValue = Math.max(0, currentValue + change);
        
        // تحريك العداد للقيمة الجديدة
        animateCounterUpdate(counter, newValue);
    });
    
    console.log('تم تحديث بيانات لوحة التحكم');
}

function animateCounterUpdate(counter, newValue) {
    const currentValue = parseInt(counter.textContent.replace(/,/g, ''));
    const difference = newValue - currentValue;
    const steps = 20;
    const stepValue = difference / steps;
    let step = 0;
    
    const timer = setInterval(() => {
        step++;
        const value = Math.round(currentValue + (stepValue * step));
        
        if (newValue > 1000) {
            counter.textContent = new Intl.NumberFormat('ar-SA').format(value);
        } else {
            counter.textContent = value;
        }
        
        if (step >= steps) {
            clearInterval(timer);
            counter.setAttribute('data-counter', newValue);
        }
    }, 50);
}

// اختصارات لوحة المفاتيح
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K للبحث السريع
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Ctrl/Cmd + D للوحة التحكم
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            window.location.href = '/dashboard';
        }
        
        // Escape لإغلاق البحث
        if (e.key === 'Escape') {
            hideSearchResults();
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.blur();
            }
        }
    });
}

// تأثيرات CSS إضافية
const additionalStyles = `
    .search-result-item:hover {
        background-color: #f8f9fa;
        transform: translateX(-5px);
        transition: all 0.2s ease;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }
    
    .stats-card {
        cursor: pointer;
    }
    
    .current-time {
        font-family: 'Courier New', monospace;
    }
`;

// إضافة الأنماط للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
