/**
 * تحسينات الأداء للرسوم المتحركة
 * Performance Optimizations for Animations
 */

/* تحسين الأداء العام */
* {
    /* تحسين الرسم */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* تحسين التمرير */
    -webkit-overflow-scrolling: touch;
}

/* تحسين العناصر المتحركة */
.stats-card,
.progress-bar,
.search-results,
.notification-badge,
.chart-container {
    /* تحسين الطبقات */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    
    /* تحسين الرسم */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    
    /* تحسين المنظور */
    perspective: 1000px;
    -webkit-perspective: 1000px;
}

/* تحسين الرسوم المتحركة للأداء */
@keyframes optimizedSlideDown {
    from {
        opacity: 0;
        transform: translate3d(0, -10px, 0);
        will-change: transform, opacity;
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        will-change: auto;
    }
}

@keyframes optimizedPulse {
    0%, 100% { 
        transform: scale3d(1, 1, 1);
        will-change: transform;
    }
    50% { 
        transform: scale3d(1.1, 1.1, 1);
        will-change: transform;
    }
}

@keyframes optimizedShimmer {
    0% { 
        transform: translate3d(-100%, 0, 0);
        will-change: transform;
    }
    100% { 
        transform: translate3d(100%, 0, 0);
        will-change: auto;
    }
}

@keyframes optimizedCountUp {
    from { 
        opacity: 0; 
        transform: translate3d(0, 20px, 0) scale3d(0.8, 0.8, 1);
        will-change: transform, opacity;
    }
    to { 
        opacity: 1; 
        transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
        will-change: auto;
    }
}

@keyframes optimizedBackgroundMove {
    0%, 100% { 
        transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
        will-change: transform;
    }
    33% { 
        transform: translate3d(-30px, -50px, 0) scale3d(1.1, 1.1, 1);
        will-change: transform;
    }
    66% { 
        transform: translate3d(20px, 30px, 0) scale3d(0.9, 0.9, 1);
        will-change: transform;
    }
}

/* تحسين الهوفر للأداء */
.stats-card-optimized {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
}

.stats-card-optimized:hover {
    transform: translate3d(0, -8px, 0) scale3d(1.02, 1.02, 1);
    will-change: transform, box-shadow;
}

.stats-card-optimized:not(:hover) {
    will-change: auto;
}

/* تحسين التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* تحسين الخطوط */
.optimized-text {
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
}

/* تحسين الصور */
.optimized-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transform: translateZ(0);
}

/* تحسين الجداول */
.optimized-table {
    contain: layout style paint;
    will-change: scroll-position;
}

/* تحسين القوائم */
.optimized-dropdown {
    contain: layout style;
    transform: translateZ(0);
}

/* تحسين النماذج */
.optimized-form {
    contain: layout style;
}

.optimized-input {
    will-change: border-color, box-shadow;
}

.optimized-input:focus {
    will-change: border-color, box-shadow;
}

.optimized-input:not(:focus) {
    will-change: auto;
}

/* تحسين الأزرار */
.optimized-button {
    contain: layout style;
    will-change: transform, background-color;
}

.optimized-button:hover {
    will-change: transform, background-color;
}

.optimized-button:not(:hover) {
    will-change: auto;
}

/* تحسين الشبكة */
.optimized-grid {
    contain: layout;
    will-change: contents;
}

/* تحسين الفليكس */
.optimized-flex {
    contain: layout style;
}

/* تحسين الموضع المطلق */
.optimized-absolute {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الموضع الثابت */
.optimized-fixed {
    contain: layout style paint;
    transform: translateZ(0);
    will-change: transform;
}

/* تحسين الظلال */
.optimized-shadow {
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
    will-change: filter;
}

/* تحسين التدرجات */
.optimized-gradient {
    background-attachment: fixed;
    will-change: background-position;
}

/* تحسين الشفافية */
.optimized-opacity {
    will-change: opacity;
}

/* تحسين التحويلات */
.optimized-transform {
    will-change: transform;
    transform: translateZ(0);
}

/* تحسين الفلاتر */
.optimized-filter {
    will-change: filter;
}

/* تحسين الحدود */
.optimized-border {
    will-change: border-color;
}

/* تحسين الخلفية */
.optimized-background {
    will-change: background-color;
}

/* تحسين النص */
.optimized-text-color {
    will-change: color;
}

/* تحسين الحجم */
.optimized-size {
    will-change: width, height;
}

/* تحسين الهوامش */
.optimized-margin {
    will-change: margin;
}

/* تحسين الحشو */
.optimized-padding {
    will-change: padding;
}

/* إعادة تعيين will-change بعد انتهاء الرسوم المتحركة */
.animation-complete {
    will-change: auto !important;
}

/* تحسين الطباعة */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
        filter: none !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
    }
}

/* تحسين الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .optimized-image {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* تحسين الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسين الذاكرة */
.memory-optimized {
    contain: strict;
    content-visibility: auto;
    contain-intrinsic-size: 300px;
}

/* تحسين الرسم */
.paint-optimized {
    contain: paint;
    isolation: isolate;
}

/* تحسين التخطيط */
.layout-optimized {
    contain: layout;
}

/* تحسين النمط */
.style-optimized {
    contain: style;
}

/* تحسين شامل */
.fully-optimized {
    contain: layout style paint;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
