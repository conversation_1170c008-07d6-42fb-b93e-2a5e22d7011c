<?php
/**
 * LogoutController
 * تحكم في تسجيل الخروج
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Session.php';

class LogoutController extends Controller
{
    private $auth;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
    }

    /**
     * تسجيل الخروج
     */
    public function logout()
    {
        try {
            // تسجيل النشاط قبل تسجيل الخروج
            if ($this->auth->check()) {
                $userId = $this->auth->getUserId();
                // يمكن إضافة تسجيل نشاط تسجيل الخروج هنا
            }

            // تسجيل الخروج
            $this->auth->logout();

            // رسالة نجاح
            Session::flash('success', 'تم تسجيل الخروج بنجاح');

            // إعادة التوجيه إلى صفحة تسجيل الدخول
            $this->redirect('/login');

        } catch (Exception $e) {
            // في حالة الخطأ، إعادة التوجيه إلى صفحة تسجيل الدخول
            Session::flash('error', 'حدث خطأ أثناء تسجيل الخروج');
            $this->redirect('/login');
        }
    }
}
?>
