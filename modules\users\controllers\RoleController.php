<?php
/**
 * SeaSystem Role Management Controller
 * متحكم إدارة الأدوار والصلاحيات
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(dirname(dirname(__DIR__))) . '/core/RBAC.php';
require_once dirname(dirname(dirname(__DIR__))) . '/core/Middleware.php';

class RoleController
{
    private $rbac;
    private $middleware;

    public function __construct()
    {
        $this->rbac = new RBAC();
        $this->middleware = new Middleware();
    }

    /**
     * عرض قائمة الأدوار
     */
    public function index()
    {
        // التحقق من الصلاحيات
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $roles = $this->rbac->getAllRoles();
            
            $this->render('roles/index', [
                'title' => 'إدارة الأدوار - SeaSystem',
                'roles' => $roles,
                'success' => Session::getFlash('success'),
                'error' => Session::getFlash('error')
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * عرض تفاصيل دور
     */
    public function show($id)
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $role = $this->getRoleById($id);
            if (!$role) {
                throw new Exception('الدور غير موجود');
            }

            $permissions = $this->rbac->getRolePermissions($id);
            $allPermissions = $this->rbac->getAllPermissions();
            
            // تجميع الصلاحيات حسب الوحدة
            $permissionsByModule = [];
            foreach ($allPermissions as $permission) {
                $permissionsByModule[$permission['module']][] = $permission;
            }

            $this->render('roles/show', [
                'title' => 'تفاصيل الدور - SeaSystem',
                'role' => $role,
                'permissions' => $permissions,
                'allPermissions' => $allPermissions,
                'permissionsByModule' => $permissionsByModule,
                'success' => Session::getFlash('success'),
                'error' => Session::getFlash('error')
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * عرض نموذج إنشاء دور جديد
     */
    public function create()
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $allPermissions = $this->rbac->getAllPermissions();
            
            // تجميع الصلاحيات حسب الوحدة
            $permissionsByModule = [];
            foreach ($allPermissions as $permission) {
                $permissionsByModule[$permission['module']][] = $permission;
            }

            $this->render('roles/create', [
                'title' => 'إنشاء دور جديد - SeaSystem',
                'permissionsByModule' => $permissionsByModule,
                'error' => Session::getFlash('error')
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * حفظ دور جديد
     */
    public function store()
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        // التحقق من CSRF والتحقق من البيانات
        if (!$this->middleware->verifyCsrf() || 
            !$this->middleware->validateInput([
                'name' => 'required|min:3|max:50',
                'display_name' => 'required|min:3|max:100'
            ])) {
            return;
        }

        try {
            $data = [
                'name' => trim($_POST['name']),
                'display_name' => trim($_POST['display_name']),
                'description' => trim($_POST['description'] ?? ''),
                'is_active' => isset($_POST['is_active'])
            ];

            // التحقق من عدم تكرار اسم الدور
            if ($this->roleNameExists($data['name'])) {
                throw new Exception('اسم الدور موجود مسبقاً');
            }

            $roleId = $this->rbac->createRole($data);
            
            if (!$roleId) {
                throw new Exception('فشل في إنشاء الدور');
            }

            // تعيين الصلاحيات
            $permissions = $_POST['permissions'] ?? [];
            foreach ($permissions as $permissionId) {
                $this->rbac->assignPermissionToRole($roleId, $permissionId);
            }

            Session::flash('success', 'تم إنشاء الدور بنجاح');
            $this->redirect('/roles/' . $roleId);

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/roles/create');
        }
    }

    /**
     * عرض نموذج تعديل دور
     */
    public function edit($id)
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $role = $this->getRoleById($id);
            if (!$role) {
                throw new Exception('الدور غير موجود');
            }

            $rolePermissions = $this->rbac->getRolePermissions($id);
            $allPermissions = $this->rbac->getAllPermissions();
            
            // تجميع الصلاحيات حسب الوحدة
            $permissionsByModule = [];
            foreach ($allPermissions as $permission) {
                $permissionsByModule[$permission['module']][] = $permission;
            }

            // قائمة معرفات الصلاحيات المعينة للدور
            $assignedPermissionIds = array_column($rolePermissions, 'id');

            $this->render('roles/edit', [
                'title' => 'تعديل الدور - SeaSystem',
                'role' => $role,
                'permissionsByModule' => $permissionsByModule,
                'assignedPermissionIds' => $assignedPermissionIds,
                'error' => Session::getFlash('error')
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * تحديث دور
     */
    public function update($id)
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        if (!$this->middleware->verifyCsrf() || 
            !$this->middleware->validateInput([
                'name' => 'required|min:3|max:50',
                'display_name' => 'required|min:3|max:100'
            ])) {
            return;
        }

        try {
            $role = $this->getRoleById($id);
            if (!$role) {
                throw new Exception('الدور غير موجود');
            }

            $data = [
                'name' => trim($_POST['name']),
                'display_name' => trim($_POST['display_name']),
                'description' => trim($_POST['description'] ?? ''),
                'is_active' => isset($_POST['is_active'])
            ];

            // التحقق من عدم تكرار اسم الدور (عدا الدور الحالي)
            if ($this->roleNameExists($data['name'], $id)) {
                throw new Exception('اسم الدور موجود مسبقاً');
            }

            // تحديث الدور
            $db = Database::getInstance();
            $db->update('roles', $data, ['id' => $id]);

            // تحديث الصلاحيات
            $this->updateRolePermissions($id, $_POST['permissions'] ?? []);

            Session::flash('success', 'تم تحديث الدور بنجاح');
            $this->redirect('/roles/' . $id);

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/roles/' . $id . '/edit');
        }
    }

    /**
     * حذف دور
     */
    public function destroy($id)
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        if (!$this->middleware->verifyCsrf()) {
            return;
        }

        try {
            $role = $this->getRoleById($id);
            if (!$role) {
                throw new Exception('الدور غير موجود');
            }

            // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
            $db = Database::getInstance();
            $userCount = $db->selectOne(
                "SELECT COUNT(*) as count FROM user_roles WHERE role_id = ?",
                [$id]
            );

            if ($userCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الدور لأنه مرتبط بمستخدمين');
            }

            // حذف صلاحيات الدور
            $db->delete('role_permissions', ['role_id' => $id]);
            
            // حذف الدور
            $db->delete('roles', ['id' => $id]);

            Session::flash('success', 'تم حذف الدور بنجاح');
            $this->redirect('/roles');

        } catch (Exception $e) {
            Session::flash('error', $e->getMessage());
            $this->redirect('/roles');
        }
    }

    /**
     * تعيين دور لمستخدم (AJAX)
     */
    public function assignToUser()
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $userId = intval($_POST['user_id'] ?? 0);
            $roleId = intval($_POST['role_id'] ?? 0);

            if (!$userId || !$roleId) {
                throw new Exception('بيانات غير صحيحة');
            }

            if ($this->rbac->assignRole($userId, $roleId)) {
                $this->jsonResponse(['success' => true, 'message' => 'تم تعيين الدور بنجاح']);
            } else {
                throw new Exception('فشل في تعيين الدور');
            }

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * إزالة دور من مستخدم (AJAX)
     */
    public function removeFromUser()
    {
        if (!$this->middleware->requirePermission('users.manage_roles')) {
            return;
        }

        try {
            $userId = intval($_POST['user_id'] ?? 0);
            $roleId = intval($_POST['role_id'] ?? 0);

            if (!$userId || !$roleId) {
                throw new Exception('بيانات غير صحيحة');
            }

            if ($this->rbac->removeRole($userId, $roleId)) {
                $this->jsonResponse(['success' => true, 'message' => 'تم إزالة الدور بنجاح']);
            } else {
                throw new Exception('فشل في إزالة الدور');
            }

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * الحصول على دور بالمعرف
     */
    private function getRoleById($id)
    {
        $db = Database::getInstance();
        return $db->selectOne("SELECT * FROM roles WHERE id = ?", [$id]);
    }

    /**
     * التحقق من وجود اسم الدور
     */
    private function roleNameExists($name, $excludeId = null)
    {
        $db = Database::getInstance();
        $query = "SELECT id FROM roles WHERE name = ?";
        $params = [$name];

        if ($excludeId) {
            $query .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $db->selectOne($query, $params);
        return !empty($result);
    }

    /**
     * تحديث صلاحيات الدور
     */
    private function updateRolePermissions($roleId, $newPermissions)
    {
        $db = Database::getInstance();
        
        // الحصول على الصلاحيات الحالية
        $currentPermissions = $this->rbac->getRolePermissions($roleId);
        $currentPermissionIds = array_column($currentPermissions, 'id');
        
        // الصلاحيات المراد إضافتها
        $toAdd = array_diff($newPermissions, $currentPermissionIds);
        
        // الصلاحيات المراد إزالتها
        $toRemove = array_diff($currentPermissionIds, $newPermissions);
        
        // إضافة الصلاحيات الجديدة
        foreach ($toAdd as $permissionId) {
            $this->rbac->assignPermissionToRole($roleId, $permissionId);
        }
        
        // إزالة الصلاحيات غير المطلوبة
        foreach ($toRemove as $permissionId) {
            $this->rbac->removePermissionFromRole($roleId, $permissionId);
        }
    }

    /**
     * عرض الصفحة
     */
    private function render($view, $data = [])
    {
        extract($data);
        include dirname(__DIR__) . "/views/{$view}.php";
    }

    /**
     * إعادة التوجيه
     */
    private function redirect($url)
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * إرجاع استجابة JSON
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * معالجة الأخطاء
     */
    private function handleError($exception)
    {
        error_log($exception->getMessage());
        echo "حدث خطأ: " . $exception->getMessage();
    }
}
