<?php
/**
 * SeaSystem Base Model Class
 * الكلاس الأساسي للنماذج
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Database.php';

abstract class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $guarded = ['id', 'created_at', 'updated_at'];
    protected $timestamps = true;
    protected $dateFormat = 'Y-m-d H:i:s';
    
    // خصائص للتحقق من صحة البيانات
    protected $rules = [];
    protected $messages = [];
    
    // خصائص للبيانات
    protected $attributes = [];
    protected $original = [];
    protected $changes = [];
    
    /**
     * منشئ الكلاس
     * 
     * @param array $attributes البيانات الأولية
     */
    public function __construct($attributes = [])
    {
        $this->db = Database::getInstance();
        $this->fill($attributes);
    }

    /**
     * ملء النموذج بالبيانات
     * 
     * @param array $attributes البيانات
     * @return $this
     */
    public function fill($attributes)
    {
        foreach ($attributes as $key => $value) {
            if ($this->isFillable($key)) {
                $this->setAttribute($key, $value);
            }
        }
        return $this;
    }

    /**
     * التحقق من إمكانية ملء الحقل
     * 
     * @param string $key اسم الحقل
     * @return bool
     */
    protected function isFillable($key)
    {
        // إذا كان fillable فارغ، كل الحقول مسموحة عدا المحمية
        if (empty($this->fillable)) {
            return !in_array($key, $this->guarded);
        }
        
        return in_array($key, $this->fillable);
    }

    /**
     * تعيين قيمة خاصية
     * 
     * @param string $key اسم الخاصية
     * @param mixed $value القيمة
     */
    public function setAttribute($key, $value)
    {
        $this->attributes[$key] = $value;
    }

    /**
     * الحصول على قيمة خاصية
     * 
     * @param string $key اسم الخاصية
     * @return mixed
     */
    public function getAttribute($key)
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * الحصول على جميع الخصائص
     * 
     * @return array
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * البحث عن سجل بالمعرف
     * 
     * @param int $id المعرف
     * @return static|null
     */
    public static function find($id)
    {
        $instance = new static();
        $result = $instance->db->selectOne(
            "SELECT * FROM {$instance->table} WHERE {$instance->primaryKey} = ?",
            [$id]
        );
        
        if ($result) {
            $model = new static($result);
            $model->original = $result;
            return $model;
        }
        
        return null;
    }

    /**
     * البحث عن سجل بالمعرف أو إرجاع خطأ
     * 
     * @param int $id المعرف
     * @return static
     * @throws Exception
     */
    public static function findOrFail($id)
    {
        $model = static::find($id);
        if (!$model) {
            throw new Exception("السجل غير موجود");
        }
        return $model;
    }

    /**
     * الحصول على جميع السجلات
     * 
     * @param array $columns الأعمدة المطلوبة
     * @return array
     */
    public static function all($columns = ['*'])
    {
        $instance = new static();
        $columnsStr = implode(', ', $columns);
        $results = $instance->db->select("SELECT {$columnsStr} FROM {$instance->table}");
        
        $models = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->original = $result;
            $models[] = $model;
        }
        
        return $models;
    }

    /**
     * البحث بشروط
     * 
     * @param array $conditions الشروط
     * @param array $columns الأعمدة
     * @return array
     */
    public static function where($conditions, $columns = ['*'])
    {
        $instance = new static();
        $columnsStr = implode(', ', $columns);
        
        $whereClause = [];
        $params = [];
        foreach ($conditions as $key => $value) {
            $whereClause[] = "{$key} = ?";
            $params[] = $value;
        }
        
        $query = "SELECT {$columnsStr} FROM {$instance->table} WHERE " . implode(' AND ', $whereClause);
        $results = $instance->db->select($query, $params);
        
        $models = [];
        foreach ($results as $result) {
            $model = new static($result);
            $model->original = $result;
            $models[] = $model;
        }
        
        return $models;
    }

    /**
     * حفظ النموذج
     * 
     * @return bool
     * @throws Exception
     */
    public function save()
    {
        // التحقق من صحة البيانات
        if (!$this->validate()) {
            return false;
        }

        try {
            $this->db->beginTransaction();
            
            if ($this->exists()) {
                $result = $this->performUpdate();
            } else {
                $result = $this->performInsert();
            }
            
            $this->db->commit();
            
            // تحديث البيانات الأصلية
            $this->original = $this->attributes;
            $this->changes = [];
            
            return $result;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * التحقق من وجود السجل
     * 
     * @return bool
     */
    public function exists()
    {
        return isset($this->attributes[$this->primaryKey]) && 
               !empty($this->attributes[$this->primaryKey]);
    }

    /**
     * تنفيذ عملية الإدراج
     * 
     * @return bool
     */
    protected function performInsert()
    {
        $data = $this->getAttributesForInsert();
        
        if ($this->timestamps) {
            $now = date($this->dateFormat);
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
        }
        
        $id = $this->db->insert($this->table, $data);
        $this->setAttribute($this->primaryKey, $id);
        
        return true;
    }

    /**
     * تنفيذ عملية التحديث
     * 
     * @return bool
     */
    protected function performUpdate()
    {
        $data = $this->getAttributesForUpdate();
        
        if (empty($data)) {
            return true; // لا توجد تغييرات
        }
        
        if ($this->timestamps) {
            $data['updated_at'] = date($this->dateFormat);
        }
        
        $where = [$this->primaryKey => $this->attributes[$this->primaryKey]];
        $this->db->update($this->table, $data, $where);
        
        return true;
    }

    /**
     * الحصول على البيانات للإدراج
     * 
     * @return array
     */
    protected function getAttributesForInsert()
    {
        $data = [];
        foreach ($this->attributes as $key => $value) {
            if (!in_array($key, $this->guarded)) {
                $data[$key] = $value;
            }
        }
        return $data;
    }

    /**
     * الحصول على البيانات للتحديث
     * 
     * @return array
     */
    protected function getAttributesForUpdate()
    {
        $data = [];
        foreach ($this->attributes as $key => $value) {
            if (!in_array($key, $this->guarded) && 
                (!isset($this->original[$key]) || $this->original[$key] !== $value)) {
                $data[$key] = $value;
            }
        }
        return $data;
    }

    /**
     * حذف النموذج
     * 
     * @return bool
     * @throws Exception
     */
    public function delete()
    {
        if (!$this->exists()) {
            throw new Exception("لا يمكن حذف سجل غير موجود");
        }
        
        try {
            $where = [$this->primaryKey => $this->attributes[$this->primaryKey]];
            $result = $this->db->delete($this->table, $where);
            
            return $result > 0;
            
        } catch (Exception $e) {
            throw new Exception("خطأ في حذف السجل: " . $e->getMessage());
        }
    }

    /**
     * التحقق من صحة البيانات
     * 
     * @return bool
     */
    protected function validate()
    {
        // يمكن تطوير نظام التحقق هنا
        return true;
    }

    /**
     * تحويل النموذج إلى مصفوفة
     * 
     * @return array
     */
    public function toArray()
    {
        return $this->attributes;
    }

    /**
     * تحويل النموذج إلى JSON
     * 
     * @return string
     */
    public function toJson()
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }

    /**
     * الوصول السحري للخصائص
     * 
     * @param string $key
     * @return mixed
     */
    public function __get($key)
    {
        return $this->getAttribute($key);
    }

    /**
     * التعيين السحري للخصائص
     * 
     * @param string $key
     * @param mixed $value
     */
    public function __set($key, $value)
    {
        $this->setAttribute($key, $value);
    }

    /**
     * التحقق من وجود خاصية
     * 
     * @param string $key
     * @return bool
     */
    public function __isset($key)
    {
        return isset($this->attributes[$key]);
    }

    /**
     * إلغاء تعيين خاصية
     * 
     * @param string $key
     */
    public function __unset($key)
    {
        unset($this->attributes[$key]);
    }
}
