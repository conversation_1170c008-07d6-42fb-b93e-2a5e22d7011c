<?php
/**
 * SeaSystem Application Configuration
 * إعدادات التطبيق العامة
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(__DIR__) . '/core/Environment.php';

// تحميل متغيرات البيئة
Environment::load();

return [
    // معلومات التطبيق الأساسية
    'name' => Environment::get('APP_NAME', 'SeaSystem ERP'),
    'version' => '1.0.0',
    'description' => 'نظام تخطيط موارد المؤسسات',
    'author' => 'SeaSystem Development Team',
    
    // إعدادات البيئة
    'env' => Environment::get('APP_ENV', 'production'),
    'debug' => Environment::get('APP_DEBUG', false),
    'url' => Environment::get('APP_URL', 'http://localhost'),
    'timezone' => Environment::get('APP_TIMEZONE', 'Asia/Riyadh'),
    
    // إعدادات الأمان
    'key' => Environment::get('APP_KEY', ''),
    'cipher' => 'AES-256-CBC',
    
    // إعدادات قاعدة البيانات
    'database' => [
        'default' => 'mysql',
        'connections' => [
            'mysql' => [
                'driver' => 'mysql',
                'host' => Environment::get('DB_HOST', 'localhost'),
                'port' => Environment::get('DB_PORT', 3306),
                'database' => Environment::get('DB_NAME', 'R1'),
                'username' => Environment::get('DB_USERNAME', 'root'),
                'password' => Environment::get('DB_PASSWORD', ''),
                'charset' => Environment::get('DB_CHARSET', 'utf8mb4'),
                'collation' => 'utf8mb4_unicode_ci',
                'prefix' => '',
                'strict' => true,
                'engine' => 'InnoDB',
            ]
        ]
    ],
    
    // إعدادات الجلسة
    'session' => [
        'driver' => 'file',
        'lifetime' => Environment::get('SESSION_LIFETIME', 3600),
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => dirname(__DIR__) . '/storage/sessions',
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => [
            'name' => 'seasystem_session',
            'path' => '/',
            'domain' => null,
            'secure' => Environment::get('SESSION_SECURE_COOKIE', false),
            'http_only' => true,
            'same_site' => 'lax',
        ],
    ],
    
    // إعدادات التسجيل
    'logging' => [
        'default' => 'file',
        'channels' => [
            'file' => [
                'driver' => 'file',
                'path' => dirname(__DIR__) . '/logs/app.log',
                'level' => Environment::get('LOG_LEVEL', 'info'),
                'max_files' => Environment::get('LOG_MAX_FILES', 30),
            ],
            'database' => [
                'driver' => 'database',
                'table' => 'activity_logs',
                'level' => 'info',
            ]
        ]
    ],
    
    // إعدادات الملفات
    'filesystems' => [
        'default' => 'local',
        'disks' => [
            'local' => [
                'driver' => 'local',
                'root' => dirname(__DIR__) . '/uploads',
                'url' => Environment::get('APP_URL') . '/uploads',
                'visibility' => 'public',
            ],
            'public' => [
                'driver' => 'local',
                'root' => dirname(__DIR__) . '/public/storage',
                'url' => Environment::get('APP_URL') . '/storage',
                'visibility' => 'public',
            ]
        ]
    ],
    
    // إعدادات البريد الإلكتروني
    'mail' => [
        'default' => 'smtp',
        'mailers' => [
            'smtp' => [
                'transport' => 'smtp',
                'host' => Environment::get('MAIL_HOST', 'smtp.gmail.com'),
                'port' => Environment::get('MAIL_PORT', 587),
                'encryption' => Environment::get('MAIL_ENCRYPTION', 'tls'),
                'username' => Environment::get('MAIL_USERNAME'),
                'password' => Environment::get('MAIL_PASSWORD'),
                'timeout' => null,
            ]
        ],
        'from' => [
            'address' => Environment::get('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'name' => Environment::get('MAIL_FROM_NAME', 'SeaSystem ERP'),
        ]
    ],
    
    // إعدادات الكاش
    'cache' => [
        'default' => 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => dirname(__DIR__) . '/storage/cache',
            ],
            'array' => [
                'driver' => 'array',
                'serialize' => false,
            ]
        ],
        'prefix' => 'seasystem_cache',
    ],
    
    // إعدادات الأداء
    'performance' => [
        'enable_compression' => true,
        'enable_caching' => true,
        'cache_duration' => 3600, // ساعة واحدة
        'enable_minification' => Environment::get('APP_ENV') === 'production',
        'enable_cdn' => false,
        'cdn_url' => '',
    ],
    
    // إعدادات الأمان
    'security' => [
        'password_min_length' => Environment::get('PASSWORD_MIN_LENGTH', 8),
        'max_login_attempts' => Environment::get('MAX_LOGIN_ATTEMPTS', 5),
        'lockout_duration' => Environment::get('LOCKOUT_DURATION', 900),
        'session_regenerate_interval' => 300,
        'csrf_token_lifetime' => 3600,
        'enable_rate_limiting' => true,
        'rate_limit_requests' => 60,
        'rate_limit_window' => 60,
        'enable_ip_whitelist' => false,
        'ip_whitelist' => [],
        'enable_2fa' => false,
    ],
    
    // إعدادات الشركة
    'company' => [
        'name' => Environment::get('COMPANY_NAME', 'شركة SeaSystem'),
        'address' => Environment::get('COMPANY_ADDRESS', ''),
        'phone' => Environment::get('COMPANY_PHONE', ''),
        'email' => Environment::get('COMPANY_EMAIL', ''),
        'website' => Environment::get('COMPANY_WEBSITE', ''),
        'logo' => Environment::get('COMPANY_LOGO', ''),
        'currency' => Environment::get('DEFAULT_CURRENCY', 'SAR'),
        'tax_rate' => Environment::get('TAX_RATE', 15),
        'fiscal_year_start' => Environment::get('FISCAL_YEAR_START', '01-01'),
    ],
    
    // إعدادات التطبيق
    'app_settings' => [
        'items_per_page' => 20,
        'max_upload_size' => Environment::get('UPLOAD_MAX_SIZE', 10485760), // 10MB
        'allowed_file_types' => explode(',', Environment::get('ALLOWED_FILE_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx')),
        'enable_notifications' => true,
        'enable_email_notifications' => true,
        'enable_sms_notifications' => false,
        'backup_frequency' => 'daily',
        'backup_retention_days' => 30,
    ],
    
    // إعدادات الوحدات
    'modules' => [
        'hr' => [
            'enabled' => true,
            'auto_generate_employee_code' => true,
            'employee_code_prefix' => 'EMP',
            'require_manager_approval' => true,
        ],
        'finance' => [
            'enabled' => true,
            'auto_generate_voucher_number' => true,
            'voucher_number_prefix' => 'JV',
            'require_approval' => true,
        ],
        'inventory' => [
            'enabled' => true,
            'auto_generate_product_code' => true,
            'product_code_prefix' => 'PRD',
            'enable_barcode' => true,
            'track_serial_numbers' => false,
        ],
        'sales' => [
            'enabled' => true,
            'auto_generate_order_number' => true,
            'order_number_prefix' => 'SO',
            'require_approval' => false,
            'enable_discounts' => true,
        ]
    ],
    
    // إعدادات API
    'api' => [
        'enabled' => true,
        'version' => 'v1',
        'rate_limit' => 1000, // طلب في الساعة
        'enable_cors' => true,
        'cors_origins' => ['*'],
        'enable_authentication' => true,
        'token_lifetime' => 3600,
    ],
    
    // إعدادات التطوير
    'development' => [
        'enable_debug_toolbar' => Environment::get('APP_ENV') === 'development',
        'enable_query_log' => Environment::get('ENABLE_QUERY_LOG', false),
        'show_errors' => Environment::get('SHOW_ERRORS', false),
        'error_reporting' => Environment::get('APP_ENV') === 'development' ? E_ALL : E_ERROR,
    ],
    
    // إعدادات النسخ الاحتياطي
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily', // daily, weekly, monthly
        'time' => '02:00', // وقت النسخ الاحتياطي
        'retention_days' => 30,
        'include_uploads' => true,
        'compression' => true,
        'encryption' => false,
        'storage_path' => dirname(__DIR__) . '/storage/backups',
    ],
    
    // إعدادات التقارير
    'reports' => [
        'default_format' => 'pdf',
        'supported_formats' => ['pdf', 'excel', 'csv'],
        'cache_reports' => true,
        'cache_duration' => 1800, // 30 دقيقة
        'max_records' => 10000,
        'enable_scheduling' => true,
    ]
];

/**
 * دالة مساعدة للحصول على إعدادات التطبيق
 * 
 * @param string $key المفتاح المطلوب
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function config($key = null, $default = null)
{
    static $config = null;
    
    if ($config === null) {
        $config = include __FILE__;
    }
    
    if ($key === null) {
        return $config;
    }
    
    $keys = explode('.', $key);
    $value = $config;
    
    foreach ($keys as $k) {
        if (is_array($value) && array_key_exists($k, $value)) {
            $value = $value[$k];
        } else {
            return $default;
        }
    }
    
    return $value;
}

/**
 * دالة للتحقق من تفعيل وحدة
 * 
 * @param string $module اسم الوحدة
 * @return bool
 */
function module_enabled($module)
{
    return config("modules.{$module}.enabled", false);
}

/**
 * دالة للحصول على إعدادات الشركة
 * 
 * @param string $key المفتاح المطلوب
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function company_setting($key, $default = null)
{
    return config("company.{$key}", $default);
}

/**
 * دالة للتحقق من بيئة التطبيق
 * 
 * @param string $env البيئة المطلوبة
 * @return bool
 */
function is_environment($env)
{
    return config('env') === $env;
}

/**
 * دالة للتحقق من تفعيل وضع التطوير
 * 
 * @return bool
 */
function is_debug_mode()
{
    return config('debug', false);
}
