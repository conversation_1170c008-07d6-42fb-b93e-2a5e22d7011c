<?php
/**
 * قائمة المنتجات
 */

// إعداد البيانات للقالب
$title = 'إدارة المنتجات';
$breadcrumbs = [
    ['title' => 'الرئيسية', 'url' => '/dashboard'],
    ['title' => 'إدارة المنتجات', 'url' => '/products']
];

// بدء المحتوى
ob_start();
?>

<!-- أدوات الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إدارة المنتجات</h1>
        <p class="text-muted">إدارة وتتبع جميع المنتجات في النظام</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="bi bi-upload me-2"></i>
            استيراد منتجات
        </button>
        <a href="/products/create" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة منتج جديد
        </a>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في المنتجات...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">الفئة</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="electronics">إلكترونيات</option>
                    <option value="clothing">ملابس</option>
                    <option value="books">كتب</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المنتجات -->
<div class="card">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-box me-2"></i>
                قائمة المنتجات
                <span class="badge bg-primary ms-2" id="productsCount">
                    <?= count($products ?? []) ?>
                </span>
            </h5>
            <div class="d-flex gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportProducts('excel')">
                            <i class="bi bi-file-earmark-excel me-2"></i>تصدير Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportProducts('pdf')">
                            <i class="bi bi-file-earmark-pdf me-2"></i>تصدير PDF
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkActions()">
                            <i class="bi bi-check2-square me-2"></i>إجراءات مجمعة
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-box" style="font-size: 4rem; color: var(--gray-300);"></i>
                </div>
                <h4 class="text-muted">لا توجد منتجات</h4>
                <p class="text-muted mb-4">ابدأ بإضافة منتج جديد للنظام</p>
                <a href="/products/create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة منتج جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="productsTable" data-table>
                    <thead>
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th data-sort="sku">كود المنتج</th>
                            <th data-sort="name">اسم المنتج</th>
                            <th data-sort="category">الفئة</th>
                            <th data-sort="price">السعر</th>
                            <th data-sort="stock">المخزون</th>
                            <th data-sort="status">الحالة</th>
                            <th data-sort="created_at">تاريخ الإضافة</th>
                            <th width="120">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr data-product-id="<?= $product['id'] ?>">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input product-checkbox" type="checkbox" value="<?= $product['id'] ?>">
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary font-monospace"><?= htmlspecialchars($product['sku']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="product-image me-3">
                                            <?php if (!empty($product['image'])): ?>
                                                <img src="<?= htmlspecialchars($product['image']) ?>" alt="Product" class="rounded" width="40" height="40">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <div class="fw-semibold"><?= htmlspecialchars($product['name']) ?></div>
                                            <?php if (!empty($product['description'])): ?>
                                                <small class="text-muted"><?= htmlspecialchars(substr($product['description'], 0, 50)) ?>...</small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($product['category_name'])): ?>
                                        <span class="badge bg-info"><?= htmlspecialchars($product['category_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-end">
                                        <div class="fw-semibold text-success"><?= number_format($product['selling_price'], 2) ?> ر.س</div>
                                        <?php if ($product['cost_price'] > 0): ?>
                                            <small class="text-muted">التكلفة: <?= number_format($product['cost_price'], 2) ?> ر.س</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?= $product['current_stock'] ?></span>
                                        <?php if ($product['current_stock'] <= $product['min_stock_level']): ?>
                                            <i class="bi bi-exclamation-triangle text-warning" title="مخزون منخفض"></i>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($product['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="text-muted"><?= date('Y-m-d', strtotime($product['created_at'])) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/products/show?id=<?= $product['id'] ?>" class="btn btn-sm btn-outline-info" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="/products/edit?id=<?= $product['id'] ?>" class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteProduct(<?= $product['id'] ?>)" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="card-footer bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        عرض 1-<?= count($products) ?> من <?= count($products) ?> منتج
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">السابق</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">التالي</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- مودال استيراد المنتجات -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد منتجات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">ملف Excel</label>
                    <input type="file" class="form-control" accept=".xlsx,.xls,.csv">
                    <div class="form-text">يدعم ملفات Excel و CSV</div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    يمكنك تحميل قالب Excel من <a href="/templates/products-template.xlsx">هنا</a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">استيراد</button>
            </div>
        </div>
    </div>
</div>

<?php
// إنهاء المحتوى
$content = ob_get_clean();

// إضافة السكريبت المخصص
$pageScript = '
// تهيئة الجدول
document.addEventListener("DOMContentLoaded", function() {
    // البحث في الجدول
    document.getElementById("searchInput").addEventListener("input", function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const rows = document.querySelectorAll("#productsTable tbody tr");
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? "" : "none";
        });
        
        updateProductsCount();
    });
    
    // تحديد الكل
    document.getElementById("selectAll").addEventListener("change", function(e) {
        const checkboxes = document.querySelectorAll(".product-checkbox");
        checkboxes.forEach(cb => cb.checked = e.target.checked);
    });
});

// حذف منتج
function deleteProduct(productId) {
    if (confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
        SeaSystem.makeRequest(`/products/delete?id=${productId}`, {
            method: "DELETE"
        }).then(response => {
            if (response.success) {
                SeaSystem.notify("تم حذف المنتج بنجاح", "success");
                location.reload();
            } else {
                SeaSystem.notify(response.message || "حدث خطأ أثناء الحذف", "error");
            }
        }).catch(error => {
            SeaSystem.notify("حدث خطأ في الشبكة", "error");
        });
    }
}

// تصدير المنتجات
function exportProducts(format) {
    const url = `/products/export?format=${format}`;
    window.open(url, "_blank");
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById("searchInput").value = "";
    document.getElementById("categoryFilter").value = "";
    document.getElementById("statusFilter").value = "";
    
    // إظهار جميع الصفوف
    const rows = document.querySelectorAll("#productsTable tbody tr");
    rows.forEach(row => row.style.display = "");
    
    updateProductsCount();
}

// تحديث عداد المنتجات
function updateProductsCount() {
    const visibleRows = document.querySelectorAll("#productsTable tbody tr:not([style*=\"display: none\"])");
    document.getElementById("productsCount").textContent = visibleRows.length;
}

// إجراءات مجمعة
function bulkActions() {
    const selectedProducts = document.querySelectorAll(".product-checkbox:checked");
    if (selectedProducts.length === 0) {
        SeaSystem.notify("يرجى تحديد منتج واحد على الأقل", "warning");
        return;
    }
    
    // يمكن إضافة مودال للإجراءات المجمعة هنا
    console.log("Selected products:", selectedProducts.length);
}
';

// تضمين القالب
include dirname(__DIR__, 3) . '/shared/layout.php';
?>
