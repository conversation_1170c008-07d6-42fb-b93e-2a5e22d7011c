<?php
/**
 * فحص بنية جدول activity_logs
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🔍 بنية جدول activity_logs:\n";
    echo "============================\n";
    
    $result = $pdo->query('DESCRIBE activity_logs');
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    
    // إنشاء الجدول إذا لم يكن موجوداً
    echo "\n🔧 إنشاء جدول activity_logs...\n";
    
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            old_values JSON,
            new_values JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user_action (user_id, action),
            INDEX idx_created_at (created_at)
        )";
        
        $pdo->exec($sql);
        echo "✅ تم إنشاء جدول activity_logs بنجاح\n";
        
    } catch (Exception $e2) {
        echo "❌ خطأ في إنشاء الجدول: " . $e2->getMessage() . "\n";
    }
}
?>
