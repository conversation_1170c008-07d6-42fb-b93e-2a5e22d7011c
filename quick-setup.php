<?php
/**
 * Quick Database Setup
 * إعداد سريع لقاعدة البيانات
 */

echo "🚀 إعداد سريع لقاعدة البيانات\n";
echo "==============================\n\n";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بخادم MySQL\n";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS R1 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE R1");
    
    echo "✅ تم إنشاء قاعدة البيانات R1\n";
    
    // إنشاء الجداول الأساسية
    echo "📊 إنشاء الجداول...\n";
    
    // جدول الأدوار
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS roles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(50) NOT NULL UNIQUE,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "   ✅ جدول الأدوار\n";
    
    // جدول الصلاحيات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS permissions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            display_name VARCHAR(150) NOT NULL,
            module VARCHAR(50) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "   ✅ جدول الصلاحيات\n";
    
    // جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20),
            avatar VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            email_verified_at TIMESTAMP NULL,
            last_login_at TIMESTAMP NULL,
            failed_login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "   ✅ جدول المستخدمين\n";
    
    // جدول ربط المستخدمين بالأدوار
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_roles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            role_id INT NOT NULL,
            assigned_by INT,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
            UNIQUE KEY unique_user_role (user_id, role_id)
        )
    ");
    echo "   ✅ جدول ربط المستخدمين بالأدوار\n";
    
    // جدول ربط الأدوار بالصلاحيات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS role_permissions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            role_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted_by INT,
            granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
            FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
            UNIQUE KEY unique_role_permission (role_id, permission_id)
        )
    ");
    echo "   ✅ جدول ربط الأدوار بالصلاحيات\n";
    
    // جدول الموظفين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            user_id INT,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            national_id VARCHAR(20) UNIQUE,
            birth_date DATE,
            gender ENUM('male', 'female'),
            marital_status ENUM('single', 'married', 'divorced', 'widowed'),
            address TEXT,
            hire_date DATE NOT NULL,
            department_id INT,
            position_id INT,
            salary DECIMAL(10,2),
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "   ✅ جدول الموظفين\n";
    
    // جدول سجل الأنشطة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "   ✅ جدول سجل الأنشطة\n";
    
    // إدراج البيانات الأساسية
    echo "\n🌱 إدراج البيانات الأساسية...\n";
    
    // إدراج الأدوار
    $pdo->exec("
        INSERT IGNORE INTO roles (name, display_name, description) VALUES
        ('super_admin', 'مدير النظام الرئيسي', 'صلاحيات كاملة على جميع أجزاء النظام'),
        ('admin', 'مدير النظام', 'صلاحيات إدارية على معظم أجزاء النظام'),
        ('hr_manager', 'مدير الموارد البشرية', 'إدارة شؤون الموظفين والموارد البشرية'),
        ('employee', 'موظف', 'صلاحيات أساسية للموظفين')
    ");
    echo "   ✅ الأدوار الأساسية\n";
    
    // إدراج المستخدم التجريبي
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->prepare("
        INSERT IGNORE INTO users (username, email, password_hash, first_name, last_name, is_active) 
        VALUES ('admin', '<EMAIL>', ?, 'مدير', 'النظام', 1)
    ")->execute([$hashedPassword]);
    echo "   ✅ المستخدم التجريبي\n";
    
    // ربط المستخدم بدور مدير النظام
    $pdo->exec("
        INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) 
        SELECT u.id, r.id, u.id 
        FROM users u, roles r 
        WHERE u.username = 'admin' AND r.name = 'super_admin'
    ");
    echo "   ✅ ربط المستخدم بالدور\n";
    
    // التحقق من النتائج
    echo "\n📊 التحقق من النتائج...\n";
    
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "   📋 عدد الجداول: " . count($tables) . "\n";
    
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "   👤 عدد المستخدمين: {$userCount}\n";
    
    $roleCount = $pdo->query("SELECT COUNT(*) FROM roles")->fetchColumn();
    echo "   🛡️  عدد الأدوار: {$roleCount}\n";
    
    echo "\n🎉 تم إعداد قاعدة البيانات بنجاح!\n";
    echo "==============================\n";
    echo "💡 معلومات تسجيل الدخول:\n";
    echo "   👤 اسم المستخدم: admin\n";
    echo "   🔑 كلمة المرور: admin123\n";
    echo "   🌐 الرابط: http://localhost/seasystem-R1/public\n\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}
?>
