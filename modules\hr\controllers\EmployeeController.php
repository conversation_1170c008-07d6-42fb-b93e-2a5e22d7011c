<?php
/**
 * SeaSystem Employee Controller
 * متحكم الموظفين
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(__DIR__) . '/models/Employee.php';
require_once dirname(dirname(dirname(__DIR__))) . '/core/Auth.php';

class EmployeeController
{
    private $auth;
    
    public function __construct()
    {
        $this->auth = new Auth();
        
        // التحقق من تسجيل الدخول
        if (!$this->auth->check()) {
            $this->redirectToLogin();
        }
    }

    /**
     * عرض قائمة الموظفين
     */
    public function index()
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.view')) {
                $this->accessDenied();
                return;
            }

            // الحصول على المرشحات من الطلب
            $filters = [
                'status' => $_GET['status'] ?? '',
                'department_id' => $_GET['department_id'] ?? '',
                'search' => $_GET['search'] ?? ''
            ];

            // إعدادات الصفحات
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // الحصول على الموظفين
            $employees = Employee::getAllWithDetails($filters, $limit, $offset);
            
            // الحصول على إحصائيات الموظفين
            $statistics = Employee::getStatistics();
            
            // الحصول على الأقسام للمرشح
            $departments = $this->getDepartments();

            // عرض الصفحة
            $this->render('employees/index', [
                'employees' => $employees,
                'statistics' => $statistics,
                'departments' => $departments,
                'filters' => $filters,
                'currentPage' => $page
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * عرض تفاصيل موظف
     */
    public function show($id)
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.view')) {
                $this->accessDenied();
                return;
            }

            $employee = Employee::findOrFail($id);
            
            $this->render('employees/show', [
                'employee' => $employee
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * عرض نموذج إضافة موظف جديد
     */
    public function create()
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.create')) {
                $this->accessDenied();
                return;
            }

            // الحصول على البيانات المساعدة
            $departments = $this->getDepartments();
            $positions = $this->getPositions();
            $managers = $this->getManagers();
            
            // إنشاء رقم موظف تلقائي
            $employeeCode = Employee::generateEmployeeCode();

            $this->render('employees/create', [
                'departments' => $departments,
                'positions' => $positions,
                'managers' => $managers,
                'employeeCode' => $employeeCode
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * حفظ موظف جديد
     */
    public function store()
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.create')) {
                $this->accessDenied();
                return;
            }

            // التحقق من طريقة الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            // تنظيف البيانات
            $data = $this->sanitizeInput($_POST);
            
            // إنشاء موظف جديد
            $employee = new Employee($data);
            
            if ($employee->save()) {
                // تسجيل العملية
                $this->logActivity('employee_created', $employee->id);
                
                $this->redirect('/employees/' . $employee->id, 'تم إضافة الموظف بنجاح');
            } else {
                throw new Exception('فشل في حفظ بيانات الموظف');
            }

        } catch (Exception $e) {
            $this->handleError($e, '/employees/create');
        }
    }

    /**
     * عرض نموذج تعديل موظف
     */
    public function edit($id)
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.edit')) {
                $this->accessDenied();
                return;
            }

            $employee = Employee::findOrFail($id);
            
            // الحصول على البيانات المساعدة
            $departments = $this->getDepartments();
            $positions = $this->getPositions();
            $managers = $this->getManagers();

            $this->render('employees/edit', [
                'employee' => $employee,
                'departments' => $departments,
                'positions' => $positions,
                'managers' => $managers
            ]);

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * تحديث بيانات موظف
     */
    public function update($id)
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.edit')) {
                $this->accessDenied();
                return;
            }

            // التحقق من طريقة الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }

            $employee = Employee::findOrFail($id);
            
            // تنظيف البيانات
            $data = $this->sanitizeInput($_POST);
            
            // تحديث البيانات
            $employee->fill($data);
            
            if ($employee->save()) {
                // تسجيل العملية
                $this->logActivity('employee_updated', $employee->id);
                
                $this->redirect('/employees/' . $employee->id, 'تم تحديث بيانات الموظف بنجاح');
            } else {
                throw new Exception('فشل في تحديث بيانات الموظف');
            }

        } catch (Exception $e) {
            $this->handleError($e, '/employees/' . $id . '/edit');
        }
    }

    /**
     * حذف موظف
     */
    public function destroy($id)
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.delete')) {
                $this->accessDenied();
                return;
            }

            $employee = Employee::findOrFail($id);
            
            if ($employee->delete()) {
                // تسجيل العملية
                $this->logActivity('employee_deleted', $id);
                
                $this->redirect('/employees', 'تم حذف الموظف بنجاح');
            } else {
                throw new Exception('فشل في حذف الموظف');
            }

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * تحديث حالة موظف
     */
    public function updateStatus($id)
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.edit')) {
                $this->accessDenied();
                return;
            }

            $employee = Employee::findOrFail($id);
            $status = $_POST['status'] ?? '';
            
            if ($employee->updateStatus($status)) {
                // تسجيل العملية
                $this->logActivity('employee_status_updated', $id, ['status' => $status]);
                
                $this->jsonResponse(['success' => true, 'message' => 'تم تحديث حالة الموظف']);
            } else {
                throw new Exception('فشل في تحديث حالة الموظف');
            }

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * البحث في الموظفين (AJAX)
     */
    public function search()
    {
        try {
            $query = $_GET['q'] ?? '';
            $limit = min(20, intval($_GET['limit'] ?? 10));
            
            if (strlen($query) < 2) {
                $this->jsonResponse(['employees' => []]);
                return;
            }
            
            $filters = ['search' => $query];
            $employees = Employee::getAllWithDetails($filters, $limit);
            
            $this->jsonResponse(['employees' => $employees]);

        } catch (Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()]);
        }
    }

    /**
     * تصدير بيانات الموظفين
     */
    public function export()
    {
        try {
            // التحقق من الصلاحية
            if (!$this->auth->hasPermission('hr.employees.view')) {
                $this->accessDenied();
                return;
            }

            $format = $_GET['format'] ?? 'csv';
            $filters = [
                'status' => $_GET['status'] ?? '',
                'department_id' => $_GET['department_id'] ?? ''
            ];

            $employees = Employee::getAllWithDetails($filters);
            
            if ($format === 'csv') {
                $this->exportToCsv($employees);
            } elseif ($format === 'excel') {
                $this->exportToExcel($employees);
            } else {
                throw new Exception('تنسيق التصدير غير مدعوم');
            }

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * الحصول على الأقسام
     */
    private function getDepartments()
    {
        $db = Database::getInstance();
        return $db->select("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
    }

    /**
     * الحصول على المناصب
     */
    private function getPositions()
    {
        $db = Database::getInstance();
        return $db->select("SELECT id, title, department_id FROM positions WHERE is_active = 1 ORDER BY title");
    }

    /**
     * الحصول على المدراء
     */
    private function getManagers()
    {
        return Employee::where(['status' => 'active'], ['id', 'first_name', 'last_name', 'employee_code']);
    }

    /**
     * تنظيف البيانات المدخلة
     */
    private function sanitizeInput($data)
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * تسجيل النشاط
     */
    private function logActivity($action, $recordId, $data = [])
    {
        // يمكن تطوير نظام تسجيل الأنشطة هنا
    }

    /**
     * عرض الصفحة
     */
    private function render($view, $data = [])
    {
        // يمكن تطوير نظام العرض هنا
        extract($data);
        include dirname(__DIR__) . "/views/{$view}.php";
    }

    /**
     * إعادة التوجيه
     */
    private function redirect($url, $message = null)
    {
        if ($message) {
            $_SESSION['success_message'] = $message;
        }
        header("Location: {$url}");
        exit;
    }

    /**
     * إعادة التوجيه لصفحة تسجيل الدخول
     */
    private function redirectToLogin()
    {
        header('Location: /login');
        exit;
    }

    /**
     * رفض الوصول
     */
    private function accessDenied()
    {
        http_response_code(403);
        echo "ليس لديك صلاحية للوصول إلى هذه الصفحة";
        exit;
    }

    /**
     * إرجاع استجابة JSON
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * معالجة الأخطاء
     */
    private function handleError($exception, $redirectUrl = null)
    {
        // تسجيل الخطأ
        error_log($exception->getMessage());
        
        if ($redirectUrl) {
            $_SESSION['error_message'] = $exception->getMessage();
            $this->redirect($redirectUrl);
        } else {
            echo "حدث خطأ: " . $exception->getMessage();
        }
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($employees)
    {
        $filename = 'employees_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // العناوين
        fputcsv($output, [
            'رقم الموظف', 'الاسم الأول', 'اسم العائلة', 'البريد الإلكتروني',
            'الهاتف', 'القسم', 'المنصب', 'الراتب', 'الحالة', 'تاريخ التوظيف'
        ]);
        
        // البيانات
        foreach ($employees as $employee) {
            fputcsv($output, [
                $employee['employee_code'],
                $employee['first_name'],
                $employee['last_name'],
                $employee['email'],
                $employee['phone'],
                $employee['department_name'],
                $employee['position_title'],
                $employee['salary'],
                $employee['status'],
                $employee['hire_date']
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * تصدير إلى Excel (يتطلب مكتبة خارجية)
     */
    private function exportToExcel($employees)
    {
        // يمكن تطوير التصدير إلى Excel هنا باستخدام مكتبة مثل PhpSpreadsheet
        throw new Exception('تصدير Excel غير متاح حالياً');
    }
}
