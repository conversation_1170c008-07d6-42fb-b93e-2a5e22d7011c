/* SeaSystem Dashboard Styles - Enhanced Version */
:root {
    /* متغيرات الألوان المحسنة */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    /* متغيرات الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* متغيرات الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* متغيرات الحدود */
    --radius-sm: 6px;
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    direction: rtl;
    overflow-x: hidden;
    line-height: 1.6;
    color: #2d3748;
    min-height: 100vh;
}

/* خلفية متحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundMove {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
    }
    33% {
        transform: translateX(-30px) translateY(-50px) scale(1.1);
    }
    66% {
        transform: translateX(20px) translateY(30px) scale(0.9);
    }
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

/* رأس القائمة الجانبية */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 80px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: bold;
    color: #3498db;
}

.logo i {
    font-size: 28px;
}

.sidebar.collapsed .logo-text {
    display: none;
}

.toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* قائمة القائمة الجانبية */
.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    margin-bottom: 5px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
}

.menu-item.active .menu-link {
    background-color: #3498db;
    color: white;
}

.menu-link i {
    width: 20px;
    margin-left: 15px;
    text-align: center;
    font-size: 16px;
}

.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .submenu-arrow {
    display: none;
}

.submenu-arrow {
    margin-right: auto;
    transition: transform 0.3s ease;
}

.menu-item.open .submenu-arrow {
    transform: rotate(180deg);
}

/* القوائم الفرعية */
.submenu {
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: rgba(0, 0, 0, 0.2);
}

.menu-item.open .submenu {
    max-height: 500px;
}

.sidebar.collapsed .submenu {
    display: none;
}

.submenu li {
    margin: 0;
}

.submenu a {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 55px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.submenu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
    padding-right: 25px;
}

.submenu a i {
    width: 16px;
    margin-left: 10px;
    font-size: 14px;
}

/* معلومات المستخدم */
.user-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .user-info {
    padding: 15px 10px;
    text-align: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar i {
    font-size: 32px;
    color: #3498db;
}

.user-details {
    flex: 1;
}

.sidebar.collapsed .user-details {
    display: none;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-role {
    display: block;
    font-size: 12px;
    color: #bdc3c7;
}

.user-actions {
    display: flex;
    gap: 8px;
}

.sidebar.collapsed .user-actions {
    display: none;
}

.user-actions a {
    color: #bdc3c7;
    font-size: 14px;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.user-actions a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
}

/* المحتوى الرئيسي المحسن */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    z-index: 0;
}

.main-content > * {
    position: relative;
    z-index: 1;
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* تحسين محتوى لوحة التحكم */
.dashboard-content {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    margin: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* الشريط العلوي */
.top-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1 {
    color: #2c3e50;
    font-size: 28px;
    margin-bottom: 5px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 14px;
}

.breadcrumb i {
    font-size: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 18px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.current-time {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* محتوى لوحة التحكم */
.dashboard-content {
    padding: 30px;
}

/* البطاقات الإحصائية المحسنة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    overflow: hidden;
    position: relative;
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stats-card:hover::before {
    opacity: 1;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.stats-card .card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.stats-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.bg-gradient-primary {
    background: var(--primary-gradient);
}

.bg-gradient-success {
    background: var(--success-gradient);
}

.bg-gradient-warning {
    background: var(--warning-gradient);
}

.bg-gradient-info {
    background: var(--info-gradient);
}

.bg-gradient-danger {
    background: var(--danger-gradient);
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, currentColor 0%, currentColor 100%);
    -webkit-background-clip: text;
    background-clip: text;
    animation: countUp 0.8s ease-out;
}

.stats-title {
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    color: #6b7280;
    margin-bottom: 1rem;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* تحسين شريط التقدم */
.progress {
    height: 8px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius);
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تحسين الأزرار */
.stats-card .btn {
    border-radius: var(--radius);
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.stats-card .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.stats-card .btn:hover::before {
    left: 100%;
}

.stats-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    border-radius: var(--radius);
    margin: 0 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateX(-4px);
    color: #667eea;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* الويدجت */
.dashboard-widgets {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.widget {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.widget-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.widget-actions {
    display: flex;
    gap: 10px;
}

.widget-btn {
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 14px;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.widget-btn:hover {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.view-all {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.view-all:hover {
    text-decoration: underline;
}

.widget-content {
    padding: 25px;
}

.chart-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #bdc3c7;
}

.chart-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

/* الجداول */
.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ecf0f1;
}

.data-table th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
}

.data-table td {
    color: #7f8c8d;
    font-size: 14px;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status.shipped {
    background-color: #cce5ff;
    color: #004085;
}

/* الأنشطة الأخيرة */
.recent-activities {
    margin-top: 30px;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.activity-content p {
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 5px;
}

.activity-time {
    color: #7f8c8d;
    font-size: 12px;
}

/* البحث السريع المحسن */
.search-box {
    position: relative;
    min-width: 300px;
}

.search-box .form-control {
    border-radius: var(--radius-lg);
    border: 2px solid transparent;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

.search-box .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.search-results {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-result-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4a5568;
    text-decoration: none;
}

.search-result-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(-5px);
    color: #667eea;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* الإشعارات المحسنة */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
    min-width: 20px;
    text-align: center;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* الوقت الحالي */
.current-time {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #6b7280;
    transition: all var(--transition-normal);
}

.current-time:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* تحسينات الرسوم البيانية */
.chart-container {
    position: relative;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.chart-container .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

.chart-container .card-body {
    padding: 2rem;
}

/* تحسينات الجداول */
.table-responsive {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.data-table {
    margin-bottom: 0;
}

.data-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: 0.75rem;
    padding: 1rem;
    border: none;
}

.data-table tbody tr {
    transition: all var(--transition-fast);
}

.data-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.02);
    transform: scale(1.01);
}

.data-table tbody td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

/* التجاوب المحسن */
@media (max-width: 1200px) {
    .search-box {
        min-width: 250px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-widgets {
        grid-template-columns: 1fr;
    }

    .top-header {
        padding: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .search-box {
        min-width: 200px;
    }

    .current-time {
        display: none !important;
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-value {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .search-box {
        min-width: 150px;
    }

    .stats-card .card-body {
        padding: 1rem;
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .stats-value {
        font-size: 1.75rem;
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}
