<?php
/**
 * SeaSystem Authentication System
 * نظام المصادقة والتحقق
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Session.php';
require_once __DIR__ . '/Environment.php';

class Auth
{
    private $db;
    private $maxLoginAttempts;
    private $lockoutDuration;
    private $passwordMinLength;

    public function __construct()
    {
        $this->db = Database::getInstance();
        
        Environment::load();
        $this->maxLoginAttempts = Environment::get('MAX_LOGIN_ATTEMPTS', 5);
        $this->lockoutDuration = Environment::get('LOCKOUT_DURATION', 900); // 15 دقيقة
        $this->passwordMinLength = Environment::get('PASSWORD_MIN_LENGTH', 8);
        
        Session::start();
    }

    /**
     * تسجيل الدخول
     * 
     * @param string $username اسم المستخدم أو البريد الإلكتروني
     * @param string $password كلمة المرور
     * @param bool $remember تذكر تسجيل الدخول
     * @return array
     */
    public function login($username, $password, $remember = false)
    {
        try {
            // التحقق من البيانات المدخلة
            if (empty($username) || empty($password)) {
                return ['success' => false, 'message' => 'يرجى إدخال اسم المستخدم وكلمة المرور'];
            }

            // التحقق من محاولات تسجيل الدخول
            if ($this->isAccountLocked($username)) {
                return ['success' => false, 'message' => 'الحساب مقفل مؤقتاً بسبب محاولات تسجيل دخول متعددة'];
            }

            // البحث عن المستخدم
            $user = $this->findUser($username);
            
            if (!$user) {
                $this->recordFailedAttempt($username);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }

            // التحقق من حالة المستخدم
            if (!$user['is_active']) {
                return ['success' => false, 'message' => 'الحساب غير نشط'];
            }

            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedAttempt($username);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }

            // تسجيل الدخول بنجاح
            $this->loginUser($user, $remember);
            $this->clearFailedAttempts($username);
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح', 'user' => $user];

        } catch (Exception $e) {
            // تسجيل الخطأ للتشخيص
            error_log("Auth Login Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            return ['success' => false, 'message' => 'حدث خطأ أثناء تسجيل الدخول: ' . $e->getMessage()];
        }
    }

    /**
     * تسجيل الخروج
     */
    public function logout()
    {
        $userId = $this->getUserId();
        
        if ($userId) {
            // تحديث وقت آخر تسجيل دخول
            $this->updateLastLogin($userId);
            
            // حذف رمز التذكر إن وجد
            $this->clearRememberToken($userId);
        }
        
        // تدمير الجلسة
        Session::destroy();
        
        // حذف كوكي التذكر
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }

    /**
     * التحقق من تسجيل الدخول
     * 
     * @return bool
     */
    public function check()
    {
        // فحص وضع التطوير
        if ($this->isDevelopmentMode()) {
            return true;
        }

        // التحقق من الجلسة
        if (Session::has('user_id') && Session::has('user_authenticated')) {
            return true;
        }

        // التحقق من رمز التذكر
        if (isset($_COOKIE['remember_token'])) {
            return $this->loginByRememberToken($_COOKIE['remember_token']);
        }

        return false;
    }

    /**
     * الحصول على المستخدم الحالي
     * 
     * @return array|null
     */
    public function user()
    {
        if (!$this->check()) {
            return null;
        }

        // إرجاع مستخدم افتراضي في وضع التطوير
        if ($this->isDevelopmentMode()) {
            return $this->getDefaultDevUser();
        }

        $userId = Session::get('user_id');
        return $this->getUserById($userId);
    }

    /**
     * الحصول على معرف المستخدم الحالي
     * 
     * @return int|null
     */
    public function getUserId()
    {
        return Session::get('user_id');
    }

    /**
     * التحقق من الصلاحية
     * 
     * @param string $permission الصلاحية المطلوبة
     * @return bool
     */
    public function hasPermission($permission)
    {
        if (!$this->check()) {
            return false;
        }
        
        $userId = $this->getUserId();
        
        // التحقق من صلاحيات المستخدم
        $query = "
            SELECT COUNT(*) as count
            FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ur.user_id = ? AND p.name = ?
        ";
        
        $result = $this->db->selectOne($query, [$userId, $permission]);
        return $result['count'] > 0;
    }

    /**
     * التحقق من الدور
     * 
     * @param string $role الدور المطلوب
     * @return bool
     */
    public function hasRole($role)
    {
        if (!$this->check()) {
            return false;
        }
        
        $userId = $this->getUserId();
        
        $query = "
            SELECT COUNT(*) as count
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND r.name = ?
        ";
        
        $result = $this->db->selectOne($query, [$userId, $role]);
        return $result['count'] > 0;
    }

    /**
     * تغيير كلمة المرور
     * 
     * @param string $currentPassword كلمة المرور الحالية
     * @param string $newPassword كلمة المرور الجديدة
     * @return array
     */
    public function changePassword($currentPassword, $newPassword)
    {
        if (!$this->check()) {
            return ['success' => false, 'message' => 'يجب تسجيل الدخول أولاً'];
        }
        
        $user = $this->user();
        
        // التحقق من كلمة المرور الحالية
        if (!password_verify($currentPassword, $user['password_hash'])) {
            return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
        }
        
        // التحقق من قوة كلمة المرور الجديدة
        $validation = $this->validatePassword($newPassword);
        if (!$validation['valid']) {
            return ['success' => false, 'message' => $validation['message']];
        }
        
        // تحديث كلمة المرور
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $this->db->update('users', 
            ['password_hash' => $hashedPassword], 
            ['id' => $user['id']]
        );
        
        // تسجيل العملية
        $this->logActivity('password_changed', $user['id']);
        
        return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
    }

    /**
     * إعادة تعيين كلمة المرور
     * 
     * @param string $email البريد الإلكتروني
     * @return array
     */
    public function resetPassword($email)
    {
        $user = $this->db->selectOne(
            "SELECT id, email, first_name, last_name FROM users WHERE email = ? AND is_active = 1",
            [$email]
        );
        
        if (!$user) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
        }
        
        // إنشاء رمز إعادة التعيين
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة
        
        // حفظ الرمز في قاعدة البيانات
        $this->db->execute(
            "INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) 
             ON DUPLICATE KEY UPDATE token = ?, expires_at = ?",
            [$email, $token, $expires, $token, $expires]
        );
        
        // إرسال البريد الإلكتروني (يمكن تطويره لاحقاً)
        // $this->sendResetEmail($user, $token);
        
        return ['success' => true, 'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'];
    }

    /**
     * البحث عن المستخدم
     * 
     * @param string $username اسم المستخدم أو البريد الإلكتروني
     * @return array|null
     */
    private function findUser($username)
    {
        return $this->db->selectOne(
            "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );
    }

    /**
     * الحصول على المستخدم بالمعرف
     * 
     * @param int $userId معرف المستخدم
     * @return array|null
     */
    private function getUserById($userId)
    {
        return $this->db->selectOne(
            "SELECT id, username, email, first_name, last_name, avatar, is_active, last_login_at, created_at FROM users WHERE id = ?",
            [$userId]
        );
    }

    /**
     * تسجيل دخول المستخدم
     * 
     * @param array $user بيانات المستخدم
     * @param bool $remember تذكر تسجيل الدخول
     */
    private function loginUser($user, $remember = false)
    {
        // تجديد معرف الجلسة
        Session::regenerateId();
        
        // حفظ بيانات المستخدم في الجلسة
        Session::set('user_id', $user['id']);
        Session::set('user_authenticated', true);
        Session::set('user_username', $user['username']);
        Session::set('login_time', time());
        
        // تحديث وقت آخر تسجيل دخول
        $this->updateLastLogin($user['id']);
        
        // إنشاء رمز التذكر إذا طُلب
        if ($remember) {
            $this->createRememberToken($user['id']);
        }
        
        // تسجيل العملية
        $this->logActivity('user_login', $user['id']);
    }

    /**
     * تسجيل الدخول برمز التذكر
     * 
     * @param string $token رمز التذكر
     * @return bool
     */
    private function loginByRememberToken($token)
    {
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE remember_token = ? AND is_active = 1",
            [$token]
        );
        
        if ($user) {
            $this->loginUser($user, true);
            return true;
        }
        
        return false;
    }

    /**
     * إنشاء رمز التذكر
     * 
     * @param int $userId معرف المستخدم
     */
    private function createRememberToken($userId)
    {
        $token = bin2hex(random_bytes(32));
        
        // حفظ الرمز في قاعدة البيانات
        $this->db->update('users', ['remember_token' => $token], ['id' => $userId]);
        
        // إنشاء كوكي التذكر (30 يوم)
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }

    /**
     * حذف رمز التذكر
     * 
     * @param int $userId معرف المستخدم
     */
    private function clearRememberToken($userId)
    {
        $this->db->update('users', ['remember_token' => null], ['id' => $userId]);
    }

    /**
     * تحديث وقت آخر تسجيل دخول
     * 
     * @param int $userId معرف المستخدم
     */
    private function updateLastLogin($userId)
    {
        $this->db->update('users', ['last_login_at' => date('Y-m-d H:i:s')], ['id' => $userId]);
    }

    /**
     * التحقق من قفل الحساب
     * 
     * @param string $username اسم المستخدم
     * @return bool
     */
    private function isAccountLocked($username)
    {
        $attempts = $this->getFailedAttempts($username);
        
        if ($attempts['count'] >= $this->maxLoginAttempts) {
            $timeDiff = time() - strtotime($attempts['last_attempt']);
            return $timeDiff < $this->lockoutDuration;
        }
        
        return false;
    }

    /**
     * تسجيل محاولة فاشلة
     * 
     * @param string $username اسم المستخدم
     */
    private function recordFailedAttempt($username)
    {
        $ip = Session::getClientIp();
        
        $this->db->execute(
            "INSERT INTO login_attempts (username, ip_address, attempted_at) VALUES (?, ?, NOW())",
            [$username, $ip]
        );
    }

    /**
     * الحصول على المحاولات الفاشلة
     * 
     * @param string $username اسم المستخدم
     * @return array
     */
    private function getFailedAttempts($username)
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt 
             FROM login_attempts 
             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$username, $this->lockoutDuration]
        );
        
        return $result ?: ['count' => 0, 'last_attempt' => null];
    }

    /**
     * مسح المحاولات الفاشلة
     * 
     * @param string $username اسم المستخدم
     */
    private function clearFailedAttempts($username)
    {
        $this->db->execute(
            "DELETE FROM login_attempts WHERE username = ?",
            [$username]
        );
    }

    /**
     * التحقق من قوة كلمة المرور
     * 
     * @param string $password كلمة المرور
     * @return array
     */
    private function validatePassword($password)
    {
        if (strlen($password) < $this->passwordMinLength) {
            return ['valid' => false, 'message' => "كلمة المرور يجب أن تكون على الأقل {$this->passwordMinLength} أحرف"];
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل'];
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل'];
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل'];
        }
        
        return ['valid' => true, 'message' => 'كلمة المرور قوية'];
    }

    /**
     * تسجيل النشاط
     * 
     * @param string $action العملية
     * @param int $userId معرف المستخدم
     * @param array $data بيانات إضافية
     */
    private function logActivity($action, $userId, $data = [])
    {
        $ip = Session::getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $this->db->insert('activity_logs', [
            'user_id' => $userId,
            'action' => $action,
            'description' => is_array($data) ? json_encode($data) : $data,
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * التحقق من وضع التطوير
     *
     * @return bool
     */
    private function isDevelopmentMode()
    {
        $devConfig = @include dirname(__DIR__) . '/config/development.php';
        return $devConfig && isset($devConfig['disable_auth_check']) && $devConfig['disable_auth_check'] === true;
    }

    /**
     * الحصول على المستخدم الافتراضي للتطوير
     *
     * @return array
     */
    private function getDefaultDevUser()
    {
        $devConfig = include dirname(__DIR__) . '/config/development.php';
        return $devConfig['default_dev_user'] ?? [
            'id' => 1,
            'username' => 'admin',
            'email' => '<EMAIL>',
            'first_name' => 'مدير',
            'last_name' => 'النظام',
            'phone' => '123456789',
            'is_active' => 1,
            'created_at' => '2025-01-01 00:00:00'
        ];
    }
}
