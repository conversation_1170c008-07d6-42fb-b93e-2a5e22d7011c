<?php
/**
 * لوحة التحكم الرئيسية
 */

// إعداد البيانات للقالب
$title = 'لوحة التحكم';
$breadcrumbs = [
    ['title' => 'الرئيسية', 'url' => '/dashboard']
];

// بيانات الإحصائيات (مؤقتة)
$stats = [
    [
        'title' => 'إجمالي العملاء',
        'value' => '1,234',
        'icon' => 'bi-people',
        'color' => 'primary',
        'change' => '+12%',
        'url' => '/customers'
    ],
    [
        'title' => 'المنتجات',
        'value' => '567',
        'icon' => 'bi-box',
        'color' => 'success',
        'change' => '+5%',
        'url' => '/products'
    ],
    [
        'title' => 'الطلبات اليوم',
        'value' => '89',
        'icon' => 'bi-cart',
        'color' => 'warning',
        'change' => '+23%',
        'url' => '/sales/orders'
    ],
    [
        'title' => 'إجمالي المبيعات',
        'value' => '₹ 45,678',
        'icon' => 'bi-currency-dollar',
        'color' => 'info',
        'change' => '+18%',
        'url' => '/finance/reports'
    ]
];

// بدء المحتوى
ob_start();
?>
<!-- بطاقات الإحصائيات -->
<div class="row mb-4">
    <?php foreach ($stats as $stat): ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-<?= $stat['color'] ?> me-3">
                            <i class="<?= $stat['icon'] ?>"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stats-value"><?= $stat['value'] ?></div>
                            <div class="stats-title"><?= $stat['title'] ?></div>
                            <div class="stats-change text-<?= $stat['color'] ?>">
                                <i class="bi bi-arrow-up"></i> <?= $stat['change'] ?>
                            </div>
                        </div>
                        <div class="stats-action">
                            <a href="<?= $stat['url'] ?>" class="btn btn-sm btn-outline-<?= $stat['color'] ?>">
                                <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- الرسوم البيانية والتقارير -->
<div class="row mb-4">
    <div class="col-xl-8 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    مخطط المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    توزيع المبيعات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="distributionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- النشاط الأخير والإشعارات -->
<div class="row">
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="activity-timeline">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">تم إضافة عميل جديد</div>
                            <div class="activity-description">أحمد محمد - شركة التقنية المتقدمة</div>
                            <div class="activity-time">منذ 5 دقائق</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="bi bi-cart-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">طلب جديد #SO-001</div>
                            <div class="activity-description">طلب بقيمة 15,000 ريال</div>
                            <div class="activity-time">منذ 15 دقيقة</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">تحذير مخزون منخفض</div>
                            <div class="activity-description">منتج لابتوب ديل - متبقي 5 قطع</div>
                            <div class="activity-time">منذ 30 دقيقة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bell me-2"></i>
                    الإشعارات
                </h5>
            </div>
            <div class="card-body">
                <div class="notification-list">
                    <div class="notification-item">
                        <div class="notification-icon bg-info">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">مرحباً بك في النظام</div>
                            <div class="notification-time">اليوم</div>
                        </div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-icon bg-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">يرجى تحديث معلومات الشركة</div>
                            <div class="notification-time">أمس</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// إنهاء المحتوى
$content = ob_get_clean();

// إضافة الأنماط المخصصة
$additionalCSS = [
    'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css'
];

// إضافة السكريبت المخصص
$additionalJS = [
    'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js'
];

$pageScript = '
// إعداد الرسوم البيانية
document.addEventListener("DOMContentLoaded", function() {
    // رسم بياني للمبيعات
    const salesCtx = document.getElementById("salesChart");
    if (salesCtx) {
        new Chart(salesCtx, {
            type: "line",
            data: {
                labels: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو"],
                datasets: [{
                    label: "المبيعات",
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: "rgb(99, 102, 241)",
                    backgroundColor: "rgba(99, 102, 241, 0.1)",
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // رسم بياني دائري للتوزيع
    const distributionCtx = document.getElementById("distributionChart");
    if (distributionCtx) {
        new Chart(distributionCtx, {
            type: "doughnut",
            data: {
                labels: ["إلكترونيات", "ملابس", "كتب", "أخرى"],
                datasets: [{
                    data: [40, 25, 20, 15],
                    backgroundColor: [
                        "rgb(99, 102, 241)",
                        "rgb(34, 197, 94)",
                        "rgb(251, 146, 60)",
                        "rgb(239, 68, 68)"
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom"
                    }
                }
            }
        });
    }
});
';

// تضمين القالب
include dirname(__DIR__, 2) . '/shared/layout.php';
?>
