<?php
/**
 * اختبار المستخدم التجريبي
 */

require_once 'core/Environment.php';
Environment::load();
require_once 'core/Database.php';

try {
    $db = Database::getInstance();
    $user = $db->selectOne('SELECT username, email, password_hash, is_active FROM users WHERE username = ?', ['admin']);
    
    if ($user) {
        echo "المستخدم موجود:\n";
        echo "اسم المستخدم: " . $user['username'] . "\n";
        echo "البريد الإلكتروني: " . $user['email'] . "\n";
        echo "نشط: " . ($user['is_active'] ? 'نعم' : 'لا') . "\n";
        echo "كلمة المرور مشفرة: " . substr($user['password_hash'], 0, 20) . "...\n";
        
        // اختبار كلمة المرور
        if (password_verify('admin123', $user['password_hash'])) {
            echo "كلمة المرور صحيحة\n";
        } else {
            echo "كلمة المرور خاطئة\n";
        }
    } else {
        echo "المستخدم غير موجود\n";
    }
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?>
