<?php
/**
 * اختبار تسجيل دخول بسيط
 */

// إعداد cURL لإرسال طلب POST
$ch = curl_init();

// إعداد الخيارات
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'username' => 'admin',
    'password' => 'admin123',
    'remember' => '1'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

echo "🧪 اختبار تسجيل الدخول...\n";
echo "========================\n\n";

// إرسال الطلب
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);

echo "📊 النتائج:\n";
echo "كود الاستجابة: {$httpCode}\n";
echo "الرابط النهائي: {$finalUrl}\n";

if ($httpCode == 200) {
    if (strpos($response, 'لوحة التحكم') !== false || strpos($finalUrl, 'dashboard') !== false) {
        echo "✅ نجح تسجيل الدخول! تم التوجيه إلى لوحة التحكم\n";
    } elseif (strpos($response, 'تسجيل الدخول') !== false) {
        echo "❌ فشل تسجيل الدخول - لا يزال في صفحة تسجيل الدخول\n";
        
        // البحث عن رسائل الخطأ
        if (preg_match('/<div class="alert alert-error">(.*?)<\/div>/s', $response, $matches)) {
            echo "رسالة الخطأ: " . strip_tags($matches[1]) . "\n";
        }
    } else {
        echo "⚠️ استجابة غير متوقعة\n";
    }
} else {
    echo "❌ خطأ HTTP: {$httpCode}\n";
}

curl_close($ch);

// اختبار الوصول للوحة التحكم
echo "\n🎯 اختبار الوصول للوحة التحكم...\n";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, 'http://localhost:8000/dashboard');
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_COOKIEFILE, 'cookies.txt');

$dashboardResponse = curl_exec($ch2);
$dashboardCode = curl_getinfo($ch2, CURLINFO_HTTP_CODE);

if ($dashboardCode == 200 && strpos($dashboardResponse, 'لوحة التحكم') !== false) {
    echo "✅ تم الوصول للوحة التحكم بنجاح!\n";
} else {
    echo "❌ لا يمكن الوصول للوحة التحكم\n";
    echo "كود الاستجابة: {$dashboardCode}\n";
}

curl_close($ch2);

// تنظيف ملف الكوكيز
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

echo "\n🔗 للاختبار اليدوي:\n";
echo "افتح المتصفح وتوجه إلى: http://localhost:8000\n";
echo "اسم المستخدم: admin\n";
echo "كلمة المرور: admin123\n";
?>
