<?php
/**
 * صفحة تسجيل دخول مبسطة للاختبار
 */

// بدء الجلسة أولاً
session_start();

// تحميل الملفات الأساسية
require_once '../core/Environment.php';
Environment::load();
require_once '../core/helpers.php';
require_once '../core/Database.php';

$message = '';
$messageType = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            throw new Exception('يرجى إدخال اسم المستخدم وكلمة المرور');
        }
        
        // البحث عن المستخدم
        $db = Database::getInstance();
        $user = $db->selectOne("SELECT * FROM users WHERE username = ? AND is_active = 1", [$username]);
        
        if (!$user) {
            throw new Exception('اسم المستخدم غير صحيح');
        }
        
        // التحقق من كلمة المرور
        if (!password_verify($password, $user['password_hash'])) {
            throw new Exception('كلمة المرور غير صحيحة');
        }
        
        // تسجيل الدخول بنجاح
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['logged_in'] = true;
        
        // تحديث آخر تسجيل دخول
        $db->update('users', ['last_login_at' => date('Y-m-d H:i:s')], ['id' => $user['id']]);
        
        $message = 'تم تسجيل الدخول بنجاح!';
        $messageType = 'success';
        
        // إعادة التوجيه إلى لوحة التحكم بعد 2 ثانية
        header("refresh:2;url=../public/index.php");
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول البسيط - SeaSystem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: right;
        }
        .info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        .session-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 تسجيل الدخول البسيط</h1>
        
        <div class="info">
            <h4>🧪 نسخة اختبار</h4>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
                <?php if ($messageType === 'success'): ?>
                    <br><small>سيتم توجيهك إلى لوحة التحكم...</small>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit">تسجيل الدخول</button>
        </form>
        
        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
            <div class="session-info">
                <h4>✅ تم تسجيل الدخول</h4>
                <p><strong>معرف المستخدم:</strong> <?= $_SESSION['user_id'] ?></p>
                <p><strong>اسم المستخدم:</strong> <?= $_SESSION['username'] ?></p>
                <p><a href="../public/index.php">الذهاب إلى لوحة التحكم</a></p>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
            <p>تم التطوير بـ ❤️ باستخدام ALAA KHERY</p>
        </div>
    </div>
</body>
</html>
