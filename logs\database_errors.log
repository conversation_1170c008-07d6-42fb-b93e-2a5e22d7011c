{"timestamp":"2025-07-04 14:02:51","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:03:25","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:05:41","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:06:34","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:07:36","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:08:14","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:09:31","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:11:19","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:18:58","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:20:10","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:20:29","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:21:40","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:22:43","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:23:13","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:26:22","message":"خطأ في استعلام UPDATE: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_login' in 'field list'","query":"UPDATE users SET last_login = :last_login WHERE id = :where_id","params":{"last_login":"2025-07-04 13:26:22","where_id":1}}
{"timestamp":"2025-07-04 13:29:05","message":"خطأ في استعلام UPDATE: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_login' in 'field list'","query":"UPDATE users SET last_login = :last_login WHERE id = :where_id","params":{"last_login":"2025-07-04 13:29:05","where_id":1}}
{"timestamp":"2025-07-04 13:32:51","message":"خطأ في استعلام INSERT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'old_values' in 'field list'","query":"INSERT INTO activity_logs (user_id,action,ip_address,user_agent,old_values,created_at) VALUES (:user_id, :action, :ip_address, :user_agent, :old_values, :created_at)","params":{"user_id":1,"action":"user_login","ip_address":"0.0.0.0","user_agent":"","old_values":"[]","created_at":"2025-07-04 13:32:51"}}
{"timestamp":"2025-07-04 13:34:05","message":"خطأ في استعلام INSERT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'old_values' in 'field list'","query":"INSERT INTO activity_logs (user_id,action,ip_address,user_agent,old_values,created_at) VALUES (:user_id, :action, :ip_address, :user_agent, :old_values, :created_at)","params":{"user_id":1,"action":"user_login","ip_address":"0.0.0.0","user_agent":"","old_values":"[]","created_at":"2025-07-04 13:34:05"}}
{"timestamp":"2025-07-04 13:35:36","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_login' in 'field list'","query":"SELECT id, username, email, first_name, last_name, avatar, is_active, last_login, created_at FROM users WHERE id = ?","params":[1]}
{"timestamp":"2025-07-04 14:36:42","message":"خطأ في استعلام UPDATE: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'field list'","query":"UPDATE users SET remember_token = :remember_token WHERE id = :where_id","params":{"remember_token":"6f8649d1fe9c79fae6eba2ee246721e6ce4f4fe2494495f4406b9c29a50af44d","where_id":1}}
{"timestamp":"2025-07-04 14:39:08","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:39:22","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:40:15","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:40:17","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:43:35","message":"خطأ في استعلام UPDATE: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'field list'","query":"UPDATE users SET remember_token = :remember_token WHERE id = :where_id","params":{"remember_token":"9e352937b022f3904a19efd737ceaa73c6789103967ecb778d231862a60dbdb7","where_id":1}}
{"timestamp":"2025-07-04 14:48:44","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:48:53","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
{"timestamp":"2025-07-04 14:52:02","message":"خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'remember_token' in 'where clause'","query":"SELECT * FROM users WHERE remember_token = ? AND is_active = 1","params":["1|3bdb4b2e7526958f7f3acec3a33d187daf79dd1c483d0b001e19834dffc4efca533d6643c58cd917ea8949e8e8e6cb90ecc373aa06a752c5f9dcb9dda94754cc"]}
