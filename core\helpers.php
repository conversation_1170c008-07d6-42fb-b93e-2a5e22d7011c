<?php
/**
 * SeaSystem Helper Functions
 * الدوال المساعدة لنظام SeaSystem
 */

if (!function_exists('config')) {
    /**
     * دالة مساعدة للحصول على إعدادات التطبيق
     * 
     * @param string $key المفتاح المطلوب
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    function config($key = null, $default = null)
    {
        static $config = null;
        
        if ($config === null) {
            $config = include dirname(__DIR__) . '/config/app.php';
        }
        
        if ($key === null) {
            return $config;
        }
        
        $keys = explode('.', $key);
        $value = $config;
        
        foreach ($keys as $k) {
            if (is_array($value) && array_key_exists($k, $value)) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
}

if (!function_exists('env')) {
    /**
     * دالة للحصول على متغيرات البيئة
     * 
     * @param string $key المفتاح
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    function env($key, $default = null)
    {
        return Environment::get($key, $default);
    }
}

if (!function_exists('app_path')) {
    /**
     * دالة للحصول على مسار التطبيق
     * 
     * @param string $path المسار النسبي
     * @return string
     */
    function app_path($path = '')
    {
        $basePath = dirname(__DIR__);
        return $path ? $basePath . '/' . ltrim($path, '/') : $basePath;
    }
}

if (!function_exists('public_path')) {
    /**
     * دالة للحصول على مسار المجلد العام
     * 
     * @param string $path المسار النسبي
     * @return string
     */
    function public_path($path = '')
    {
        $publicPath = dirname(__DIR__) . '/public';
        return $path ? $publicPath . '/' . ltrim($path, '/') : $publicPath;
    }
}

if (!function_exists('storage_path')) {
    /**
     * دالة للحصول على مسار التخزين
     * 
     * @param string $path المسار النسبي
     * @return string
     */
    function storage_path($path = '')
    {
        $storagePath = dirname(__DIR__) . '/storage';
        return $path ? $storagePath . '/' . ltrim($path, '/') : $storagePath;
    }
}

if (!function_exists('asset')) {
    /**
     * دالة للحصول على رابط الملفات الثابتة
     * 
     * @param string $path مسار الملف
     * @return string
     */
    function asset($path)
    {
        $baseUrl = rtrim(config('app.url', 'http://localhost'), '/');
        return $baseUrl . '/assets/' . ltrim($path, '/');
    }
}

if (!function_exists('url')) {
    /**
     * دالة لإنشاء رابط
     * 
     * @param string $path المسار
     * @return string
     */
    function url($path = '')
    {
        $baseUrl = rtrim(config('app.url', 'http://localhost'), '/');
        return $path ? $baseUrl . '/' . ltrim($path, '/') : $baseUrl;
    }
}

if (!function_exists('redirect')) {
    /**
     * دالة إعادة التوجيه
     * 
     * @param string $url الرابط
     * @param int $code كود الاستجابة
     */
    function redirect($url, $code = 302)
    {
        header("Location: {$url}", true, $code);
        exit;
    }
}

if (!function_exists('old')) {
    /**
     * دالة للحصول على القيم القديمة من النموذج
     * 
     * @param string $key المفتاح
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    function old($key, $default = null)
    {
        return $_SESSION['_old'][$key] ?? $default;
    }
}

if (!function_exists('csrf_token')) {
    /**
     * دالة لإنشاء رمز CSRF
     * 
     * @return string
     */
    function csrf_token()
    {
        if (!isset($_SESSION['_token'])) {
            $_SESSION['_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['_token'];
    }
}

if (!function_exists('csrf_field')) {
    /**
     * دالة لإنشاء حقل CSRF مخفي
     * 
     * @return string
     */
    function csrf_field()
    {
        return '<input type="hidden" name="_token" value="' . csrf_token() . '">';
    }
}

if (!function_exists('method_field')) {
    /**
     * دالة لإنشاء حقل HTTP method مخفي
     * 
     * @param string $method الطريقة
     * @return string
     */
    function method_field($method)
    {
        return '<input type="hidden" name="_method" value="' . strtoupper($method) . '">';
    }
}

if (!function_exists('dd')) {
    /**
     * دالة للطباعة والتوقف (Debug)
     * 
     * @param mixed ...$vars المتغيرات
     */
    function dd(...$vars)
    {
        echo '<pre style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px; font-family: monospace;">';
        foreach ($vars as $var) {
            var_dump($var);
            echo "\n" . str_repeat('-', 50) . "\n";
        }
        echo '</pre>';
        exit;
    }
}

if (!function_exists('now')) {
    /**
     * دالة للحصول على التاريخ والوقت الحالي
     * 
     * @param string $format التنسيق
     * @return string
     */
    function now($format = 'Y-m-d H:i:s')
    {
        return date($format);
    }
}

if (!function_exists('str_random')) {
    /**
     * دالة لإنشاء نص عشوائي
     * 
     * @param int $length الطول
     * @return string
     */
    function str_random($length = 16)
    {
        return bin2hex(random_bytes($length / 2));
    }
}

if (!function_exists('str_slug')) {
    /**
     * دالة لتحويل النص إلى slug
     * 
     * @param string $title النص
     * @param string $separator الفاصل
     * @return string
     */
    function str_slug($title, $separator = '-')
    {
        // تحويل الأحرف العربية
        $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
        $english = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'th', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
        
        $title = str_replace($arabic, $english, $title);
        $title = preg_replace('/[^A-Za-z0-9-]+/', $separator, $title);
        $title = trim($title, $separator);
        $title = preg_replace('/[' . preg_quote($separator) . ']+/', $separator, $title);
        
        return strtolower($title);
    }
}

if (!function_exists('format_currency')) {
    /**
     * دالة لتنسيق العملة
     * 
     * @param float $amount المبلغ
     * @param string $currency العملة
     * @return string
     */
    function format_currency($amount, $currency = 'SAR')
    {
        return number_format($amount, 2) . ' ' . $currency;
    }
}

if (!function_exists('format_date')) {
    /**
     * دالة لتنسيق التاريخ
     * 
     * @param string $date التاريخ
     * @param string $format التنسيق
     * @return string
     */
    function format_date($date, $format = 'Y-m-d')
    {
        return date($format, strtotime($date));
    }
}

if (!function_exists('is_active_route')) {
    /**
     * دالة للتحقق من المسار النشط
     *
     * @param string $route المسار
     * @return bool
     */
    function is_active_route($route)
    {
        $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        return strpos($currentPath, $route) !== false;
    }
}

if (!function_exists('module_enabled')) {
    /**
     * دالة للتحقق من تفعيل وحدة
     *
     * @param string $module اسم الوحدة
     * @return bool
     */
    function module_enabled($module)
    {
        return config("modules.{$module}.enabled", false);
    }
}

if (!function_exists('company_setting')) {
    /**
     * دالة للحصول على إعدادات الشركة
     *
     * @param string $key المفتاح المطلوب
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    function company_setting($key, $default = null)
    {
        return config("company.{$key}", $default);
    }
}

if (!function_exists('is_environment')) {
    /**
     * دالة للتحقق من بيئة التطبيق
     *
     * @param string $env البيئة المطلوبة
     * @return bool
     */
    function is_environment($env)
    {
        return config('env') === $env;
    }
}

if (!function_exists('is_debug_mode')) {
    /**
     * دالة للتحقق من تفعيل وضع التطوير
     *
     * @return bool
     */
    function is_debug_mode()
    {
        return config('debug', false);
    }
}
?>
