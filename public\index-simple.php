<?php
/**
 * نسخة مبسطة من index.php للتطوير
 */

// بدء الجلسة
session_start();

// تعريف المسارات
define('ROOT_PATH', dirname(__DIR__));

// تعطيل الأخطاء في الإنتاج
error_reporting(E_ALL);
ini_set('display_errors', 1);

// الحصول على المسار المطلوب
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($requestUri, PHP_URL_PATH);
$path = trim($path, '/');

// إذا كان المسار فارغ، توجه إلى لوحة التحكم
if (empty($path)) {
    $path = 'dashboard';
}

// تحليل المسار
$pathParts = explode('/', $path);
$module = $pathParts[0] ?? 'dashboard';

// التوجيه حسب المسار
switch ($module) {
    case 'dashboard':
        handleDashboard();
        break;
        
    case 'test':
        include 'test.php';
        break;
        
    case 'debug':
        include 'debug.php';
        break;
        
    case 'dashboard-simple':
        include 'dashboard-simple.php';
        break;
        
    default:
        show404();
        break;
}

/**
 * معالجة لوحة التحكم
 */
function handleDashboard()
{
    // بيانات وهمية للمستخدم
    $currentUser = [
        'id' => 1,
        'username' => 'admin',
        'first_name' => 'مدير',
        'last_name' => 'النظام',
        'email' => '<EMAIL>',
        'role' => 'admin'
    ];
    
    // بيانات وهمية للإحصائيات
    $stats = [
        [
            'title' => 'إجمالي العملاء',
            'value' => '1,234',
            'icon' => 'bi-people',
            'color' => 'primary',
            'change' => '+12%',
            'url' => '/customers'
        ],
        [
            'title' => 'المنتجات',
            'value' => '567',
            'icon' => 'bi-box',
            'color' => 'success',
            'change' => '+5%',
            'url' => '/products'
        ],
        [
            'title' => 'طلبات اليوم',
            'value' => '89',
            'icon' => 'bi-cart',
            'color' => 'warning',
            'change' => '+8%',
            'url' => '/orders'
        ],
        [
            'title' => 'مبيعات اليوم',
            'value' => '45,680',
            'icon' => 'bi-currency-dollar',
            'color' => 'info',
            'change' => '+25%',
            'url' => '/sales'
        ]
    ];
    
    // إعداد متغيرات القالب
    $pageTitle = 'لوحة التحكم';
    $breadcrumbs = [
        ['title' => 'الرئيسية', 'url' => '/dashboard']
    ];
    
    // تحميل ملف لوحة التحكم
    $dashboardFile = ROOT_PATH . '/modules/dashboard/views/index.php';
    
    if (file_exists($dashboardFile)) {
        try {
            // تضمين ملف لوحة التحكم
            include $dashboardFile;
        } catch (Exception $e) {
            echo "<h1>خطأ في تحميل لوحة التحكم</h1>";
            echo "<p>تفاصيل الخطأ: " . $e->getMessage() . "</p>";
            echo "<p><a href='/dashboard-simple'>جرب لوحة التحكم المبسطة</a></p>";
        }
    } else {
        echo "<h1>ملف لوحة التحكم غير موجود</h1>";
        echo "<p>المسار المطلوب: {$dashboardFile}</p>";
        echo "<p><a href='/dashboard-simple'>جرب لوحة التحكم المبسطة</a></p>";
    }
}

/**
 * عرض صفحة 404
 */
function show404()
{
    http_response_code(404);
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>404 - الصفحة غير موجودة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
        <style>
            body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .error-container { background: white; border-radius: 20px; padding: 40px; margin: 50px auto; max-width: 600px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error-container">
                <h1 class="display-1 text-primary">404</h1>
                <h2 class="mb-4">الصفحة غير موجودة</h2>
                <p class="lead mb-4">عذراً، الصفحة التي تبحث عنها غير موجودة.</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a href="/dashboard" class="btn btn-primary btn-lg">لوحة التحكم</a>
                    <a href="/dashboard-simple" class="btn btn-outline-primary btn-lg">لوحة التحكم المبسطة</a>
                    <a href="/test" class="btn btn-outline-secondary btn-lg">اختبار النظام</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
