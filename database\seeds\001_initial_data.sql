-- البيانات الأولية لنظام SeaSystem
USE R1;

-- ===================================
-- الأدوار والصلاحيات الأساسية
-- ===================================

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, display_name, description) VALUES
('super_admin', 'مدير النظام الرئيسي', 'صلاحيات كاملة على جميع أجزاء النظام'),
('admin', 'مدير النظام', 'صلاحيات إدارية على معظم أجزاء النظام'),
('hr_manager', 'مدير الموارد البشرية', 'إدارة شؤون الموظفين والموارد البشرية'),
('finance_manager', 'مدير المالية', 'إدارة الحسابات والشؤون المالية'),
('inventory_manager', 'مدير المخزون', 'إدارة المخزون والمنتجات'),
('sales_manager', 'مدير المبيعات', 'إدارة المبيعات والعملاء'),
('employee', 'موظف', 'صلاحيات أساسية للموظفين');

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, display_name, module, description) VALUES
-- صلاحيات إدارة المستخدمين
('users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين'),
('users.create', 'إنشاء مستخدم', 'users', 'إنشاء مستخدمين جدد'),
('users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين'),
('users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين'),
('users.manage_roles', 'إدارة أدوار المستخدمين', 'users', 'تعيين وإزالة الأدوار'),

-- صلاحيات الموارد البشرية
('hr.employees.view', 'عرض الموظفين', 'hr', 'عرض قائمة الموظفين'),
('hr.employees.create', 'إنشاء موظف', 'hr', 'إضافة موظفين جدد'),
('hr.employees.edit', 'تعديل الموظفين', 'hr', 'تعديل بيانات الموظفين'),
('hr.employees.delete', 'حذف الموظفين', 'hr', 'حذف سجلات الموظفين'),
('hr.departments.manage', 'إدارة الأقسام', 'hr', 'إدارة أقسام الشركة'),
('hr.positions.manage', 'إدارة المناصب', 'hr', 'إدارة المناصب الوظيفية'),

-- صلاحيات المالية
('finance.accounts.view', 'عرض الحسابات', 'finance', 'عرض دليل الحسابات'),
('finance.accounts.manage', 'إدارة الحسابات', 'finance', 'إدارة دليل الحسابات'),
('finance.journal.view', 'عرض القيود', 'finance', 'عرض القيود المحاسبية'),
('finance.journal.create', 'إنشاء قيود', 'finance', 'إنشاء قيود محاسبية'),
('finance.journal.edit', 'تعديل القيود', 'finance', 'تعديل القيود المحاسبية'),
('finance.reports.view', 'عرض التقارير المالية', 'finance', 'عرض التقارير المالية'),

-- صلاحيات المخزون
('inventory.products.view', 'عرض المنتجات', 'inventory', 'عرض قائمة المنتجات'),
('inventory.products.create', 'إنشاء منتج', 'inventory', 'إضافة منتجات جديدة'),
('inventory.products.edit', 'تعديل المنتجات', 'inventory', 'تعديل بيانات المنتجات'),
('inventory.products.delete', 'حذف المنتجات', 'inventory', 'حذف المنتجات'),
('inventory.stock.view', 'عرض المخزون', 'inventory', 'عرض مستويات المخزون'),
('inventory.stock.manage', 'إدارة المخزون', 'inventory', 'إدارة حركات المخزون'),
('inventory.warehouses.manage', 'إدارة المخازن', 'inventory', 'إدارة المخازن'),

-- صلاحيات المبيعات
('sales.customers.view', 'عرض العملاء', 'sales', 'عرض قائمة العملاء'),
('sales.customers.create', 'إنشاء عميل', 'sales', 'إضافة عملاء جدد'),
('sales.customers.edit', 'تعديل العملاء', 'sales', 'تعديل بيانات العملاء'),
('sales.orders.view', 'عرض الطلبات', 'sales', 'عرض أوامر البيع'),
('sales.orders.create', 'إنشاء طلب', 'sales', 'إنشاء أوامر بيع جديدة'),
('sales.orders.edit', 'تعديل الطلبات', 'sales', 'تعديل أوامر البيع'),
('sales.reports.view', 'عرض تقارير المبيعات', 'sales', 'عرض تقارير المبيعات');

-- ربط الأدوار بالصلاحيات
-- مدير النظام الرئيسي - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'super_admin';

-- مدير النظام - معظم الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name NOT IN ('users.delete', 'hr.employees.delete');

-- مدير الموارد البشرية
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'hr_manager' AND p.module = 'hr';

-- مدير المالية
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'finance_manager' AND p.module = 'finance';

-- مدير المخزون
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'inventory_manager' AND p.module = 'inventory';

-- مدير المبيعات
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'sales_manager' AND p.module = 'sales';

-- ===================================
-- البيانات الأساسية للنظام
-- ===================================

-- وحدات القياس الأساسية
INSERT INTO units (name, symbol, description) VALUES
('قطعة', 'قطعة', 'وحدة العد الأساسية'),
('كيلوجرام', 'كجم', 'وحدة الوزن'),
('متر', 'م', 'وحدة الطول'),
('لتر', 'لتر', 'وحدة الحجم'),
('صندوق', 'صندوق', 'وحدة التعبئة'),
('كرتون', 'كرتون', 'وحدة التعبئة الكبيرة');

-- فئات المنتجات الأساسية
INSERT INTO product_categories (name, description) VALUES
('إلكترونيات', 'الأجهزة والمعدات الإلكترونية'),
('مكتبية', 'اللوازم والمعدات المكتبية'),
('خدمات', 'الخدمات المقدمة للعملاء'),
('مواد خام', 'المواد الخام المستخدمة في الإنتاج');

-- الأقسام الأساسية
INSERT INTO departments (name, description) VALUES
('الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي'),
('الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف'),
('المالية والمحاسبة', 'إدارة الشؤون المالية والمحاسبية'),
('المبيعات والتسويق', 'إدارة المبيعات وخدمة العملاء'),
('المخازن والمشتريات', 'إدارة المخزون والمشتريات'),
('تقنية المعلومات', 'إدارة الأنظمة والتقنية');

-- المناصب الأساسية
INSERT INTO positions (title, description, department_id, min_salary, max_salary) VALUES
('مدير عام', 'المدير العام للشركة', 1, 15000.00, 25000.00),
('مدير موارد بشرية', 'مدير قسم الموارد البشرية', 2, 8000.00, 12000.00),
('مدير مالي', 'مدير القسم المالي', 3, 10000.00, 15000.00),
('مدير مبيعات', 'مدير قسم المبيعات', 4, 8000.00, 12000.00),
('مدير مخازن', 'مدير المخازن والمشتريات', 5, 6000.00, 10000.00),
('مدير تقنية', 'مدير قسم تقنية المعلومات', 6, 9000.00, 14000.00),
('محاسب', 'محاسب عام', 3, 4000.00, 7000.00),
('مندوب مبيعات', 'مندوب مبيعات', 4, 3000.00, 6000.00),
('أمين مخزن', 'أمين مخزن', 5, 2500.00, 4500.00);

-- المخازن الأساسية
INSERT INTO warehouses (name, location) VALUES
('المخزن الرئيسي', 'المقر الرئيسي - الطابق الأرضي'),
('مخزن المواد الخام', 'المقر الرئيسي - الطابق السفلي'),
('مخزن المنتجات الجاهزة', 'المقر الرئيسي - الطابق الأول');

-- دليل الحسابات الأساسي
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
-- الأصول
('1000', 'الأصول', 'asset'),
('1100', 'الأصول المتداولة', 'asset'),
('1110', 'النقدية', 'asset'),
('1120', 'البنوك', 'asset'),
('1130', 'العملاء', 'asset'),
('1140', 'المخزون', 'asset'),
('1200', 'الأصول الثابتة', 'asset'),
('1210', 'الأثاث والمعدات', 'asset'),
('1220', 'السيارات', 'asset'),

-- الخصوم
('2000', 'الخصوم', 'liability'),
('2100', 'الخصوم المتداولة', 'liability'),
('2110', 'الموردون', 'liability'),
('2120', 'المصروفات المستحقة', 'liability'),
('2200', 'الخصوم طويلة الأجل', 'liability'),
('2210', 'القروض طويلة الأجل', 'liability'),

-- حقوق الملكية
('3000', 'حقوق الملكية', 'equity'),
('3100', 'رأس المال', 'equity'),
('3200', 'الأرباح المحتجزة', 'equity'),

-- الإيرادات
('4000', 'الإيرادات', 'revenue'),
('4100', 'إيرادات المبيعات', 'revenue'),
('4200', 'إيرادات أخرى', 'revenue'),

-- المصروفات
('5000', 'المصروفات', 'expense'),
('5100', 'تكلفة البضاعة المباعة', 'expense'),
('5200', 'مصروفات التشغيل', 'expense'),
('5210', 'الرواتب والأجور', 'expense'),
('5220', 'الإيجار', 'expense'),
('5230', 'الكهرباء والماء', 'expense'),
('5240', 'الاتصالات', 'expense'),
('5250', 'مصروفات أخرى', 'expense');

-- تحديث العلاقات الهرمية لدليل الحسابات
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '1000') AS temp) WHERE account_code = '1100';
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '1000') AS temp) WHERE account_code = '1200';
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '1100') AS temp) WHERE account_code IN ('1110', '1120', '1130', '1140');
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '1200') AS temp) WHERE account_code IN ('1210', '1220');

UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '2000') AS temp) WHERE account_code = '2100';
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '2000') AS temp) WHERE account_code = '2200';
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '2100') AS temp) WHERE account_code IN ('2110', '2120');
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '2200') AS temp) WHERE account_code = '2210';

UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '3000') AS temp) WHERE account_code IN ('3100', '3200');
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '4000') AS temp) WHERE account_code IN ('4100', '4200');
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '5000') AS temp) WHERE account_code IN ('5100', '5200');
UPDATE chart_of_accounts SET parent_id = (SELECT id FROM (SELECT id FROM chart_of_accounts WHERE account_code = '5200') AS temp) WHERE account_code IN ('5210', '5220', '5230', '5240', '5250');

-- إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
('company_name', 'شركة SeaSystem', 'اسم الشركة', TRUE),
('company_address', '', 'عنوان الشركة', TRUE),
('company_phone', '', 'هاتف الشركة', TRUE),
('company_email', '', 'بريد الشركة الإلكتروني', TRUE),
('default_currency', 'SAR', 'العملة الافتراضية', TRUE),
('tax_rate', '15', 'معدل الضريبة المضافة (%)', TRUE),
('session_timeout', '3600', 'مدة انتهاء الجلسة (بالثواني)', FALSE),
('max_login_attempts', '5', 'عدد محاولات تسجيل الدخول المسموحة', FALSE);
