<?php
/**
 * SeaSystem Database Setup Script
 * سكريبت إعداد قاعدة البيانات
 */

echo "🗄️  إعداد قاعدة البيانات - SeaSystem ERP\n";
echo "==========================================\n\n";

try {
    // تحميل إعدادات البيئة
    require_once 'core/Environment.php';
    Environment::load();
    
    $host = Environment::get('DB_HOST', 'localhost');
    $port = Environment::get('DB_PORT', 3306);
    $dbname = Environment::get('DB_NAME', 'R1');
    $username = Environment::get('DB_USERNAME', 'root');
    $password = Environment::get('DB_PASSWORD', '');
    
    echo "📋 إعدادات قاعدة البيانات:\n";
    echo "   🖥️  الخادم: {$host}:{$port}\n";
    echo "   🗄️  قاعدة البيانات: {$dbname}\n";
    echo "   👤 المستخدم: {$username}\n\n";
    
    // الاتصال بخادم MySQL (بدون تحديد قاعدة البيانات)
    echo "🔌 الاتصال بخادم MySQL...\n";
    $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ تم الاتصال بنجاح\n\n";
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    echo "🏗️  إنشاء قاعدة البيانات...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "   ✅ تم إنشاء/التحقق من قاعدة البيانات {$dbname}\n\n";
    
    // الاتصال بقاعدة البيانات المحددة
    $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // تشغيل ملفات الترحيل
    echo "📊 تشغيل ملفات الترحيل...\n";
    
    $migrationFile = 'database/migrations/001_create_database_schema.sql';
    if (file_exists($migrationFile)) {
        echo "   📄 تشغيل: {$migrationFile}\n";
        $sql = file_get_contents($migrationFile);
        
        // تقسيم الاستعلامات
        $statements = explode(';', $sql);
        $executedStatements = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                    $executedStatements++;
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة مسبقاً
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "   ⚠️  تحذير: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        
        echo "   ✅ تم تنفيذ {$executedStatements} استعلام\n";
    } else {
        echo "   ❌ ملف الترحيل غير موجود: {$migrationFile}\n";
    }
    
    // تشغيل البيانات الأولية
    echo "\n🌱 إدراج البيانات الأولية...\n";
    
    $seedFile = 'database/seeds/001_initial_data.sql';
    if (file_exists($seedFile)) {
        echo "   📄 تشغيل: {$seedFile}\n";
        $sql = file_get_contents($seedFile);
        
        // تقسيم الاستعلامات
        $statements = explode(';', $sql);
        $executedStatements = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                    $executedStatements++;
                } catch (PDOException $e) {
                    // تجاهل أخطاء البيانات المكررة
                    if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                        echo "   ⚠️  تحذير: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        
        echo "   ✅ تم تنفيذ {$executedStatements} استعلام\n";
    } else {
        echo "   ❌ ملف البيانات الأولية غير موجود: {$seedFile}\n";
    }
    
    // التحقق من الجداول المنشأة
    echo "\n📋 التحقق من الجداول المنشأة...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "   📊 تم إنشاء " . count($tables) . " جدول:\n";
    foreach ($tables as $table) {
        // عد السجلات في كل جدول
        try {
            $countStmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
            $count = $countStmt->fetchColumn();
            echo "      📄 {$table} ({$count} سجل)\n";
        } catch (Exception $e) {
            echo "      📄 {$table} (خطأ في العد)\n";
        }
    }
    
    // التحقق من المستخدم التجريبي
    echo "\n👤 التحقق من المستخدم التجريبي...\n";
    $stmt = $pdo->prepare("SELECT username, email, first_name, last_name FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($adminUser) {
        echo "   ✅ المستخدم التجريبي موجود:\n";
        echo "      👤 اسم المستخدم: {$adminUser['username']}\n";
        echo "      📧 البريد الإلكتروني: {$adminUser['email']}\n";
        echo "      🏷️  الاسم: {$adminUser['first_name']} {$adminUser['last_name']}\n";
        echo "      🔑 كلمة المرور: admin123\n";
    } else {
        echo "   ❌ المستخدم التجريبي غير موجود\n";
        
        // إنشاء المستخدم التجريبي
        echo "   📝 إنشاء المستخدم التجريبي...\n";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, first_name, last_name, is_active, created_at) 
            VALUES ('admin', '<EMAIL>', ?, 'مدير', 'النظام', 1, NOW())
        ");
        $stmt->execute([$hashedPassword]);
        
        // تعيين دور مدير النظام
        $userId = $pdo->lastInsertId();
        $stmt = $pdo->prepare("
            INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at) 
            VALUES (?, (SELECT id FROM roles WHERE name = 'super_admin'), ?, NOW())
        ");
        $stmt->execute([$userId, $userId]);
        
        echo "   ✅ تم إنشاء المستخدم التجريبي\n";
    }
    
    // إنشاء فهارس إضافية للأداء
    echo "\n⚡ تحسين الأداء...\n";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status)",
        "CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department_id)",
        "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_activity_logs_created ON activity_logs(created_at)",
    ];
    
    foreach ($indexes as $indexSql) {
        try {
            $pdo->exec($indexSql);
            echo "   ✅ تم إنشاء فهرس\n";
        } catch (Exception $e) {
            // تجاهل أخطاء الفهارس الموجودة
        }
    }
    
    // إحصائيات نهائية
    echo "\n📊 إحصائيات قاعدة البيانات:\n";
    
    // حجم قاعدة البيانات
    $stmt = $pdo->query("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = '{$dbname}'
    ");
    $size = $stmt->fetchColumn();
    echo "   💾 حجم قاعدة البيانات: {$size} ميجابايت\n";
    
    // عدد الجداول
    echo "   📋 عدد الجداول: " . count($tables) . "\n";
    
    // إجمالي السجلات
    $totalRecords = 0;
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
            $totalRecords += $stmt->fetchColumn();
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }
    echo "   📄 إجمالي السجلات: {$totalRecords}\n";
    
    echo "\n🎉 تم إعداد قاعدة البيانات بنجاح!\n";
    echo "==========================================\n";
    echo "💡 يمكنك الآن تشغيل النظام:\n";
    echo "   🌐 http://localhost/seasystem-R1/public\n";
    echo "   👤 اسم المستخدم: admin\n";
    echo "   🔑 كلمة المرور: admin123\n\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ في إعداد قاعدة البيانات:\n";
    echo "   " . $e->getMessage() . "\n\n";
    echo "💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة إعدادات الاتصال في ملف .env\n";
    echo "   - وجود صلاحيات إنشاء قواعد البيانات\n\n";
    exit(1);
}
?>
