<?php
/**
 * SeaSystem Backup Manager
 * نظام النسخ الاحتياطي
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Database.php';
require_once 'Logger.php';
require_once dirname(__DIR__) . '/config/app.php';

class Backup
{
    private $db;
    private $config;
    private $backupPath;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->config = config('backup');
        $this->backupPath = $this->config['storage_path'];
        
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }

    /**
     * إنشاء نسخة احتياطية كاملة
     * 
     * @return array
     */
    public function createFullBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupName = "seasystem_backup_{$timestamp}";
            $backupDir = $this->backupPath . '/' . $backupName;
            
            // إنشاء مجلد النسخة الاحتياطية
            mkdir($backupDir, 0755, true);
            
            Logger::info("بدء إنشاء النسخة الاحتياطية: {$backupName}");
            
            // نسخ قاعدة البيانات
            $dbBackupFile = $this->backupDatabase($backupDir);
            
            // نسخ الملفات
            $filesBackupDir = null;
            if ($this->config['include_uploads']) {
                $filesBackupDir = $this->backupFiles($backupDir);
            }
            
            // إنشاء ملف معلومات النسخة الاحتياطية
            $this->createBackupInfo($backupDir, $dbBackupFile, $filesBackupDir);
            
            // ضغط النسخة الاحتياطية
            $archiveFile = null;
            if ($this->config['compression']) {
                $archiveFile = $this->compressBackup($backupDir, $backupName);
                // حذف المجلد غير المضغوط
                $this->deleteDirectory($backupDir);
            }
            
            // تنظيف النسخ القديمة
            $this->cleanOldBackups();
            
            Logger::info("تم إنشاء النسخة الاحتياطية بنجاح: {$backupName}");
            
            return [
                'success' => true,
                'backup_name' => $backupName,
                'backup_path' => $archiveFile ?: $backupDir,
                'size' => $this->getBackupSize($archiveFile ?: $backupDir),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            Logger::error("فشل في إنشاء النسخة الاحتياطية: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * نسخ قاعدة البيانات
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     * @return string مسار ملف النسخة الاحتياطية
     */
    private function backupDatabase($backupDir)
    {
        $dbConfig = config('database.connections.mysql');
        $filename = $backupDir . '/database.sql';
        
        // إنشاء أمر mysqldump
        $command = sprintf(
            'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['port']),
            escapeshellarg($dbConfig['username']),
            escapeshellarg($dbConfig['password']),
            escapeshellarg($dbConfig['database']),
            escapeshellarg($filename)
        );
        
        // تنفيذ الأمر
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            // في حالة فشل mysqldump، استخدم PHP
            $this->backupDatabaseWithPHP($filename);
        }
        
        if (!file_exists($filename) || filesize($filename) === 0) {
            throw new Exception('فشل في نسخ قاعدة البيانات');
        }
        
        return $filename;
    }

    /**
     * نسخ قاعدة البيانات باستخدام PHP
     * 
     * @param string $filename اسم الملف
     */
    private function backupDatabaseWithPHP($filename)
    {
        $tables = $this->db->select("SHOW TABLES");
        $sql = "-- SeaSystem Database Backup\n";
        $sql .= "-- Created: " . date('Y-m-d H:i:s') . "\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            
            // هيكل الجدول
            $createTable = $this->db->selectOne("SHOW CREATE TABLE `{$tableName}`");
            $sql .= "-- Table: {$tableName}\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $sql .= $createTable['Create Table'] . ";\n\n";
            
            // بيانات الجدول
            $rows = $this->db->select("SELECT * FROM `{$tableName}`");
            if (!empty($rows)) {
                $sql .= "-- Data for table: {$tableName}\n";
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                    }, array_values($row));
                    
                    $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql .= "\n";
            }
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        file_put_contents($filename, $sql);
    }

    /**
     * نسخ الملفات
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     * @return string مسار مجلد الملفات
     */
    private function backupFiles($backupDir)
    {
        $filesDir = $backupDir . '/files';
        mkdir($filesDir, 0755, true);
        
        $sourceDir = dirname(__DIR__) . '/uploads';
        
        if (is_dir($sourceDir)) {
            $this->copyDirectory($sourceDir, $filesDir . '/uploads');
        }
        
        // نسخ ملفات التكوين المهمة
        $configFiles = [
            dirname(__DIR__) . '/config/.env',
            dirname(__DIR__) . '/config/app.php'
        ];
        
        $configDir = $filesDir . '/config';
        mkdir($configDir, 0755, true);
        
        foreach ($configFiles as $file) {
            if (file_exists($file)) {
                copy($file, $configDir . '/' . basename($file));
            }
        }
        
        return $filesDir;
    }

    /**
     * إنشاء ملف معلومات النسخة الاحتياطية
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     * @param string $dbFile ملف قاعدة البيانات
     * @param string $filesDir مجلد الملفات
     */
    private function createBackupInfo($backupDir, $dbFile, $filesDir)
    {
        $info = [
            'backup_name' => basename($backupDir),
            'created_at' => date('Y-m-d H:i:s'),
            'version' => config('version', '1.0.0'),
            'database_file' => basename($dbFile),
            'includes_files' => $filesDir !== null,
            'files_directory' => $filesDir ? basename($filesDir) : null,
            'compression' => $this->config['compression'],
            'encryption' => $this->config['encryption']
        ];
        
        file_put_contents(
            $backupDir . '/backup_info.json',
            json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
    }

    /**
     * ضغط النسخة الاحتياطية
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     * @param string $backupName اسم النسخة الاحتياطية
     * @return string مسار الملف المضغوط
     */
    private function compressBackup($backupDir, $backupName)
    {
        $archiveFile = $this->backupPath . '/' . $backupName . '.tar.gz';
        
        // استخدام tar إذا كان متاحاً
        if (function_exists('exec')) {
            $command = sprintf(
                'cd %s && tar -czf %s %s',
                escapeshellarg(dirname($backupDir)),
                escapeshellarg($archiveFile),
                escapeshellarg(basename($backupDir))
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($archiveFile)) {
                return $archiveFile;
            }
        }
        
        // استخدام ZipArchive كبديل
        if (class_exists('ZipArchive')) {
            $archiveFile = $this->backupPath . '/' . $backupName . '.zip';
            $zip = new ZipArchive();
            
            if ($zip->open($archiveFile, ZipArchive::CREATE) === TRUE) {
                $this->addDirectoryToZip($zip, $backupDir, basename($backupDir));
                $zip->close();
                return $archiveFile;
            }
        }
        
        throw new Exception('فشل في ضغط النسخة الاحتياطية');
    }

    /**
     * إضافة مجلد إلى ملف ZIP
     * 
     * @param ZipArchive $zip
     * @param string $dir المجلد
     * @param string $base المسار الأساسي
     */
    private function addDirectoryToZip($zip, $dir, $base = '')
    {
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $filePath = $dir . '/' . $file;
            $zipPath = $base . '/' . $file;
            
            if (is_dir($filePath)) {
                $zip->addEmptyDir($zipPath);
                $this->addDirectoryToZip($zip, $filePath, $zipPath);
            } else {
                $zip->addFile($filePath, $zipPath);
            }
        }
    }

    /**
     * نسخ مجلد
     * 
     * @param string $source المصدر
     * @param string $destination الوجهة
     */
    private function copyDirectory($source, $destination)
    {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $files = scandir($source);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $sourcePath = $source . '/' . $file;
            $destPath = $destination . '/' . $file;
            
            if (is_dir($sourcePath)) {
                $this->copyDirectory($sourcePath, $destPath);
            } else {
                copy($sourcePath, $destPath);
            }
        }
    }

    /**
     * حذف مجلد
     * 
     * @param string $dir المجلد
     */
    private function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $filePath = $dir . '/' . $file;
            
            if (is_dir($filePath)) {
                $this->deleteDirectory($filePath);
            } else {
                unlink($filePath);
            }
        }
        
        rmdir($dir);
    }

    /**
     * الحصول على حجم النسخة الاحتياطية
     * 
     * @param string $path المسار
     * @return string
     */
    private function getBackupSize($path)
    {
        if (is_file($path)) {
            $size = filesize($path);
        } else {
            $size = $this->getDirectorySize($path);
        }
        
        return $this->formatBytes($size);
    }

    /**
     * الحصول على حجم مجلد
     * 
     * @param string $dir المجلد
     * @return int
     */
    private function getDirectorySize($dir)
    {
        $size = 0;
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $filePath = $dir . '/' . $file;
            
            if (is_dir($filePath)) {
                $size += $this->getDirectorySize($filePath);
            } else {
                $size += filesize($filePath);
            }
        }
        
        return $size;
    }

    /**
     * تنسيق حجم الملف
     * 
     * @param int $bytes البايتات
     * @return string
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * تنظيف النسخ القديمة
     */
    private function cleanOldBackups()
    {
        $retentionDays = $this->config['retention_days'];
        $cutoffTime = time() - ($retentionDays * 24 * 60 * 60);
        
        $files = glob($this->backupPath . '/*');
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (is_dir($file)) {
                    $this->deleteDirectory($file);
                } else {
                    unlink($file);
                }
                
                Logger::info("تم حذف النسخة الاحتياطية القديمة: " . basename($file));
            }
        }
    }

    /**
     * الحصول على قائمة النسخ الاحتياطية
     * 
     * @return array
     */
    public function getBackupList()
    {
        $backups = [];
        $files = glob($this->backupPath . '/*');
        
        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => $this->getBackupSize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                'is_compressed' => pathinfo($file, PATHINFO_EXTENSION) !== ''
            ];
        }
        
        // ترتيب حسب تاريخ الإنشاء
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return $backups;
    }

    /**
     * حذف نسخة احتياطية
     * 
     * @param string $backupName اسم النسخة الاحتياطية
     * @return bool
     */
    public function deleteBackup($backupName)
    {
        try {
            $backupPath = $this->backupPath . '/' . $backupName;
            
            if (!file_exists($backupPath)) {
                throw new Exception('النسخة الاحتياطية غير موجودة');
            }
            
            if (is_dir($backupPath)) {
                $this->deleteDirectory($backupPath);
            } else {
                unlink($backupPath);
            }
            
            Logger::info("تم حذف النسخة الاحتياطية: {$backupName}");
            return true;
            
        } catch (Exception $e) {
            Logger::error("فشل في حذف النسخة الاحتياطية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * استعادة نسخة احتياطية
     * 
     * @param string $backupName اسم النسخة الاحتياطية
     * @return array
     */
    public function restoreBackup($backupName)
    {
        try {
            Logger::warning("بدء استعادة النسخة الاحتياطية: {$backupName}");
            
            $backupPath = $this->backupPath . '/' . $backupName;
            
            if (!file_exists($backupPath)) {
                throw new Exception('النسخة الاحتياطية غير موجودة');
            }
            
            // استخراج النسخة الاحتياطية إذا كانت مضغوطة
            $extractedPath = $this->extractBackup($backupPath);
            
            // استعادة قاعدة البيانات
            $this->restoreDatabase($extractedPath);
            
            // استعادة الملفات
            $this->restoreFiles($extractedPath);
            
            Logger::info("تم استعادة النسخة الاحتياطية بنجاح: {$backupName}");
            
            return [
                'success' => true,
                'message' => 'تم استعادة النسخة الاحتياطية بنجاح'
            ];
            
        } catch (Exception $e) {
            Logger::error("فشل في استعادة النسخة الاحتياطية: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * استخراج النسخة الاحتياطية
     * 
     * @param string $backupPath مسار النسخة الاحتياطية
     * @return string مسار المجلد المستخرج
     */
    private function extractBackup($backupPath)
    {
        if (is_dir($backupPath)) {
            return $backupPath;
        }
        
        $extractPath = $this->backupPath . '/temp_restore_' . time();
        mkdir($extractPath, 0755, true);
        
        $extension = pathinfo($backupPath, PATHINFO_EXTENSION);
        
        if ($extension === 'zip') {
            $zip = new ZipArchive();
            if ($zip->open($backupPath) === TRUE) {
                $zip->extractTo($extractPath);
                $zip->close();
            } else {
                throw new Exception('فشل في استخراج ملف ZIP');
            }
        } else {
            // استخدام tar
            $command = sprintf(
                'cd %s && tar -xzf %s',
                escapeshellarg($extractPath),
                escapeshellarg($backupPath)
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new Exception('فشل في استخراج ملف tar');
            }
        }
        
        return $extractPath;
    }

    /**
     * استعادة قاعدة البيانات
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     */
    private function restoreDatabase($backupDir)
    {
        $sqlFile = $backupDir . '/database.sql';
        
        if (!file_exists($sqlFile)) {
            // البحث في المجلدات الفرعية
            $files = glob($backupDir . '/*/database.sql');
            if (!empty($files)) {
                $sqlFile = $files[0];
            } else {
                throw new Exception('ملف قاعدة البيانات غير موجود');
            }
        }
        
        $dbConfig = config('database.connections.mysql');
        
        // استخدام mysql command line
        $command = sprintf(
            'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['port']),
            escapeshellarg($dbConfig['username']),
            escapeshellarg($dbConfig['password']),
            escapeshellarg($dbConfig['database']),
            escapeshellarg($sqlFile)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            // استخدام PHP كبديل
            $this->restoreDatabaseWithPHP($sqlFile);
        }
    }

    /**
     * استعادة قاعدة البيانات باستخدام PHP
     * 
     * @param string $sqlFile ملف SQL
     */
    private function restoreDatabaseWithPHP($sqlFile)
    {
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->db->execute($statement);
            }
        }
    }

    /**
     * استعادة الملفات
     * 
     * @param string $backupDir مجلد النسخة الاحتياطية
     */
    private function restoreFiles($backupDir)
    {
        $filesDir = $backupDir . '/files';
        
        if (!is_dir($filesDir)) {
            // البحث في المجلدات الفرعية
            $dirs = glob($backupDir . '/*/files');
            if (!empty($dirs)) {
                $filesDir = $dirs[0];
            } else {
                return; // لا توجد ملفات للاستعادة
            }
        }
        
        // استعادة ملفات الرفع
        $uploadsSource = $filesDir . '/uploads';
        $uploadsDestination = dirname(__DIR__) . '/uploads';
        
        if (is_dir($uploadsSource)) {
            if (is_dir($uploadsDestination)) {
                $this->deleteDirectory($uploadsDestination);
            }
            $this->copyDirectory($uploadsSource, $uploadsDestination);
        }
        
        // استعادة ملفات التكوين
        $configSource = $filesDir . '/config';
        $configDestination = dirname(__DIR__) . '/config';
        
        if (is_dir($configSource)) {
            $configFiles = scandir($configSource);
            foreach ($configFiles as $file) {
                if ($file !== '.' && $file !== '..') {
                    $sourcePath = $configSource . '/' . $file;
                    $destPath = $configDestination . '/' . $file;
                    
                    if (is_file($sourcePath)) {
                        copy($sourcePath, $destPath);
                    }
                }
            }
        }
    }
}
