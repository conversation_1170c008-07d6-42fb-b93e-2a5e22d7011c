<?php
/**
 * قائمة العملاء
 */
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - SeaSystem ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-center mb-4">
                        <i class="bi bi-water"></i> SeaSystem
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-2"></i> لوحة التحكم
                        </a>
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people me-2"></i> المستخدمين
                        </a>
                        <a class="nav-link active" href="/customers">
                            <i class="bi bi-person-badge me-2"></i> العملاء
                        </a>
                        <a class="nav-link" href="/products">
                            <i class="bi bi-box me-2"></i> المنتجات
                        </a>
                        <a class="nav-link" href="/sales/orders">
                            <i class="bi bi-cart me-2"></i> الطلبات
                        </a>
                        <a class="nav-link" href="/finance/invoices">
                            <i class="bi bi-receipt me-2"></i> الفواتير
                        </a>
                        <a class="nav-link" href="/reports">
                            <i class="bi bi-graph-up me-2"></i> التقارير
                        </a>
                        <a class="nav-link" href="/settings">
                            <i class="bi bi-gear me-2"></i> الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="/logout">
                            <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white rounded mb-4 shadow-sm">
                    <div class="container-fluid">
                        <span class="navbar-brand">إدارة العملاء</span>
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-2"></i>
                                    مدير النظام
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i> الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear me-2"></i> الإعدادات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Content -->
                <div class="card">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-person-badge me-2"></i>
                            قائمة العملاء
                        </h5>
                        <a href="/customers/create" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($customers)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-person-badge" style="font-size: 4rem; color: #dee2e6;"></i>
                                <h4 class="text-muted mt-3">لا توجد عملاء</h4>
                                <p class="text-muted">ابدأ بإضافة عميل جديد للنظام</p>
                                <a href="/customers/create" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إضافة عميل جديد
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>كود العميل</th>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                            <th>نوع العميل</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($customers as $customer): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary"><?= htmlspecialchars($customer['customer_code']) ?></span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-info rounded-circle d-flex align-items-center justify-content-center text-white me-3">
                                                            <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?= htmlspecialchars($customer['name']) ?></div>
                                                            <?php if (!empty($customer['contact_person'])): ?>
                                                                <small class="text-muted"><?= htmlspecialchars($customer['contact_person']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?= htmlspecialchars($customer['email'] ?? '-') ?></td>
                                                <td><?= htmlspecialchars($customer['phone'] ?? '-') ?></td>
                                                <td>
                                                    <?php if ($customer['customer_type'] === 'company'): ?>
                                                        <span class="badge bg-primary">شركة</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">فرد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($customer['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= date('Y-m-d', strtotime($customer['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="/customers/show?id=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="/customers/edit?id=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(<?= $customer['id'] ?>)" title="حذف">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteCustomer(customerId) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                fetch(`/customers/delete?id=${customerId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الحذف');
                });
            }
        }
    </script>
</body>
</html>
