/**
 * SeaSystem ERP - مكتبة JavaScript الأساسية
 * Modern JavaScript Library with ES6+ Features
 */

class SeaSystem {
    constructor() {
        this.config = {
            apiUrl: '/api',
            timeout: 30000,
            retryAttempts: 3,
            debug: false
        };
        
        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAjaxDefaults();
        this.initializeNotifications();
        
        if (this.config.debug) {
            console.log('🌊 SeaSystem ERP initialized');
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تأكيد الحذف
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-confirm]')) {
                e.preventDefault();
                this.confirmAction(e.target);
            }
        });

        // إرسال النماذج بـ AJAX
        document.addEventListener('submit', (e) => {
            if (e.target.matches('[data-ajax]')) {
                e.preventDefault();
                this.submitForm(e.target);
            }
        });

        // تحميل المحتوى بـ AJAX
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-load]')) {
                e.preventDefault();
                this.loadContent(e.target);
            }
        });
    }

    /**
     * تهيئة المكونات
     */
    initializeComponents() {
        this.initializeTables();
        this.initializeModals();
        this.initializeTooltips();
        this.initializeCharts();
    }

    /**
     * إعداد AJAX الافتراضي
     */
    setupAjaxDefaults() {
        // إضافة CSRF token لجميع طلبات AJAX
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.csrfToken = token.getAttribute('content');
        }
    }

    /**
     * تهيئة نظام الإشعارات
     */
    initializeNotifications() {
        // إنشاء حاوي الإشعارات
        if (!document.getElementById('notifications-container')) {
            const container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
        }
    }

    /**
     * عرض إشعار
     */
    notify(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        const notification = document.createElement('div');
        
        const typeClasses = {
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-yellow-500 text-white',
            info: 'bg-blue-500 text-white'
        };

        notification.className = `
            ${typeClasses[type] || typeClasses.info}
            px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300
            translate-x-full opacity-0 max-w-sm
        `;
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // تحريك الإشعار للداخل
        setTimeout(() => {
            notification.classList.remove('translate-x-full', 'opacity-0');
        }, 100);

        // إزالة الإشعار تلقائياً
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }

        return notification;
    }

    /**
     * تأكيد الإجراء
     */
    async confirmAction(element) {
        const message = element.getAttribute('data-confirm') || 'هل أنت متأكد؟';
        const confirmed = await this.showConfirmDialog(message);
        
        if (confirmed) {
            const url = element.href || element.getAttribute('data-url');
            const method = element.getAttribute('data-method') || 'GET';
            
            if (url) {
                this.makeRequest(url, { method });
            }
        }
    }

    /**
     * عرض مربع حوار التأكيد
     */
    showConfirmDialog(message) {
        return new Promise((resolve) => {
            // يمكن استبدال هذا بمكتبة مودال أكثر تقدماً
            const result = confirm(message);
            resolve(result);
        });
    }

    /**
     * إرسال نموذج بـ AJAX
     */
    async submitForm(form) {
        const submitBtn = form.querySelector('[type="submit"]');
        const originalText = submitBtn?.innerHTML;
        
        try {
            // تعطيل الزر وإظهار التحميل
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري المعالجة...';
            }

            const formData = new FormData(form);
            const response = await this.makeRequest(form.action, {
                method: form.method || 'POST',
                body: formData
            });

            if (response.success) {
                this.notify(response.message || 'تم الحفظ بنجاح', 'success');
                
                // إعادة التوجيه إذا كان مطلوباً
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1000);
                }
            } else {
                this.notify(response.message || 'حدث خطأ', 'error');
            }

        } catch (error) {
            this.notify('حدث خطأ في الشبكة', 'error');
            console.error('Form submission error:', error);
        } finally {
            // إعادة تفعيل الزر
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }
    }

    /**
     * تحميل المحتوى
     */
    async loadContent(element) {
        const url = element.getAttribute('data-load');
        const target = element.getAttribute('data-target');
        
        if (!url || !target) return;

        const targetElement = document.querySelector(target);
        if (!targetElement) return;

        try {
            // إظهار مؤشر التحميل
            targetElement.innerHTML = '<div class="text-center py-4"><i class="bi bi-hourglass-split"></i> جاري التحميل...</div>';

            const response = await this.makeRequest(url);
            targetElement.innerHTML = response.html || response;

        } catch (error) {
            targetElement.innerHTML = '<div class="text-center py-4 text-red-500">حدث خطأ في التحميل</div>';
            console.error('Content loading error:', error);
        }
    }

    /**
     * إجراء طلب HTTP
     */
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        // إضافة CSRF token
        if (this.csrfToken && options.method !== 'GET') {
            defaultOptions.headers['X-CSRF-TOKEN'] = this.csrfToken;
        }

        const finalOptions = { ...defaultOptions, ...options };

        // إذا كان FormData، إزالة Content-Type
        if (finalOptions.body instanceof FormData) {
            delete finalOptions.headers['Content-Type'];
        }

        const response = await fetch(url, finalOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    }

    /**
     * تهيئة الجداول
     */
    initializeTables() {
        const tables = document.querySelectorAll('[data-table]');
        tables.forEach(table => {
            this.enhanceTable(table);
        });
    }

    /**
     * تحسين الجدول
     */
    enhanceTable(table) {
        // إضافة البحث
        const searchInput = table.querySelector('[data-search]');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterTable(table, e.target.value);
            });
        }

        // إضافة الترتيب
        const sortableHeaders = table.querySelectorAll('[data-sort]');
        sortableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    /**
     * فلترة الجدول
     */
    filterTable(table, searchTerm) {
        const rows = table.querySelectorAll('tbody tr');
        const term = searchTerm.toLowerCase();

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(term) ? '' : 'none';
        });
    }

    /**
     * ترتيب الجدول
     */
    sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            const aValue = isNaN(aText) ? aText : parseFloat(aText);
            const bValue = isNaN(bText) ? bText : parseFloat(bText);

            if (isAscending) {
                return aValue > bValue ? -1 : 1;
            } else {
                return aValue < bValue ? -1 : 1;
            }
        });

        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));

        // تحديث أيقونة الترتيب
        table.querySelectorAll('[data-sort]').forEach(h => {
            h.classList.remove('sort-asc', 'sort-desc');
        });
        
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
    }

    /**
     * تهيئة المودالات
     */
    initializeModals() {
        // سيتم إضافة نظام مودال متقدم لاحقاً
    }

    /**
     * تهيئة التلميحات
     */
    initializeTooltips() {
        // سيتم إضافة نظام تلميحات متقدم لاحقاً
    }

    /**
     * تهيئة الرسوم البيانية
     */
    initializeCharts() {
        // سيتم إضافة دعم Chart.js لاحقاً
    }

    /**
     * تنسيق العملة
     */
    formatCurrency(amount, currency = 'SAR') {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat('ar-SA', { ...defaultOptions, ...options }).format(new Date(date));
    }

    /**
     * تنسيق الأرقام
     */
    formatNumber(number, decimals = 2) {
        return new Intl.NumberFormat('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.SeaSystem = new SeaSystem();
});

// تصدير للاستخدام كوحدة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SeaSystem;
}
