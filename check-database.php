<?php
/**
 * فحص قاعدة البيانات
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📊 الجداول الموجودة في قاعدة البيانات:\n";
    echo "=====================================\n";

    $result = $pdo->query('SHOW TABLES');
    $tables = [];
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
        echo "✅ " . $row[0] . "\n";
    }

    echo "\n📈 إحصائيات الجداول:\n";
    echo "===================\n";
    
    foreach ($tables as $table) {
        $countResult = $pdo->query("SELECT COUNT(*) as count FROM `{$table}`");
        $count = $countResult->fetch(PDO::FETCH_ASSOC)['count'];
        echo "📋 {$table}: {$count} سجل\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
