<?php
/**
 * اختبار تسجيل دخول مباشر
 */

// بدء الجلسة
session_start();

// تحميل الملفات الأساسية
require_once 'core/Environment.php';
Environment::load();
require_once 'core/helpers.php';
require_once 'core/Database.php';
require_once 'core/Session.php';
require_once 'core/Auth.php';

echo "🧪 اختبار تسجيل الدخول المباشر\n";
echo "================================\n\n";

try {
    // إنشاء مثيل Auth
    $auth = new Auth();
    
    // محاولة تسجيل الدخول
    echo "📝 محاولة تسجيل الدخول...\n";
    $result = $auth->login('admin', 'admin123', false);
    
    echo "📊 النتيجة:\n";
    print_r($result);
    
    if ($result['success']) {
        echo "\n✅ تم تسجيل الدخول بنجاح!\n";
        
        // التحقق من الجلسة
        echo "\n🔗 معلومات الجلسة:\n";
        print_r($_SESSION);
        
        // التحقق من المستخدم الحالي
        $currentUser = $auth->user();
        if ($currentUser) {
            echo "\n👤 المستخدم الحالي:\n";
            print_r($currentUser);
        }
        
    } else {
        echo "\n❌ فشل تسجيل الدخول: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
    echo "التفاصيل:\n" . $e->getTraceAsString() . "\n";
}
?>
