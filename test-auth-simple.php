<?php
/**
 * اختبار بسيط لكلاس Auth
 */

echo "🔍 اختبار تحميل الملفات...\n";

// تحميل Environment
echo "1. تحميل Environment...\n";
require_once 'core/Environment.php';
Environment::load();
echo "   ✅ تم تحميل Environment\n";

// تحميل helpers
echo "2. تحميل helpers...\n";
require_once 'core/helpers.php';
echo "   ✅ تم تحميل helpers\n";

// تحميل Database
echo "3. تحميل Database...\n";
require_once 'core/Database.php';
echo "   ✅ تم تحميل Database\n";

// اختبار Database
echo "4. اختبار Database...\n";
try {
    $db = Database::getInstance();
    echo "   ✅ تم إنشاء مثيل Database\n";
} catch (Exception $e) {
    echo "   ❌ خطأ في Database: " . $e->getMessage() . "\n";
    exit(1);
}

// تحميل Session
echo "5. تحميل Session...\n";
require_once 'core/Session.php';
echo "   ✅ تم تحميل Session\n";

// تحميل Auth
echo "6. تحميل Auth...\n";
require_once 'core/Auth.php';
echo "   ✅ تم تحميل Auth\n";

// إنشاء مثيل Auth
echo "7. إنشاء مثيل Auth...\n";
try {
    $auth = new Auth();
    echo "   ✅ تم إنشاء مثيل Auth\n";
} catch (Exception $e) {
    echo "   ❌ خطأ في إنشاء Auth: " . $e->getMessage() . "\n";
    echo "   الملف: " . $e->getFile() . "\n";
    echo "   السطر: " . $e->getLine() . "\n";
    exit(1);
}

echo "\n✅ جميع الملفات تم تحميلها بنجاح!\n";
?>
