<?php
/**
 * UserController
 * تحكم في إدارة المستخدمين
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class UserController extends Controller
{
    private $auth;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // التحقق من تسجيل الدخول
        if (!$this->auth->check()) {
            $this->redirect('/login');
        }
    }

    /**
     * عرض قائمة المستخدمين
     */
    public function index()
    {
        // التحقق من الصلاحية
        if (!$this->hasPermission('users_view')) {
            $this->showError('ليس لديك صلاحية لعرض المستخدمين');
            return;
        }

        $users = $this->db->select("
            SELECT u.*, r.name as role_name 
            FROM users u 
            LEFT JOIN user_roles ur ON u.id = ur.user_id 
            LEFT JOIN roles r ON ur.role_id = r.id 
            ORDER BY u.created_at DESC
        ");

        $this->render('users/index', [
            'users' => $users,
            'title' => 'إدارة المستخدمين'
        ]);
    }

    /**
     * عرض نموذج إضافة مستخدم جديد
     */
    public function create()
    {
        if (!$this->hasPermission('users_create')) {
            $this->showError('ليس لديك صلاحية لإنشاء مستخدمين');
            return;
        }

        $roles = $this->db->select("SELECT * FROM roles WHERE is_active = 1 ORDER BY name");

        $this->render('users/create', [
            'roles' => $roles,
            'title' => 'إضافة مستخدم جديد'
        ]);
    }

    /**
     * حفظ مستخدم جديد
     */
    public function store()
    {
        if (!$this->hasPermission('users_create')) {
            $this->jsonResponse(['success' => false, 'message' => 'ليس لديك صلاحية لإنشاء مستخدمين']);
            return;
        }

        try {
            // التحقق من البيانات
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $firstName = trim($_POST['first_name'] ?? '');
            $lastName = trim($_POST['last_name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $roleId = intval($_POST['role_id'] ?? 0);

            if (empty($username) || empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
                throw new Exception('جميع الحقول المطلوبة يجب ملؤها');
            }

            if (strlen($password) < 6) {
                throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            // التحقق من عدم تكرار اسم المستخدم والبريد
            $existingUser = $this->db->selectOne("SELECT id FROM users WHERE username = ? OR email = ?", [$username, $email]);
            if ($existingUser) {
                throw new Exception('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً');
            }

            // إنشاء المستخدم
            $userId = $this->db->insert('users', [
                'username' => $username,
                'email' => $email,
                'password_hash' => password_hash($password, PASSWORD_DEFAULT),
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone' => $phone,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // ربط الدور
            if ($roleId > 0) {
                $this->db->insert('user_roles', [
                    'user_id' => $userId,
                    'role_id' => $roleId
                ]);
            }

            $this->jsonResponse(['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * عرض نموذج تعديل مستخدم
     */
    public function edit($id)
    {
        if (!$this->hasPermission('users_edit')) {
            $this->showError('ليس لديك صلاحية لتعديل المستخدمين');
            return;
        }

        $user = $this->db->selectOne("
            SELECT u.*, ur.role_id 
            FROM users u 
            LEFT JOIN user_roles ur ON u.id = ur.user_id 
            WHERE u.id = ?
        ", [$id]);

        if (!$user) {
            $this->showError('المستخدم غير موجود');
            return;
        }

        $roles = $this->db->select("SELECT * FROM roles WHERE is_active = 1 ORDER BY name");

        $this->render('users/edit', [
            'user' => $user,
            'roles' => $roles,
            'title' => 'تعديل المستخدم'
        ]);
    }

    /**
     * تحديث بيانات مستخدم
     */
    public function update($id)
    {
        if (!$this->hasPermission('users_edit')) {
            $this->jsonResponse(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل المستخدمين']);
            return;
        }

        try {
            $user = $this->db->selectOne("SELECT * FROM users WHERE id = ?", [$id]);
            if (!$user) {
                throw new Exception('المستخدم غير موجود');
            }

            // التحقق من البيانات
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $firstName = trim($_POST['first_name'] ?? '');
            $lastName = trim($_POST['last_name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $roleId = intval($_POST['role_id'] ?? 0);
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            if (empty($username) || empty($email) || empty($firstName) || empty($lastName)) {
                throw new Exception('جميع الحقول المطلوبة يجب ملؤها');
            }

            // التحقق من عدم تكرار اسم المستخدم والبريد
            $existingUser = $this->db->selectOne("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?", [$username, $email, $id]);
            if ($existingUser) {
                throw new Exception('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً');
            }

            // تحديث البيانات
            $updateData = [
                'username' => $username,
                'email' => $email,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone' => $phone,
                'is_active' => $isActive,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // تحديث كلمة المرور إذا تم إدخالها
            $password = $_POST['password'] ?? '';
            if (!empty($password)) {
                if (strlen($password) < 6) {
                    throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                }
                $updateData['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
            }

            $this->db->update('users', $updateData, ['id' => $id]);

            // تحديث الدور
            $this->db->delete('user_roles', ['user_id' => $id]);
            if ($roleId > 0) {
                $this->db->insert('user_roles', [
                    'user_id' => $id,
                    'role_id' => $roleId
                ]);
            }

            $this->jsonResponse(['success' => true, 'message' => 'تم تحديث المستخدم بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * حذف مستخدم
     */
    public function delete($id)
    {
        if (!$this->hasPermission('users_delete')) {
            $this->jsonResponse(['success' => false, 'message' => 'ليس لديك صلاحية لحذف المستخدمين']);
            return;
        }

        try {
            $user = $this->db->selectOne("SELECT * FROM users WHERE id = ?", [$id]);
            if (!$user) {
                throw new Exception('المستخدم غير موجود');
            }

            // منع حذف المستخدم الحالي
            if ($id == $this->auth->getUserId()) {
                throw new Exception('لا يمكنك حذف حسابك الخاص');
            }

            // حذف المستخدم
            $this->db->delete('user_roles', ['user_id' => $id]);
            $this->db->delete('users', ['id' => $id]);

            $this->jsonResponse(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * التحقق من الصلاحية
     */
    private function hasPermission($permission)
    {
        // يمكن تطوير هذه الدالة لاحقاً للتحقق من الصلاحيات
        return true; // مؤقتاً
    }
}
?>
