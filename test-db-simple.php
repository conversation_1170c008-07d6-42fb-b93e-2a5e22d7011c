<?php
/**
 * اختبار قاعدة البيانات البسيط
 */

echo "🗄️ اختبار قاعدة البيانات...\n";

try {
    // اتصال مباشر بـ PDO
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ الاتصال المباشر نجح\n";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->prepare("SELECT username, email FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ المستخدم موجود: " . $user['username'] . " (" . $user['email'] . ")\n";
    } else {
        echo "❌ المستخدم غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال المباشر: " . $e->getMessage() . "\n";
}

echo "\n";

// اختبار كلاس Database
try {
    require_once 'core/Environment.php';
    Environment::load();
    require_once 'core/Database.php';
    
    echo "🔧 اختبار كلاس Database...\n";
    
    $db = Database::getInstance();
    echo "✅ تم إنشاء مثيل Database\n";
    
    $user = $db->selectOne("SELECT username, email FROM users WHERE username = ?", ['admin']);
    
    if ($user) {
        echo "✅ المستخدم موجود عبر Database: " . $user['username'] . " (" . $user['email'] . ")\n";
    } else {
        echo "❌ المستخدم غير موجود عبر Database\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في كلاس Database: " . $e->getMessage() . "\n";
    echo "التفاصيل: " . $e->getFile() . " في السطر " . $e->getLine() . "\n";
}
?>
