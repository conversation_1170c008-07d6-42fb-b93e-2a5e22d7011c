<?php
/**
 * SeaSystem Logger
 * نظام التسجيل والمراقبة
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Database.php';
require_once dirname(__DIR__) . '/config/app.php';

class Logger
{
    const EMERGENCY = 'emergency';
    const ALERT = 'alert';
    const CRITICAL = 'critical';
    const ERROR = 'error';
    const WARNING = 'warning';
    const NOTICE = 'notice';
    const INFO = 'info';
    const DEBUG = 'debug';

    private static $instance = null;
    private $logPath;
    private $maxFiles;
    private $enableDatabase;
    private $db;

    private function __construct()
    {
        $this->logPath = config('logging.channels.file.path', dirname(__DIR__) . '/logs/app.log');
        $this->maxFiles = config('logging.channels.file.max_files', 30);
        $this->enableDatabase = config('logging.default') === 'database';
        
        if ($this->enableDatabase) {
            $this->db = Database::getInstance();
        }
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        $logDir = dirname($this->logPath);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * الحصول على مثيل وحيد من الكلاس
     * 
     * @return Logger
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * تسجيل رسالة طوارئ
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function emergency($message, array $context = [])
    {
        self::getInstance()->log(self::EMERGENCY, $message, $context);
    }

    /**
     * تسجيل رسالة تنبيه
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function alert($message, array $context = [])
    {
        self::getInstance()->log(self::ALERT, $message, $context);
    }

    /**
     * تسجيل رسالة حرجة
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function critical($message, array $context = [])
    {
        self::getInstance()->log(self::CRITICAL, $message, $context);
    }

    /**
     * تسجيل رسالة خطأ
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function error($message, array $context = [])
    {
        self::getInstance()->log(self::ERROR, $message, $context);
    }

    /**
     * تسجيل رسالة تحذير
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function warning($message, array $context = [])
    {
        self::getInstance()->log(self::WARNING, $message, $context);
    }

    /**
     * تسجيل رسالة ملاحظة
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function notice($message, array $context = [])
    {
        self::getInstance()->log(self::NOTICE, $message, $context);
    }

    /**
     * تسجيل رسالة معلومات
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function info($message, array $context = [])
    {
        self::getInstance()->log(self::INFO, $message, $context);
    }

    /**
     * تسجيل رسالة تطوير
     * 
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public static function debug($message, array $context = [])
    {
        self::getInstance()->log(self::DEBUG, $message, $context);
    }

    /**
     * تسجيل رسالة
     * 
     * @param string $level المستوى
     * @param string $message الرسالة
     * @param array $context السياق
     */
    public function log($level, $message, array $context = [])
    {
        // التحقق من مستوى التسجيل
        if (!$this->shouldLog($level)) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $formattedMessage = $this->formatMessage($level, $message, $context, $timestamp);

        // التسجيل في الملف
        $this->logToFile($formattedMessage);

        // التسجيل في قاعدة البيانات
        if ($this->enableDatabase) {
            $this->logToDatabase($level, $message, $context, $timestamp);
        }

        // إدارة الملفات القديمة
        $this->rotateLogFiles();
    }

    /**
     * التحقق من ضرورة التسجيل
     * 
     * @param string $level المستوى
     * @return bool
     */
    private function shouldLog($level)
    {
        $configLevel = config('logging.channels.file.level', 'info');
        $levels = [
            self::DEBUG => 0,
            self::INFO => 1,
            self::NOTICE => 2,
            self::WARNING => 3,
            self::ERROR => 4,
            self::CRITICAL => 5,
            self::ALERT => 6,
            self::EMERGENCY => 7
        ];

        return $levels[$level] >= $levels[$configLevel];
    }

    /**
     * تنسيق الرسالة
     * 
     * @param string $level المستوى
     * @param string $message الرسالة
     * @param array $context السياق
     * @param string $timestamp الوقت
     * @return string
     */
    private function formatMessage($level, $message, array $context, $timestamp)
    {
        $contextStr = !empty($context) ? ' ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        return "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
    }

    /**
     * التسجيل في الملف
     * 
     * @param string $message الرسالة المنسقة
     */
    private function logToFile($message)
    {
        try {
            file_put_contents($this->logPath, $message, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // في حالة فشل الكتابة، استخدم error_log
            error_log("Logger: فشل في كتابة السجل - " . $e->getMessage());
        }
    }

    /**
     * التسجيل في قاعدة البيانات
     * 
     * @param string $level المستوى
     * @param string $message الرسالة
     * @param array $context السياق
     * @param string $timestamp الوقت
     */
    private function logToDatabase($level, $message, array $context, $timestamp)
    {
        try {
            $this->db->insert('activity_logs', [
                'user_id' => $context['user_id'] ?? null,
                'action' => $level,
                'table_name' => $context['table'] ?? null,
                'record_id' => $context['record_id'] ?? null,
                'old_values' => isset($context['old_values']) ? json_encode($context['old_values']) : null,
                'new_values' => isset($context['new_values']) ? json_encode($context['new_values']) : null,
                'ip_address' => $this->getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => $timestamp
            ]);
        } catch (Exception $e) {
            // في حالة فشل قاعدة البيانات، سجل في الملف
            $this->logToFile("[{$timestamp}] database_error: فشل في تسجيل السجل في قاعدة البيانات - " . $e->getMessage() . PHP_EOL);
        }
    }

    /**
     * إدارة الملفات القديمة
     */
    private function rotateLogFiles()
    {
        $logDir = dirname($this->logPath);
        $logBasename = basename($this->logPath, '.log');
        
        $files = glob($logDir . '/' . $logBasename . '*.log');
        
        if (count($files) > $this->maxFiles) {
            // ترتيب الملفات حسب تاريخ التعديل
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // حذف الملفات الأقدم
            $filesToDelete = array_slice($files, 0, count($files) - $this->maxFiles);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }

    /**
     * الحصول على عنوان IP الخاص بالعميل
     * 
     * @return string
     */
    private function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * تسجيل نشاط المستخدم
     * 
     * @param string $action العملية
     * @param int $userId معرف المستخدم
     * @param array $data البيانات الإضافية
     */
    public static function logUserActivity($action, $userId = null, array $data = [])
    {
        $context = array_merge($data, ['user_id' => $userId]);
        self::info("User Activity: {$action}", $context);
    }

    /**
     * تسجيل عملية قاعدة البيانات
     * 
     * @param string $operation العملية
     * @param string $table الجدول
     * @param int $recordId معرف السجل
     * @param array $oldValues القيم القديمة
     * @param array $newValues القيم الجديدة
     */
    public static function logDatabaseOperation($operation, $table, $recordId = null, array $oldValues = [], array $newValues = [])
    {
        $context = [
            'table' => $table,
            'record_id' => $recordId,
            'old_values' => $oldValues,
            'new_values' => $newValues
        ];
        
        self::info("Database {$operation}: {$table}", $context);
    }

    /**
     * تسجيل خطأ في النظام
     * 
     * @param Exception $exception الاستثناء
     * @param array $context السياق الإضافي
     */
    public static function logException(Exception $exception, array $context = [])
    {
        $context = array_merge($context, [
            'exception' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        self::error($exception->getMessage(), $context);
    }

    /**
     * تسجيل محاولة أمنية مشبوهة
     * 
     * @param string $type نوع المحاولة
     * @param array $details التفاصيل
     */
    public static function logSecurityEvent($type, array $details = [])
    {
        $context = array_merge($details, [
            'security_event' => $type,
            'ip_address' => self::getInstance()->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        self::warning("Security Event: {$type}", $context);
    }

    /**
     * تسجيل أداء النظام
     * 
     * @param string $operation العملية
     * @param float $executionTime وقت التنفيذ
     * @param array $details التفاصيل
     */
    public static function logPerformance($operation, $executionTime, array $details = [])
    {
        $context = array_merge($details, [
            'execution_time' => $executionTime,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]);
        
        $level = $executionTime > 5 ? self::WARNING : self::INFO;
        self::getInstance()->log($level, "Performance: {$operation}", $context);
    }

    /**
     * الحصول على السجلات الأخيرة
     * 
     * @param int $limit عدد السجلات
     * @param string $level المستوى
     * @return array
     */
    public static function getRecentLogs($limit = 100, $level = null)
    {
        $instance = self::getInstance();
        
        if ($instance->enableDatabase) {
            return $instance->getLogsFromDatabase($limit, $level);
        } else {
            return $instance->getLogsFromFile($limit, $level);
        }
    }

    /**
     * الحصول على السجلات من قاعدة البيانات
     * 
     * @param int $limit عدد السجلات
     * @param string $level المستوى
     * @return array
     */
    private function getLogsFromDatabase($limit, $level = null)
    {
        try {
            $query = "SELECT * FROM activity_logs";
            $params = [];
            
            if ($level) {
                $query .= " WHERE action = ?";
                $params[] = $level;
            }
            
            $query .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            return $this->db->select($query, $params);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على السجلات من الملف
     * 
     * @param int $limit عدد السجلات
     * @param string $level المستوى
     * @return array
     */
    private function getLogsFromFile($limit, $level = null)
    {
        try {
            if (!file_exists($this->logPath)) {
                return [];
            }
            
            $lines = file($this->logPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $lines = array_reverse($lines);
            
            $logs = [];
            $count = 0;
            
            foreach ($lines as $line) {
                if ($count >= $limit) {
                    break;
                }
                
                if ($level && strpos($line, $level . ':') === false) {
                    continue;
                }
                
                $logs[] = $line;
                $count++;
            }
            
            return $logs;
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * مسح السجلات القديمة
     * 
     * @param int $days عدد الأيام
     */
    public static function clearOldLogs($days = 30)
    {
        $instance = self::getInstance();
        
        if ($instance->enableDatabase) {
            $instance->clearOldDatabaseLogs($days);
        }
        
        $instance->clearOldFileLog($days);
    }

    /**
     * مسح السجلات القديمة من قاعدة البيانات
     * 
     * @param int $days عدد الأيام
     */
    private function clearOldDatabaseLogs($days)
    {
        try {
            $this->db->execute(
                "DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$days]
            );
        } catch (Exception $e) {
            self::error("فشل في مسح السجلات القديمة من قاعدة البيانات: " . $e->getMessage());
        }
    }

    /**
     * مسح ملف السجل القديم
     * 
     * @param int $days عدد الأيام
     */
    private function clearOldFileLog($days)
    {
        try {
            $logDir = dirname($this->logPath);
            $files = glob($logDir . '/*.log');
            
            foreach ($files as $file) {
                if (filemtime($file) < time() - ($days * 24 * 60 * 60)) {
                    unlink($file);
                }
            }
        } catch (Exception $e) {
            self::error("فشل في مسح ملفات السجل القديمة: " . $e->getMessage());
        }
    }
}
