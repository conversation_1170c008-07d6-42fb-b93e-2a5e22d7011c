<?php
/**
 * SeaSystem Role-Based Access Control (RBAC)
 * نظام التحكم في الوصول القائم على الأدوار
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Database.php';
require_once 'Auth.php';

class RBAC
{
    private $db;
    private $auth;
    private $cache = [];
    private $cacheEnabled = true;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
    }

    /**
     * التحقق من صلاحية المستخدم
     * 
     * @param int|null $userId معرف المستخدم (null للمستخدم الحالي)
     * @param string $permission الصلاحية المطلوبة
     * @return bool
     */
    public function hasPermission($userId = null, $permission = null)
    {
        // إذا لم يتم تمرير معرف المستخدم، استخدم المستخدم الحالي
        if ($userId === null) {
            if (!$this->auth->check()) {
                return false;
            }
            $userId = $this->auth->getUserId();
        }

        // إذا لم يتم تمرير الصلاحية، استخدم المعامل الأول كصلاحية
        if ($permission === null && is_string($userId)) {
            $permission = $userId;
            $userId = $this->auth->getUserId();
            if (!$userId) {
                return false;
            }
        }

        // التحقق من الكاش
        $cacheKey = "user_{$userId}_permission_{$permission}";
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        // استعلام قاعدة البيانات
        $query = "
            SELECT COUNT(*) as count
            FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND p.name = ? AND r.is_active = 1
        ";

        $result = $this->db->selectOne($query, [$userId, $permission]);
        $hasPermission = $result['count'] > 0;

        // حفظ في الكاش
        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $hasPermission;
        }

        return $hasPermission;
    }

    /**
     * التحقق من دور المستخدم
     * 
     * @param int|null $userId معرف المستخدم
     * @param string $role الدور المطلوب
     * @return bool
     */
    public function hasRole($userId = null, $role = null)
    {
        if ($userId === null) {
            if (!$this->auth->check()) {
                return false;
            }
            $userId = $this->auth->getUserId();
        }

        if ($role === null && is_string($userId)) {
            $role = $userId;
            $userId = $this->auth->getUserId();
            if (!$userId) {
                return false;
            }
        }

        $cacheKey = "user_{$userId}_role_{$role}";
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "
            SELECT COUNT(*) as count
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND r.name = ? AND r.is_active = 1
        ";

        $result = $this->db->selectOne($query, [$userId, $role]);
        $hasRole = $result['count'] > 0;

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $hasRole;
        }

        return $hasRole;
    }

    /**
     * الحصول على أدوار المستخدم
     * 
     * @param int $userId معرف المستخدم
     * @return array
     */
    public function getUserRoles($userId)
    {
        $cacheKey = "user_{$userId}_roles";
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "
            SELECT r.id, r.name, r.display_name, r.description
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND r.is_active = 1
            ORDER BY r.display_name
        ";

        $roles = $this->db->select($query, [$userId]);

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $roles;
        }

        return $roles;
    }

    /**
     * الحصول على صلاحيات المستخدم
     * 
     * @param int $userId معرف المستخدم
     * @return array
     */
    public function getUserPermissions($userId)
    {
        $cacheKey = "user_{$userId}_permissions";
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "
            SELECT DISTINCT p.id, p.name, p.display_name, p.module, p.description
            FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND r.is_active = 1
            ORDER BY p.module, p.display_name
        ";

        $permissions = $this->db->select($query, [$userId]);

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $permissions;
        }

        return $permissions;
    }

    /**
     * تعيين دور للمستخدم
     * 
     * @param int $userId معرف المستخدم
     * @param int $roleId معرف الدور
     * @param int|null $assignedBy معرف من قام بالتعيين
     * @return bool
     */
    public function assignRole($userId, $roleId, $assignedBy = null)
    {
        try {
            // التحقق من وجود المستخدم والدور
            if (!$this->userExists($userId) || !$this->roleExists($roleId)) {
                return false;
            }

            // التحقق من عدم وجود التعيين مسبقاً
            if ($this->userHasRoleId($userId, $roleId)) {
                return true; // موجود مسبقاً
            }

            $assignedBy = $assignedBy ?: $this->auth->getUserId();

            $this->db->insert('user_roles', [
                'user_id' => $userId,
                'role_id' => $roleId,
                'assigned_by' => $assignedBy,
                'assigned_at' => date('Y-m-d H:i:s')
            ]);

            // مسح الكاش
            $this->clearUserCache($userId);

            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * إزالة دور من المستخدم
     * 
     * @param int $userId معرف المستخدم
     * @param int $roleId معرف الدور
     * @return bool
     */
    public function removeRole($userId, $roleId)
    {
        try {
            $result = $this->db->delete('user_roles', [
                'user_id' => $userId,
                'role_id' => $roleId
            ]);

            // مسح الكاش
            $this->clearUserCache($userId);

            return $result > 0;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * الحصول على جميع الأدوار
     * 
     * @param bool $activeOnly الأدوار النشطة فقط
     * @return array
     */
    public function getAllRoles($activeOnly = true)
    {
        $cacheKey = "all_roles_" . ($activeOnly ? 'active' : 'all');
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "SELECT * FROM roles";
        $params = [];

        if ($activeOnly) {
            $query .= " WHERE is_active = 1";
        }

        $query .= " ORDER BY display_name";

        $roles = $this->db->select($query, $params);

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $roles;
        }

        return $roles;
    }

    /**
     * الحصول على جميع الصلاحيات
     * 
     * @param string|null $module الوحدة المحددة
     * @return array
     */
    public function getAllPermissions($module = null)
    {
        $cacheKey = "all_permissions_" . ($module ?: 'all');
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "SELECT * FROM permissions";
        $params = [];

        if ($module) {
            $query .= " WHERE module = ?";
            $params[] = $module;
        }

        $query .= " ORDER BY module, display_name";

        $permissions = $this->db->select($query, $params);

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $permissions;
        }

        return $permissions;
    }

    /**
     * الحصول على صلاحيات الدور
     * 
     * @param int $roleId معرف الدور
     * @return array
     */
    public function getRolePermissions($roleId)
    {
        $cacheKey = "role_{$roleId}_permissions";
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        $query = "
            SELECT p.id, p.name, p.display_name, p.module, p.description
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            WHERE rp.role_id = ?
            ORDER BY p.module, p.display_name
        ";

        $permissions = $this->db->select($query, [$roleId]);

        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = $permissions;
        }

        return $permissions;
    }

    /**
     * تعيين صلاحية لدور
     * 
     * @param int $roleId معرف الدور
     * @param int $permissionId معرف الصلاحية
     * @return bool
     */
    public function assignPermissionToRole($roleId, $permissionId)
    {
        try {
            // التحقق من عدم وجود التعيين مسبقاً
            $existing = $this->db->selectOne(
                "SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?",
                [$roleId, $permissionId]
            );

            if ($existing) {
                return true; // موجود مسبقاً
            }

            $this->db->insert('role_permissions', [
                'role_id' => $roleId,
                'permission_id' => $permissionId
            ]);

            // مسح الكاش
            $this->clearRoleCache($roleId);

            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * إزالة صلاحية من دور
     * 
     * @param int $roleId معرف الدور
     * @param int $permissionId معرف الصلاحية
     * @return bool
     */
    public function removePermissionFromRole($roleId, $permissionId)
    {
        try {
            $result = $this->db->delete('role_permissions', [
                'role_id' => $roleId,
                'permission_id' => $permissionId
            ]);

            // مسح الكاش
            $this->clearRoleCache($roleId);

            return $result > 0;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * إنشاء دور جديد
     * 
     * @param array $data بيانات الدور
     * @return int|false معرف الدور الجديد أو false
     */
    public function createRole($data)
    {
        try {
            $roleId = $this->db->insert('roles', [
                'name' => $data['name'],
                'display_name' => $data['display_name'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true
            ]);

            // مسح كاش الأدوار
            $this->clearCache('all_roles');

            return $roleId;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * إنشاء صلاحية جديدة
     * 
     * @param array $data بيانات الصلاحية
     * @return int|false معرف الصلاحية الجديدة أو false
     */
    public function createPermission($data)
    {
        try {
            $permissionId = $this->db->insert('permissions', [
                'name' => $data['name'],
                'display_name' => $data['display_name'],
                'module' => $data['module'],
                'description' => $data['description'] ?? null
            ]);

            // مسح كاش الصلاحيات
            $this->clearCache('all_permissions');

            return $permissionId;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود المستخدم
     * 
     * @param int $userId معرف المستخدم
     * @return bool
     */
    private function userExists($userId)
    {
        $result = $this->db->selectOne("SELECT id FROM users WHERE id = ?", [$userId]);
        return !empty($result);
    }

    /**
     * التحقق من وجود الدور
     * 
     * @param int $roleId معرف الدور
     * @return bool
     */
    private function roleExists($roleId)
    {
        $result = $this->db->selectOne("SELECT id FROM roles WHERE id = ?", [$roleId]);
        return !empty($result);
    }

    /**
     * التحقق من وجود دور للمستخدم
     * 
     * @param int $userId معرف المستخدم
     * @param int $roleId معرف الدور
     * @return bool
     */
    private function userHasRoleId($userId, $roleId)
    {
        $result = $this->db->selectOne(
            "SELECT id FROM user_roles WHERE user_id = ? AND role_id = ?",
            [$userId, $roleId]
        );
        return !empty($result);
    }

    /**
     * مسح كاش المستخدم
     * 
     * @param int $userId معرف المستخدم
     */
    private function clearUserCache($userId)
    {
        $patterns = [
            "user_{$userId}_permission_",
            "user_{$userId}_role_",
            "user_{$userId}_roles",
            "user_{$userId}_permissions"
        ];

        foreach ($this->cache as $key => $value) {
            foreach ($patterns as $pattern) {
                if (strpos($key, $pattern) === 0) {
                    unset($this->cache[$key]);
                }
            }
        }
    }

    /**
     * مسح كاش الدور
     * 
     * @param int $roleId معرف الدور
     */
    private function clearRoleCache($roleId)
    {
        $patterns = [
            "role_{$roleId}_permissions"
        ];

        foreach ($this->cache as $key => $value) {
            foreach ($patterns as $pattern) {
                if (strpos($key, $pattern) === 0) {
                    unset($this->cache[$key]);
                }
            }
        }
    }

    /**
     * مسح الكاش
     * 
     * @param string|null $pattern نمط المفاتيح
     */
    public function clearCache($pattern = null)
    {
        if ($pattern === null) {
            $this->cache = [];
        } else {
            foreach ($this->cache as $key => $value) {
                if (strpos($key, $pattern) !== false) {
                    unset($this->cache[$key]);
                }
            }
        }
    }

    /**
     * تفعيل/إلغاء تفعيل الكاش
     * 
     * @param bool $enabled
     */
    public function setCacheEnabled($enabled)
    {
        $this->cacheEnabled = $enabled;
        if (!$enabled) {
            $this->cache = [];
        }
    }
}
