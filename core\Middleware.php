<?php
/**
 * SeaSystem Middleware System
 * نظام الوسطاء للتحقق من الصلاحيات والأمان
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Auth.php';
require_once 'RBAC.php';
require_once 'Session.php';

class Middleware
{
    private $auth;
    private $rbac;

    public function __construct()
    {
        $this->auth = new Auth();
        $this->rbac = new RBAC();
    }

    /**
     * التحقق من تسجيل الدخول
     * 
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function requireAuth($next = null)
    {
        if (!$this->auth->check()) {
            // حفظ الرابط المطلوب للعودة إليه بعد تسجيل الدخول
            Session::set('intended_url', $_SERVER['REQUEST_URI']);
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'غير مصرح', 'redirect' => '/login'], 401);
            } else {
                $this->redirect('/login');
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من الصلاحية
     * 
     * @param string $permission الصلاحية المطلوبة
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function requirePermission($permission, $next = null)
    {
        // التحقق من تسجيل الدخول أولاً
        if (!$this->requireAuth()) {
            return false;
        }

        if (!$this->rbac->hasPermission($permission)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'ليس لديك صلاحية للوصول'], 403);
            } else {
                $this->accessDenied();
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من الدور
     * 
     * @param string $role الدور المطلوب
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function requireRole($role, $next = null)
    {
        if (!$this->requireAuth()) {
            return false;
        }

        if (!$this->rbac->hasRole($role)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'ليس لديك الدور المطلوب'], 403);
            } else {
                $this->accessDenied();
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من عدة صلاحيات (يجب توفر جميعها)
     * 
     * @param array $permissions الصلاحيات المطلوبة
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function requireAllPermissions($permissions, $next = null)
    {
        if (!$this->requireAuth()) {
            return false;
        }

        foreach ($permissions as $permission) {
            if (!$this->rbac->hasPermission($permission)) {
                if ($this->isAjaxRequest()) {
                    $this->jsonResponse(['error' => 'ليس لديك جميع الصلاحيات المطلوبة'], 403);
                } else {
                    $this->accessDenied();
                }
                return false;
            }
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من إحدى الصلاحيات (يكفي توفر واحدة)
     * 
     * @param array $permissions الصلاحيات المطلوبة
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function requireAnyPermission($permissions, $next = null)
    {
        if (!$this->requireAuth()) {
            return false;
        }

        $hasAny = false;
        foreach ($permissions as $permission) {
            if ($this->rbac->hasPermission($permission)) {
                $hasAny = true;
                break;
            }
        }

        if (!$hasAny) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'ليس لديك أي من الصلاحيات المطلوبة'], 403);
            } else {
                $this->accessDenied();
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من CSRF Token
     * 
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function verifyCsrf($next = null)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
            $sessionToken = Session::get('csrf_token');

            if (empty($token) || empty($sessionToken) || !hash_equals($sessionToken, $token)) {
                if ($this->isAjaxRequest()) {
                    $this->jsonResponse(['error' => 'رمز الأمان غير صحيح'], 419);
                } else {
                    Session::flash('error', 'رمز الأمان غير صحيح');
                    $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
                }
                return false;
            }
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من معدل الطلبات (Rate Limiting)
     * 
     * @param int $maxRequests الحد الأقصى للطلبات
     * @param int $timeWindow النافذة الزمنية بالثواني
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function rateLimit($maxRequests = 60, $timeWindow = 60, $next = null)
    {
        $ip = $this->getClientIp();
        $key = "rate_limit_{$ip}";
        
        $requests = Session::get($key, []);
        $now = time();
        
        // إزالة الطلبات القديمة
        $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        if (count($requests) >= $maxRequests) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'تم تجاوز الحد المسموح من الطلبات'], 429);
            } else {
                http_response_code(429);
                echo "تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.";
                exit;
            }
            return false;
        }
        
        // إضافة الطلب الحالي
        $requests[] = $now;
        Session::set($key, $requests);

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من صحة البيانات المدخلة
     * 
     * @param array $rules قواعد التحقق
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function validateInput($rules, $next = null)
    {
        $errors = [];
        $data = $_POST;

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $fieldRules = explode('|', $rule);

            foreach ($fieldRules as $fieldRule) {
                $ruleParts = explode(':', $fieldRule);
                $ruleName = $ruleParts[0];
                $ruleValue = $ruleParts[1] ?? null;

                switch ($ruleName) {
                    case 'required':
                        if (empty($value)) {
                            $errors[$field][] = "الحقل {$field} مطلوب";
                        }
                        break;

                    case 'email':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field][] = "الحقل {$field} يجب أن يكون بريد إلكتروني صحيح";
                        }
                        break;

                    case 'min':
                        if (!empty($value) && strlen($value) < $ruleValue) {
                            $errors[$field][] = "الحقل {$field} يجب أن يكون على الأقل {$ruleValue} أحرف";
                        }
                        break;

                    case 'max':
                        if (!empty($value) && strlen($value) > $ruleValue) {
                            $errors[$field][] = "الحقل {$field} يجب ألا يزيد عن {$ruleValue} حرف";
                        }
                        break;

                    case 'numeric':
                        if (!empty($value) && !is_numeric($value)) {
                            $errors[$field][] = "الحقل {$field} يجب أن يكون رقم";
                        }
                        break;
                }
            }
        }

        if (!empty($errors)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['errors' => $errors], 422);
            } else {
                Session::flash('validation_errors', $errors);
                $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * تسجيل النشاط
     * 
     * @param string $action العملية
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function logActivity($action, $next = null)
    {
        $userId = $this->auth->getUserId();
        $ip = $this->getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        try {
            $db = Database::getInstance();
            $db->insert('activity_logs', [
                'user_id' => $userId,
                'action' => $action,
                'ip_address' => $ip,
                'user_agent' => $userAgent,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // تسجيل الخطأ دون إيقاف التنفيذ
            error_log("فشل في تسجيل النشاط: " . $e->getMessage());
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من انتهاء صلاحية الجلسة
     * 
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public function checkSessionExpiry($next = null)
    {
        if (Session::isExpired()) {
            Session::destroy();
            
            if ($this->isAjaxRequest()) {
                $this->jsonResponse(['error' => 'انتهت صلاحية الجلسة', 'redirect' => '/login'], 401);
            } else {
                Session::flash('error', 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
                $this->redirect('/login');
            }
            return false;
        }

        if ($next && is_callable($next)) {
            return $next();
        }

        return true;
    }

    /**
     * التحقق من كون الطلب AJAX
     * 
     * @return bool
     */
    private function isAjaxRequest()
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * الحصول على عنوان IP الخاص بالعميل
     * 
     * @return string
     */
    private function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * إعادة التوجيه
     * 
     * @param string $url الرابط
     */
    private function redirect($url)
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * رفض الوصول
     */
    private function accessDenied()
    {
        http_response_code(403);
        echo "ليس لديك صلاحية للوصول إلى هذه الصفحة";
        exit;
    }

    /**
     * إرجاع استجابة JSON
     * 
     * @param array $data البيانات
     * @param int $statusCode رمز الحالة
     */
    private function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * تطبيق عدة وسطاء
     * 
     * @param array $middlewares قائمة الوسطاء
     * @param callable $next الدالة التالية
     * @return mixed
     */
    public static function apply($middlewares, $next = null)
    {
        $middleware = new self();
        
        foreach ($middlewares as $middlewareConfig) {
            if (is_string($middlewareConfig)) {
                $method = $middlewareConfig;
                $params = [];
            } else {
                $method = $middlewareConfig[0];
                $params = array_slice($middlewareConfig, 1);
            }
            
            if (method_exists($middleware, $method)) {
                $result = call_user_func_array([$middleware, $method], $params);
                if ($result === false) {
                    return false;
                }
            }
        }
        
        if ($next && is_callable($next)) {
            return $next();
        }
        
        return true;
    }
}
