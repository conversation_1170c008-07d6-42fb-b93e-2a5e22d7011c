<?php
/**
 * إنشاء جداول المالية
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "💰 إنشاء جداول المالية...\n";
    echo "========================\n\n";

    // جدول دليل الحسابات
    $sql = "
    CREATE TABLE IF NOT EXISTS accounts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
        parent_id INT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES accounts(id) ON DELETE SET NULL,
        INDEX idx_account_code (account_code),
        INDEX idx_account_type (account_type)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول accounts\n";

    // جدول القيود اليومية
    $sql = "
    CREATE TABLE IF NOT EXISTS journal_entries (
        id INT PRIMARY KEY AUTO_INCREMENT,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        entry_date DATE NOT NULL,
        description TEXT,
        reference VARCHAR(100),
        total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_entry_date (entry_date),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول journal_entries\n";

    // جدول تفاصيل القيود
    $sql = "
    CREATE TABLE IF NOT EXISTS journal_entry_details (
        id INT PRIMARY KEY AUTO_INCREMENT,
        journal_entry_id INT NOT NULL,
        account_id INT NOT NULL,
        debit DECIMAL(15,2) DEFAULT 0,
        credit DECIMAL(15,2) DEFAULT 0,
        description TEXT,
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE RESTRICT,
        INDEX idx_journal_entry (journal_entry_id),
        INDEX idx_account (account_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول journal_entry_details\n";

    // جدول الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT PRIMARY KEY AUTO_INCREMENT,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        invoice_type ENUM('sales', 'purchase') NOT NULL,
        customer_id INT,
        supplier_id INT,
        invoice_date DATE NOT NULL,
        due_date DATE,
        subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
        tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_invoice_number (invoice_number),
        INDEX idx_invoice_date (invoice_date),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول invoices\n";

    // جدول تفاصيل الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        invoice_id INT NOT NULL,
        product_id INT,
        description VARCHAR(255) NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        INDEX idx_invoice (invoice_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول invoice_items\n";

    // جدول المدفوعات
    $sql = "
    CREATE TABLE IF NOT EXISTS payments (
        id INT PRIMARY KEY AUTO_INCREMENT,
        payment_number VARCHAR(50) UNIQUE NOT NULL,
        payment_type ENUM('received', 'paid') NOT NULL,
        payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_date DATE NOT NULL,
        reference VARCHAR(100),
        description TEXT,
        invoice_id INT,
        account_id INT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE SET NULL,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_payment_date (payment_date),
        INDEX idx_payment_type (payment_type)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول payments\n";

    echo "\n🎉 تم إنشاء جميع جداول المالية بنجاح!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
