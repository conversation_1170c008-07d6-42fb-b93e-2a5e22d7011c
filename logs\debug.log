handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: login
REQUEST_METHOD: POST
محاولة تسجيل الدخول للمستخدم: admin
نتيجة تسجيل الدخول: Array
(
    [success] => 
    [message] => حدث خطأ أثناء تسجيل الدخول: خطأ في تنفيذ الاستعلام
)

handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
handleAuth called with action: showLoginForm
REQUEST_METHOD: GET
