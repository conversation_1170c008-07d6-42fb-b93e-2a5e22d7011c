<?php
/**
 * SeaSystem Environment Manager
 * إدارة متغيرات البيئة والتكوين
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

class Environment
{
    private static $loaded = false;
    private static $variables = [];

    /**
     * تحميل متغيرات البيئة من ملف .env
     * 
     * @param string $path مسار ملف .env
     * @return bool
     */
    public static function load($path = null)
    {
        if (self::$loaded) {
            return true;
        }

        if ($path === null) {
            $path = dirname(__DIR__) . '/config/.env';
        }

        if (!file_exists($path)) {
            // إذا لم يوجد ملف .env، استخدم القيم الافتراضية
            self::setDefaults();
            self::$loaded = true;
            return false;
        }

        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // تجاهل التعليقات
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // تحليل السطر
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // إزالة علامات الاقتباس إذا وجدت
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }

                // تحويل القيم المنطقية
                if (strtolower($value) === 'true') {
                    $value = true;
                } elseif (strtolower($value) === 'false') {
                    $value = false;
                } elseif (strtolower($value) === 'null') {
                    $value = null;
                }

                self::$variables[$key] = $value;
                
                // تعيين متغير البيئة
                if (!array_key_exists($key, $_ENV)) {
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
            }
        }

        self::$loaded = true;
        return true;
    }

    /**
     * الحصول على قيمة متغير البيئة
     * 
     * @param string $key اسم المتغير
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // التحقق من متغيرات البيئة المحملة
        if (isset(self::$variables[$key])) {
            return self::$variables[$key];
        }

        // التحقق من متغيرات البيئة العامة
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }

        // التحقق من $_ENV
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }

        return $default;
    }

    /**
     * تعيين قيمة متغير البيئة
     * 
     * @param string $key اسم المتغير
     * @param mixed $value القيمة
     */
    public static function set($key, $value)
    {
        self::$variables[$key] = $value;
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }

    /**
     * التحقق من وجود متغير البيئة
     * 
     * @param string $key اسم المتغير
     * @return bool
     */
    public static function has($key)
    {
        return isset(self::$variables[$key]) || 
               getenv($key) !== false || 
               isset($_ENV[$key]);
    }

    /**
     * الحصول على جميع متغيرات البيئة
     * 
     * @return array
     */
    public static function all()
    {
        return array_merge($_ENV, self::$variables);
    }

    /**
     * تعيين القيم الافتراضية
     */
    private static function setDefaults()
    {
        $defaults = [
            'DB_HOST' => 'localhost',
            'DB_PORT' => '3306',
            'DB_NAME' => 'R1',
            'DB_USERNAME' => 'root',
            'DB_PASSWORD' => '',
            'DB_CHARSET' => 'utf8mb4',
            'APP_NAME' => 'SeaSystem ERP',
            'APP_ENV' => 'development',
            'APP_DEBUG' => true,
            'APP_TIMEZONE' => 'Asia/Riyadh',
            'SESSION_LIFETIME' => 3600,
            'PASSWORD_MIN_LENGTH' => 8,
            'MAX_LOGIN_ATTEMPTS' => 5,
            'LOCKOUT_DURATION' => 900,
            'UPLOAD_MAX_SIZE' => 10485760,
            'LOG_LEVEL' => 'info',
            'DEFAULT_CURRENCY' => 'SAR',
            'TAX_RATE' => 15,
            'SHOW_ERRORS' => true
        ];

        foreach ($defaults as $key => $value) {
            self::$variables[$key] = $value;
            if (!array_key_exists($key, $_ENV)) {
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }

    /**
     * التحقق من صحة التكوين المطلوب
     * 
     * @return array مصفوفة بالأخطاء إن وجدت
     */
    public static function validate()
    {
        $errors = [];
        $required = ['DB_HOST', 'DB_NAME', 'DB_USERNAME', 'APP_NAME'];

        foreach ($required as $key) {
            if (!self::has($key) || empty(self::get($key))) {
                $errors[] = "متغير البيئة المطلوب '$key' غير محدد أو فارغ";
            }
        }

        // التحقق من صحة قاعدة البيانات
        $dbPort = self::get('DB_PORT', 3306);
        if (!is_numeric($dbPort) || $dbPort < 1 || $dbPort > 65535) {
            $errors[] = "رقم منفذ قاعدة البيانات غير صحيح";
        }

        return $errors;
    }

    /**
     * إعادة تحميل متغيرات البيئة
     */
    public static function reload()
    {
        self::$loaded = false;
        self::$variables = [];
        self::load();
    }
}
