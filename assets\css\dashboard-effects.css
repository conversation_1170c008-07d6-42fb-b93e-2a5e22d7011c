/**
 * تأثيرات لوحة التحكم المتقدمة
 * Advanced Dashboard Effects
 */

/* تأثيرات الجسيمات المتحركة */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    width: 4px;
    height: 4px;
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    width: 6px;
    height: 6px;
    left: 20%;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    width: 3px;
    height: 3px;
    left: 30%;
    animation-delay: 2s;
}

.particle:nth-child(4) {
    width: 5px;
    height: 5px;
    left: 40%;
    animation-delay: 3s;
}

.particle:nth-child(5) {
    width: 4px;
    height: 4px;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* تأثيرات الهوفر المتقدمة */
.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.stats-card:hover::after {
    left: 100%;
}

/* تأثيرات النبض للإشعارات */
.notification-pulse {
    position: relative;
}

.notification-pulse::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 2px solid #ff6b6b;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* تأثيرات الكتابة */
.typing-effect {
    overflow: hidden;
    border-right: 2px solid #667eea;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #667eea; }
}

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* تأثيرات الموجات */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(102, 126, 234, 0.1),
        transparent
    );
    animation: wave 2s infinite;
}

@keyframes wave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تأثيرات الانزلاق */
.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تأثيرات التكبير */
.zoom-in {
    animation: zoomIn 0.5s ease-out;
}

.zoom-out {
    animation: zoomOut 0.5s ease-out;
}

@keyframes zoomIn {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes zoomOut {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0);
        opacity: 0;
    }
}

/* تأثيرات الدوران */
.rotate-in {
    animation: rotateIn 0.5s ease-out;
}

@keyframes rotateIn {
    from {
        transform: rotate(-360deg) scale(0);
        opacity: 0;
    }
    to {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
}

/* تأثيرات الاهتزاز */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* تأثيرات الوميض */
.flash {
    animation: flash 1s ease-in-out;
}

@keyframes flash {
    0%, 50%, 100% { opacity: 1; }
    25%, 75% { opacity: 0; }
}

/* تأثيرات الارتداد */
.bounce {
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -20px, 0);
    }
    70% {
        transform: translate3d(0, -10px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* تأثيرات التلاشي */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* تأثيرات الضوء المتحرك */
.light-effect {
    position: relative;
    overflow: hidden;
}

.light-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transform: skewX(-25deg);
    animation: light-sweep 3s infinite;
}

@keyframes light-sweep {
    0% { left: -100%; }
    100% { left: 150%; }
}
