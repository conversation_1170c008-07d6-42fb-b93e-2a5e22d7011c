<?php
/**
 * API Controller
 * تحكم في واجهة برمجة التطبيقات
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class ApiController extends Controller
{
    private $auth;
    private $db;
    private $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    private $apiVersion = 'v1';

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // إعداد headers للـ API
        $this->setupApiHeaders();
        
        // التحقق من طريقة الطلب
        $this->validateRequestMethod();
    }

    /**
     * إعداد headers للـ API
     */
    private function setupApiHeaders()
    {
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, PATCH, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // معالجة طلبات OPTIONS
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }

    /**
     * التحقق من طريقة الطلب
     */
    private function validateRequestMethod()
    {
        if (!in_array($_SERVER['REQUEST_METHOD'], $this->allowedMethods)) {
            $this->apiError('Method not allowed', 405);
        }
    }

    /**
     * إرجاع استجابة API ناجحة
     */
    protected function apiSuccess($data = null, $message = 'Success', $code = 200)
    {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c'),
            'version' => $this->apiVersion
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * إرجاع استجابة API خطأ
     */
    protected function apiError($message = 'Error', $code = 400, $errors = null)
    {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => date('c'),
            'version' => $this->apiVersion
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * التحقق من المصادقة للـ API
     */
    protected function requireApiAuth()
    {
        $token = $this->getBearerToken();
        
        if (!$token) {
            $this->apiError('Authentication required', 401);
        }

        // التحقق من صحة الـ token
        if (!$this->validateApiToken($token)) {
            $this->apiError('Invalid token', 401);
        }
    }

    /**
     * الحصول على Bearer Token
     */
    private function getBearerToken()
    {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }

    /**
     * التحقق من صحة API Token
     */
    private function validateApiToken($token)
    {
        // في الإنتاج، يجب التحقق من الـ token في قاعدة البيانات
        // هنا نستخدم token بسيط للتطوير
        return $token === 'seasystem-api-token-2024';
    }

    /**
     * الحصول على بيانات الطلب
     */
    protected function getRequestData()
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->apiError('Invalid JSON data', 400);
        }
        
        return $data ?: [];
    }

    /**
     * تطبيق فلاتر على الاستعلام
     */
    protected function applyFilters($query, $filters = [])
    {
        $conditions = [];
        $params = [];

        foreach ($filters as $key => $value) {
            if (!empty($value)) {
                switch ($key) {
                    case 'search':
                        // بحث عام
                        break;
                    case 'status':
                        $conditions[] = "status = ?";
                        $params[] = $value;
                        break;
                    case 'date_from':
                        $conditions[] = "created_at >= ?";
                        $params[] = $value;
                        break;
                    case 'date_to':
                        $conditions[] = "created_at <= ?";
                        $params[] = $value;
                        break;
                }
            }
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        return [$query, $params];
    }

    /**
     * تطبيق ترقيم الصفحات
     */
    protected function applyPagination($query, $page = 1, $limit = 20)
    {
        $page = max(1, intval($page));
        $limit = min(100, max(1, intval($limit))); // حد أقصى 100 عنصر
        $offset = ($page - 1) * $limit;

        $query .= " LIMIT {$limit} OFFSET {$offset}";

        return [
            'query' => $query,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'offset' => $offset
            ]
        ];
    }

    /**
     * API للمستخدمين
     */
    public function users()
    {
        $this->requireApiAuth();

        switch ($_SERVER['REQUEST_METHOD']) {
            case 'GET':
                return $this->getUsers();
            case 'POST':
                return $this->createUser();
            default:
                $this->apiError('Method not allowed', 405);
        }
    }

    /**
     * الحصول على قائمة المستخدمين
     */
    private function getUsers()
    {
        try {
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $search = $_GET['search'] ?? '';

            $query = "SELECT id, username, email, first_name, last_name, is_active, created_at FROM users";
            
            if (!empty($search)) {
                $query .= " WHERE username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?";
                $searchTerm = "%{$search}%";
                $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
            } else {
                $params = [];
            }

            $query .= " ORDER BY created_at DESC";
            
            $paginationResult = $this->applyPagination($query, $page, $limit);
            $users = $this->db->select($paginationResult['query'], $params);

            // إحصائيات إضافية
            $totalUsers = $this->db->selectOne("SELECT COUNT(*) as count FROM users")['count'];

            $this->apiSuccess([
                'users' => $users,
                'pagination' => array_merge($paginationResult['pagination'], [
                    'total' => $totalUsers,
                    'pages' => ceil($totalUsers / $paginationResult['pagination']['limit'])
                ])
            ]);

        } catch (Exception $e) {
            $this->apiError('Failed to fetch users', 500);
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    private function createUser()
    {
        try {
            $data = $this->getRequestData();

            // التحقق من البيانات المطلوبة
            $required = ['username', 'email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    $this->apiError("Field '{$field}' is required", 400);
                }
            }

            // التحقق من عدم تكرار اسم المستخدم والبريد
            $existing = $this->db->selectOne(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                [$data['username'], $data['email']]
            );

            if ($existing) {
                $this->apiError('Username or email already exists', 409);
            }

            // إنشاء المستخدم
            $userId = $this->db->insert('users', [
                'username' => $data['username'],
                'email' => $data['email'],
                'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'phone' => $data['phone'] ?? null,
                'is_active' => $data['is_active'] ?? 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->apiSuccess(['user_id' => $userId], 'User created successfully', 201);

        } catch (Exception $e) {
            $this->apiError('Failed to create user', 500);
        }
    }

    /**
     * API للمنتجات
     */
    public function products()
    {
        $this->requireApiAuth();

        switch ($_SERVER['REQUEST_METHOD']) {
            case 'GET':
                return $this->getProducts();
            case 'POST':
                return $this->createProduct();
            default:
                $this->apiError('Method not allowed', 405);
        }
    }

    /**
     * الحصول على قائمة المنتجات
     */
    private function getProducts()
    {
        try {
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $search = $_GET['search'] ?? '';
            $category = $_GET['category'] ?? '';

            $query = "
                SELECT p.*, c.name as category_name, u.name as unit_name
                FROM products p 
                LEFT JOIN product_categories c ON p.category_id = c.id 
                LEFT JOIN units u ON p.unit_id = u.id
            ";

            $conditions = [];
            $params = [];

            if (!empty($search)) {
                $conditions[] = "(p.name LIKE ? OR p.sku LIKE ? OR p.description LIKE ?)";
                $searchTerm = "%{$search}%";
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
            }

            if (!empty($category)) {
                $conditions[] = "p.category_id = ?";
                $params[] = $category;
            }

            if (!empty($conditions)) {
                $query .= " WHERE " . implode(" AND ", $conditions);
            }

            $query .= " ORDER BY p.created_at DESC";
            
            $paginationResult = $this->applyPagination($query, $page, $limit);
            $products = $this->db->select($paginationResult['query'], $params);

            $this->apiSuccess(['products' => $products]);

        } catch (Exception $e) {
            $this->apiError('Failed to fetch products', 500);
        }
    }

    /**
     * إنشاء منتج جديد
     */
    private function createProduct()
    {
        try {
            $data = $this->getRequestData();

            // التحقق من البيانات المطلوبة
            if (empty($data['name'])) {
                $this->apiError('Product name is required', 400);
            }

            // إنشاء SKU تلقائياً إذا لم يتم توفيره
            if (empty($data['sku'])) {
                $lastProduct = $this->db->selectOne("SELECT sku FROM products ORDER BY id DESC LIMIT 1");
                $nextNumber = 1;
                if ($lastProduct) {
                    $lastNumber = intval(substr($lastProduct['sku'], 4));
                    $nextNumber = $lastNumber + 1;
                }
                $data['sku'] = 'PROD' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            }

            // إنشاء المنتج
            $productId = $this->db->insert('products', [
                'sku' => $data['sku'],
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'category_id' => $data['category_id'] ?? null,
                'unit_id' => $data['unit_id'] ?? null,
                'cost_price' => $data['cost_price'] ?? 0,
                'selling_price' => $data['selling_price'] ?? 0,
                'min_stock_level' => $data['min_stock_level'] ?? 0,
                'current_stock' => $data['current_stock'] ?? 0,
                'barcode' => $data['barcode'] ?? null,
                'is_active' => $data['is_active'] ?? 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->apiSuccess(['product_id' => $productId], 'Product created successfully', 201);

        } catch (Exception $e) {
            $this->apiError('Failed to create product', 500);
        }
    }

    /**
     * API للإحصائيات
     */
    public function stats()
    {
        $this->requireApiAuth();

        try {
            $stats = [
                'users' => [
                    'total' => $this->db->selectOne("SELECT COUNT(*) as count FROM users")['count'],
                    'active' => $this->db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count']
                ],
                'products' => [
                    'total' => $this->db->selectOne("SELECT COUNT(*) as count FROM products")['count'],
                    'active' => $this->db->selectOne("SELECT COUNT(*) as count FROM products WHERE is_active = 1")['count'],
                    'low_stock' => $this->db->selectOne("SELECT COUNT(*) as count FROM products WHERE current_stock <= min_stock_level")['count']
                ],
                'customers' => [
                    'total' => $this->db->selectOne("SELECT COUNT(*) as count FROM customers")['count'],
                    'active' => $this->db->selectOne("SELECT COUNT(*) as count FROM customers WHERE is_active = 1")['count']
                ]
            ];

            $this->apiSuccess($stats);

        } catch (Exception $e) {
            $this->apiError('Failed to fetch statistics', 500);
        }
    }

    /**
     * معلومات API
     */
    public function info()
    {
        $this->apiSuccess([
            'name' => 'SeaSystem ERP API',
            'version' => $this->apiVersion,
            'description' => 'RESTful API for SeaSystem ERP',
            'endpoints' => [
                'GET /api/users' => 'Get users list',
                'POST /api/users' => 'Create new user',
                'GET /api/products' => 'Get products list',
                'POST /api/products' => 'Create new product',
                'GET /api/stats' => 'Get system statistics',
                'GET /api/info' => 'Get API information'
            ],
            'authentication' => 'Bearer Token required',
            'rate_limit' => '1000 requests per hour'
        ]);
    }
}
?>
