# SeaSystem Environment Configuration
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب بيئتك

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=R1
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4

# إعدادات التطبيق
APP_NAME="SeaSystem ERP"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/seasystem-R1
APP_TIMEZONE=Asia/Riyadh

# إعدادات الأمان
APP_KEY=your-secret-key-here-32-characters
SESSION_LIFETIME=3600
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# إعدادات البريد الإلكتروني
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="SeaSystem ERP"

# إعدادات الملفات
UPLOAD_MAX_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
UPLOAD_PATH=uploads/

# إعدادات التسجيل
LOG_LEVEL=info
LOG_PATH=logs/
LOG_MAX_FILES=30

# إعدادات الشركة
COMPANY_NAME="شركة SeaSystem"
COMPANY_ADDRESS=""
COMPANY_PHONE=""
COMPANY_EMAIL=""
DEFAULT_CURRENCY=SAR
TAX_RATE=15

# إعدادات التطوير
ENABLE_QUERY_LOG=false
SHOW_ERRORS=true
