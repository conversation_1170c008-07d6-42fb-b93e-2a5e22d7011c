{"timestamp":"2025-07-04 14:02:51","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:03:25","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:05:41","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:06:34","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:07:36","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:08:14","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:09:31","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:11:19","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:18:58","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:20:10","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:20:29","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:21:40","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 14:22:43","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
{"timestamp":"2025-07-04 13:23:13","message":"خطأ في استعلام SELECT: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'r1.login_attempts' doesn't exist","query":"SELECT COUNT(*) as count, MAX(attempted_at) as last_attempt \n             FROM login_attempts \n             WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)","params":["admin","900"]}
