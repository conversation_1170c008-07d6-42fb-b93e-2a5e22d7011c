<?php
/**
 * اختبار مباشر لكلاس Auth
 */

// تحميل الملفات الأساسية
require_once 'core/Environment.php';
Environment::load();
require_once 'core/helpers.php';
require_once 'core/Database.php';
require_once 'core/Session.php';
require_once 'core/Auth.php';

echo "🧪 اختبار مباشر لكلاس Auth\n";
echo "============================\n\n";

try {
    echo "1. إنشاء مثيل Auth...\n";
    $auth = new Auth();
    echo "   ✅ تم إنشاء مثيل Auth\n";
    
    echo "2. اختبار تسجيل الدخول...\n";
    $result = $auth->login('admin', 'admin123', false);
    
    echo "3. النتيجة:\n";
    print_r($result);
    
    if ($result['success']) {
        echo "\n✅ تم تسجيل الدخول بنجاح!\n";
    } else {
        echo "\n❌ فشل تسجيل الدخول: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
    echo "\nStack Trace:\n" . $e->getTraceAsString() . "\n";
}
?>
