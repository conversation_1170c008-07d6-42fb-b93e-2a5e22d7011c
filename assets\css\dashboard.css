/* SeaSystem Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    overflow-x: hidden;
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

/* رأس القائمة الجانبية */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 80px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: bold;
    color: #3498db;
}

.logo i {
    font-size: 28px;
}

.sidebar.collapsed .logo-text {
    display: none;
}

.toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* قائمة القائمة الجانبية */
.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    margin-bottom: 5px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
}

.menu-item.active .menu-link {
    background-color: #3498db;
    color: white;
}

.menu-link i {
    width: 20px;
    margin-left: 15px;
    text-align: center;
    font-size: 16px;
}

.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .submenu-arrow {
    display: none;
}

.submenu-arrow {
    margin-right: auto;
    transition: transform 0.3s ease;
}

.menu-item.open .submenu-arrow {
    transform: rotate(180deg);
}

/* القوائم الفرعية */
.submenu {
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: rgba(0, 0, 0, 0.2);
}

.menu-item.open .submenu {
    max-height: 500px;
}

.sidebar.collapsed .submenu {
    display: none;
}

.submenu li {
    margin: 0;
}

.submenu a {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 55px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.submenu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
    padding-right: 25px;
}

.submenu a i {
    width: 16px;
    margin-left: 10px;
    font-size: 14px;
}

/* معلومات المستخدم */
.user-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .user-info {
    padding: 15px 10px;
    text-align: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar i {
    font-size: 32px;
    color: #3498db;
}

.user-details {
    flex: 1;
}

.sidebar.collapsed .user-details {
    display: none;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-role {
    display: block;
    font-size: 12px;
    color: #bdc3c7;
}

.user-actions {
    display: flex;
    gap: 8px;
}

.sidebar.collapsed .user-actions {
    display: none;
}

.user-actions a {
    color: #bdc3c7;
    font-size: 14px;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.user-actions a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #3498db;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* الشريط العلوي */
.top-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1 {
    color: #2c3e50;
    font-size: 28px;
    margin-bottom: 5px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 14px;
}

.breadcrumb i {
    font-size: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 18px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.current-time {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* محتوى لوحة التحكم */
.dashboard-content {
    padding: 30px;
}

/* البطاقات الإحصائية */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-info h3 {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.stat-number {
    color: #2c3e50;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 12px;
    font-weight: 500;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

/* الويدجت */
.dashboard-widgets {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.widget {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.widget-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.widget-actions {
    display: flex;
    gap: 10px;
}

.widget-btn {
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 14px;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.widget-btn:hover {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.view-all {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.view-all:hover {
    text-decoration: underline;
}

.widget-content {
    padding: 25px;
}

.chart-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #bdc3c7;
}

.chart-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

/* الجداول */
.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ecf0f1;
}

.data-table th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
}

.data-table td {
    color: #7f8c8d;
    font-size: 14px;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status.shipped {
    background-color: #cce5ff;
    color: #004085;
}

/* الأنشطة الأخيرة */
.recent-activities {
    margin-top: 30px;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.activity-content p {
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 5px;
}

.activity-time {
    color: #7f8c8d;
    font-size: 12px;
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .top-header {
        padding: 15px 20px;
    }
    
    .dashboard-content {
        padding: 20px;
    }
}
