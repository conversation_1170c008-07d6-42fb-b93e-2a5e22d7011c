<?php
echo "بدء الاختبار...\n";

try {
    require_once 'core/Environment.php';
    echo "تم تحميل Environment\n";
    
    Environment::load();
    echo "تم تحميل إعدادات البيئة\n";
    
    require_once 'core/helpers.php';
    echo "تم تحميل helpers\n";
    
    require_once 'core/Database.php';
    echo "تم تحميل Database\n";
    
    $db = Database::getInstance();
    echo "تم إنشاء مثيل Database\n";
    
    // اختبار بسيط
    $user = $db->selectOne("SELECT * FROM users WHERE username = ?", ['admin']);
    if ($user) {
        echo "المستخدم موجود: " . $user['username'] . "\n";
        
        // اختبار تحديث بسيط
        $result = $db->update('users', ['last_login_at' => date('Y-m-d H:i:s')], ['id' => $user['id']]);
        echo "نتيجة التحديث: {$result}\n";
        
    } else {
        echo "المستخدم غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?>
