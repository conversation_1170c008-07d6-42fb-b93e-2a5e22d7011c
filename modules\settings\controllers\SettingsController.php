<?php
/**
 * SettingsController
 * تحكم في إعدادات النظام
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class SettingsController extends Controller
{
    private $auth;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // التحقق من تسجيل الدخول
        $this->requireAuth();
    }

    /**
     * صفحة الإعدادات الرئيسية
     */
    public function index()
    {
        $this->render('settings/index', [
            'title' => 'إعدادات النظام'
        ]);
    }

    /**
     * إعدادات الشركة
     */
    public function company()
    {
        $settings = $this->getSettingsByCategory([
            'company_name', 'company_email', 'company_phone', 'company_address',
            'company_website', 'company_logo', 'tax_number', 'commercial_register'
        ]);

        $this->render('settings/company', [
            'title' => 'إعدادات الشركة',
            'settings' => $settings
        ]);
    }

    /**
     * إعدادات النظام
     */
    public function system()
    {
        $settings = $this->getSettingsByCategory([
            'default_currency', 'timezone', 'date_format', 'items_per_page',
            'session_timeout', 'max_login_attempts', 'enable_api',
            'enable_notifications', 'enable_email_notifications'
        ]);

        $this->render('settings/system', [
            'title' => 'إعدادات النظام',
            'settings' => $settings
        ]);
    }

    /**
     * إعدادات البريد الإلكتروني
     */
    public function email()
    {
        $settings = $this->getSettingsByCategory([
            'mail_driver', 'mail_host', 'mail_port', 'mail_username',
            'mail_password', 'mail_encryption', 'mail_from_address', 'mail_from_name'
        ]);

        $this->render('settings/email', [
            'title' => 'إعدادات البريد الإلكتروني',
            'settings' => $settings
        ]);
    }

    /**
     * النسخ الاحتياطي
     */
    public function backup()
    {
        $backups = $this->db->select("
            SELECT * FROM backups 
            ORDER BY created_at DESC 
            LIMIT 20
        ");

        $this->render('settings/backup', [
            'title' => 'النسخ الاحتياطي',
            'backups' => $backups
        ]);
    }

    /**
     * سجل الأخطاء
     */
    public function logs()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 50;
        $offset = ($page - 1) * $limit;

        $logs = $this->db->select("
            SELECT el.*, u.username 
            FROM error_logs el
            LEFT JOIN users u ON el.user_id = u.id
            ORDER BY el.created_at DESC 
            LIMIT ? OFFSET ?
        ", [$limit, $offset]);

        $totalLogs = $this->db->selectOne("SELECT COUNT(*) as count FROM error_logs")['count'];

        $this->render('settings/logs', [
            'title' => 'سجل الأخطاء',
            'logs' => $logs,
            'currentPage' => $page,
            'totalPages' => ceil($totalLogs / $limit),
            'totalLogs' => $totalLogs
        ]);
    }

    /**
     * حفظ الإعدادات
     */
    public function save()
    {
        try {
            $settings = $_POST['settings'] ?? [];
            
            if (empty($settings)) {
                throw new Exception('لا توجد إعدادات للحفظ');
            }

            foreach ($settings as $key => $value) {
                $this->updateSetting($key, $value);
            }

            $this->jsonResponse([
                'success' => true,
                'message' => 'تم حفظ الإعدادات بنجاح'
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * إنشاء نسخة احتياطية
     */
    public function createBackup()
    {
        try {
            $type = $_POST['type'] ?? 'full';
            $backupName = 'backup_' . date('Y-m-d_H-i-s');
            
            // إنشاء مجلد النسخ الاحتياطي
            $backupDir = dirname(__DIR__, 3) . '/storage/backups';
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }

            $backupId = $this->db->insert('backups', [
                'backup_name' => $backupName,
                'backup_type' => $type,
                'file_path' => '',
                'status' => 'pending',
                'created_by' => $this->auth->getUserId(),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // تنفيذ النسخ الاحتياطي في الخلفية
            $this->executeBackup($backupId, $type, $backupName, $backupDir);

            $this->jsonResponse([
                'success' => true,
                'message' => 'تم بدء إنشاء النسخة الاحتياطية',
                'backup_id' => $backupId
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * تنفيذ النسخ الاحتياطي
     */
    private function executeBackup($backupId, $type, $backupName, $backupDir)
    {
        try {
            $filePath = '';
            $fileSize = 0;

            switch ($type) {
                case 'database':
                    $filePath = $this->backupDatabase($backupName, $backupDir);
                    break;
                case 'files':
                    $filePath = $this->backupFiles($backupName, $backupDir);
                    break;
                case 'full':
                    $filePath = $this->backupFull($backupName, $backupDir);
                    break;
            }

            if ($filePath && file_exists($filePath)) {
                $fileSize = filesize($filePath);
                
                $this->db->update('backups', [
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'status' => 'completed',
                    'completed_at' => date('Y-m-d H:i:s')
                ], ['id' => $backupId]);
            } else {
                throw new Exception('فشل في إنشاء النسخة الاحتياطية');
            }

        } catch (Exception $e) {
            $this->db->update('backups', [
                'status' => 'failed'
            ], ['id' => $backupId]);
            
            error_log("Backup failed: " . $e->getMessage());
        }
    }

    /**
     * نسخ احتياطي لقاعدة البيانات
     */
    private function backupDatabase($backupName, $backupDir)
    {
        $filename = $backupDir . '/' . $backupName . '_database.sql';
        
        // استخدام mysqldump
        $command = "mysqldump --host=localhost --user=root --password= --single-transaction --routines --triggers R1 > " . escapeshellarg($filename);
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($filename)) {
            return $filename;
        }
        
        throw new Exception('فشل في نسخ قاعدة البيانات');
    }

    /**
     * نسخ احتياطي للملفات
     */
    private function backupFiles($backupName, $backupDir)
    {
        $filename = $backupDir . '/' . $backupName . '_files.zip';
        $rootDir = dirname(__DIR__, 3);
        
        $zip = new ZipArchive();
        if ($zip->open($filename, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('فشل في إنشاء ملف ZIP');
        }

        $this->addDirectoryToZip($zip, $rootDir, '', ['storage/backups', 'storage/logs', '.git']);
        $zip->close();

        return $filename;
    }

    /**
     * نسخ احتياطي كامل
     */
    private function backupFull($backupName, $backupDir)
    {
        // نسخ قاعدة البيانات أولاً
        $dbBackup = $this->backupDatabase($backupName, $backupDir);
        
        // ثم نسخ الملفات
        $filesBackup = $this->backupFiles($backupName, $backupDir);
        
        // دمج النسختين في ملف واحد
        $fullBackup = $backupDir . '/' . $backupName . '_full.zip';
        
        $zip = new ZipArchive();
        if ($zip->open($fullBackup, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('فشل في إنشاء النسخة الكاملة');
        }

        $zip->addFile($dbBackup, 'database.sql');
        $zip->addFile($filesBackup, 'files.zip');
        $zip->close();

        // حذف الملفات المؤقتة
        unlink($dbBackup);
        unlink($filesBackup);

        return $fullBackup;
    }

    /**
     * إضافة مجلد إلى ZIP
     */
    private function addDirectoryToZip($zip, $dir, $zipPath = '', $exclude = [])
    {
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $filePath = $dir . '/' . $file;
            $zipFilePath = $zipPath ? $zipPath . '/' . $file : $file;
            
            // تحقق من الاستثناءات
            $skip = false;
            foreach ($exclude as $excludePattern) {
                if (strpos($zipFilePath, $excludePattern) === 0) {
                    $skip = true;
                    break;
                }
            }
            
            if ($skip) continue;
            
            if (is_dir($filePath)) {
                $zip->addEmptyDir($zipFilePath);
                $this->addDirectoryToZip($zip, $filePath, $zipFilePath, $exclude);
            } else {
                $zip->addFile($filePath, $zipFilePath);
            }
        }
    }

    /**
     * تحميل نسخة احتياطية
     */
    public function downloadBackup($id)
    {
        $backup = $this->db->selectOne("SELECT * FROM backups WHERE id = ?", [$id]);
        
        if (!$backup || !file_exists($backup['file_path'])) {
            $this->showError('النسخة الاحتياطية غير موجودة');
            return;
        }

        $filename = basename($backup['file_path']);
        
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($backup['file_path']));
        
        readfile($backup['file_path']);
        exit;
    }

    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($id)
    {
        try {
            $backup = $this->db->selectOne("SELECT * FROM backups WHERE id = ?", [$id]);
            
            if (!$backup) {
                throw new Exception('النسخة الاحتياطية غير موجودة');
            }

            // حذف الملف
            if (file_exists($backup['file_path'])) {
                unlink($backup['file_path']);
            }

            // حذف السجل
            $this->db->delete('backups', ['id' => $id]);

            $this->jsonResponse([
                'success' => true,
                'message' => 'تم حذف النسخة الاحتياطية بنجاح'
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * الحصول على إعدادات حسب الفئة
     */
    private function getSettingsByCategory($keys)
    {
        $placeholders = str_repeat('?,', count($keys) - 1) . '?';
        
        $settings = $this->db->select("
            SELECT setting_key, setting_value, setting_type, description 
            FROM system_settings 
            WHERE setting_key IN ({$placeholders})
        ", $keys);

        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = [
                'value' => $this->castSettingValue($setting['setting_value'], $setting['setting_type']),
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }

        return $result;
    }

    /**
     * تحديث إعداد
     */
    private function updateSetting($key, $value)
    {
        $setting = $this->db->selectOne("
            SELECT setting_type FROM system_settings WHERE setting_key = ?
        ", [$key]);

        if (!$setting) {
            // إنشاء إعداد جديد
            $this->db->insert('system_settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'setting_type' => 'string',
                'updated_by' => $this->auth->getUserId()
            ]);
        } else {
            // تحديث الإعداد الموجود
            $this->db->update('system_settings', [
                'setting_value' => $value,
                'updated_by' => $this->auth->getUserId()
            ], ['setting_key' => $key]);
        }
    }

    /**
     * تحويل قيمة الإعداد حسب النوع
     */
    private function castSettingValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }

    /**
     * تنظيف السجلات القديمة
     */
    public function cleanup()
    {
        try {
            $days = $_POST['days'] ?? 30;
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));

            // تنظيف سجل الأخطاء
            $deletedLogs = $this->db->delete('error_logs', ['created_at <' => $cutoffDate]);

            // تنظيف الإشعارات المقروءة
            $deletedNotifications = $this->db->delete('notifications', [
                'created_at <' => $cutoffDate,
                'is_read' => 1
            ]);

            // تنظيف الجلسات المنتهية
            $deletedSessions = $this->db->delete('user_sessions', [
                'last_activity <' => $cutoffDate
            ]);

            $this->jsonResponse([
                'success' => true,
                'message' => 'تم تنظيف النظام بنجاح',
                'details' => [
                    'deleted_logs' => $deletedLogs,
                    'deleted_notifications' => $deletedNotifications,
                    'deleted_sessions' => $deletedSessions
                ]
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
?>
