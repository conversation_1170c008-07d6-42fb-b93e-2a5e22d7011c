<?php
/**
 * SeaSystem Quick Start Script
 * سكريبت التشغيل السريع لنظام SeaSystem
 * 
 * هذا الملف يساعد في إعداد وتشغيل النظام بسرعة
 */

echo "🚀 مرحباً بك في SeaSystem ERP\n";
echo "================================\n\n";

// التحقق من متطلبات PHP
echo "📋 التحقق من متطلبات النظام...\n";

$requirements = [
    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
    'MBString Extension' => extension_loaded('mbstring'),
    'JSON Extension' => extension_loaded('json'),
    'OpenSSL Extension' => extension_loaded('openssl'),
];

$allRequirementsMet = true;

foreach ($requirements as $requirement => $met) {
    $status = $met ? '✅' : '❌';
    echo "   {$status} {$requirement}\n";
    if (!$met) {
        $allRequirementsMet = false;
    }
}

if (!$allRequirementsMet) {
    echo "\n❌ بعض المتطلبات غير متوفرة. يرجى تثبيتها أولاً.\n";
    exit(1);
}

echo "\n✅ جميع المتطلبات متوفرة!\n\n";

// التحقق من ملف .env
echo "🔧 التحقق من إعدادات التكوين...\n";

if (!file_exists('config/.env')) {
    echo "   ❌ ملف .env غير موجود\n";
    echo "   📝 إنشاء ملف .env من المثال...\n";
    
    if (file_exists('config/.env.example')) {
        copy('config/.env.example', 'config/.env');
        echo "   ✅ تم إنشاء ملف .env\n";
    } else {
        echo "   ❌ ملف .env.example غير موجود\n";
    }
} else {
    echo "   ✅ ملف .env موجود\n";
}

// التحقق من المجلدات المطلوبة
echo "\n📁 التحقق من المجلدات...\n";

$directories = [
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الملفات المرفوعة',
    'storage/cache' => 'مجلد الكاش',
    'storage/sessions' => 'مجلد الجلسات',
    'storage/backups' => 'مجلد النسخ الاحتياطي'
];

foreach ($directories as $dir => $description) {
    if (!is_dir($dir)) {
        echo "   📝 إنشاء {$description}...\n";
        mkdir($dir, 0755, true);
    }
    
    if (is_writable($dir)) {
        echo "   ✅ {$description} - قابل للكتابة\n";
    } else {
        echo "   ⚠️  {$description} - غير قابل للكتابة\n";
        chmod($dir, 0755);
    }
}

// اختبار الاتصال بقاعدة البيانات
echo "\n🗄️  اختبار الاتصال بقاعدة البيانات...\n";

try {
    require_once 'core/Environment.php';
    Environment::load();
    
    $host = Environment::get('DB_HOST', 'localhost');
    $port = Environment::get('DB_PORT', 3306);
    $dbname = Environment::get('DB_NAME', 'R1');
    $username = Environment::get('DB_USERNAME', 'root');
    $password = Environment::get('DB_PASSWORD', '');
    
    $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "   ✅ الاتصال بخادم MySQL نجح\n";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbname}'");
    if ($stmt->rowCount() > 0) {
        echo "   ✅ قاعدة البيانات {$dbname} موجودة\n";
        
        // التحقق من الجداول
        $pdo->exec("USE {$dbname}");
        $stmt = $pdo->query("SHOW TABLES");
        $tableCount = $stmt->rowCount();
        
        if ($tableCount > 0) {
            echo "   ✅ تم العثور على {$tableCount} جدول في قاعدة البيانات\n";
        } else {
            echo "   ⚠️  قاعدة البيانات فارغة - تحتاج لتشغيل ملفات الترحيل\n";
            echo "   💡 قم بتشغيل: php setup-database.php\n";
        }
    } else {
        echo "   ❌ قاعدة البيانات {$dbname} غير موجودة\n";
        echo "   💡 قم بإنشاء قاعدة البيانات أولاً\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
    echo "   💡 تحقق من إعدادات قاعدة البيانات في ملف .env\n";
}

// معلومات التشغيل
echo "\n🌐 معلومات التشغيل:\n";
echo "   📍 مسار المشروع: " . __DIR__ . "\n";
echo "   🔗 رابط النظام: http://localhost/seasystem-R1/public\n";
echo "   👤 المستخدم التجريبي: admin\n";
echo "   🔑 كلمة المرور: admin123\n";

// التحقق من خادم الويب
echo "\n🌍 التحقق من خادم الويب...\n";

if (isset($_SERVER['SERVER_SOFTWARE'])) {
    echo "   ✅ خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
} else {
    echo "   ⚠️  يتم التشغيل من سطر الأوامر\n";
    echo "   💡 لتشغيل خادم تطوير محلي:\n";
    echo "      php -S localhost:8000 -t public\n";
}

// نصائح مفيدة
echo "\n💡 نصائح مفيدة:\n";
echo "   🔧 لإعداد قاعدة البيانات: php setup-database.php\n";
echo "   🗄️  لإنشاء نسخة احتياطية: php backup.php\n";
echo "   📊 لعرض إحصائيات النظام: php stats.php\n";
echo "   🧹 لتنظيف الملفات المؤقتة: php cleanup.php\n";

echo "\n🎉 النظام جاهز للاستخدام!\n";
echo "   🌐 افتح المتصفح وتوجه إلى: http://localhost/seasystem-R1/public\n";
echo "   📚 للمزيد من المعلومات، راجع ملف README.md\n\n";

// إنشاء ملف معلومات النظام
$systemInfo = [
    'project_name' => 'SeaSystem ERP',
    'version' => '1.0.0',
    'php_version' => PHP_VERSION,
    'setup_date' => date('Y-m-d H:i:s'),
    'requirements_met' => $allRequirementsMet,
    'database_configured' => isset($pdo),
];

file_put_contents('system-info.json', json_encode($systemInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📄 تم حفظ معلومات النظام في ملف system-info.json\n";
echo "================================\n";
echo "شكراً لاستخدام SeaSystem ERP! 🚀\n";
?>
