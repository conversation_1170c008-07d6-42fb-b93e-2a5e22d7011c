<?php
/**
 * SeaSystem Database Configuration
 * إعدادات قاعدة البيانات
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(__DIR__) . '/core/Environment.php';

// تحميل متغيرات البيئة
Environment::load();

return [
    // إعدادات الاتصال الافتراضية
    'default' => 'mysql',
    
    // إعدادات قواعد البيانات المختلفة
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => Environment::get('DB_HOST', 'localhost'),
            'port' => Environment::get('DB_PORT', 3306),
            'database' => Environment::get('DB_NAME', 'R1'),
            'username' => Environment::get('DB_USERNAME', 'root'),
            'password' => Environment::get('DB_PASSWORD', ''),
            'charset' => Environment::get('DB_CHARSET', 'utf8mb4'),
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ],
        
        // إعدادات قاعدة بيانات الاختبار
        'testing' => [
            'driver' => 'mysql',
            'host' => Environment::get('TEST_DB_HOST', 'localhost'),
            'port' => Environment::get('TEST_DB_PORT', 3306),
            'database' => Environment::get('TEST_DB_NAME', 'R1_test'),
            'username' => Environment::get('TEST_DB_USERNAME', 'root'),
            'password' => Environment::get('TEST_DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB'
        ]
    ],
    
    // إعدادات الترحيل
    'migrations' => [
        'table' => 'migrations',
        'path' => dirname(__DIR__) . '/database/migrations'
    ],
    
    // إعدادات البذور
    'seeds' => [
        'path' => dirname(__DIR__) . '/database/seeds'
    ],
    
    // إعدادات التسجيل
    'logging' => [
        'enabled' => Environment::get('ENABLE_QUERY_LOG', false),
        'slow_query_threshold' => 1000, // بالميلي ثانية
        'log_file' => dirname(__DIR__) . '/logs/database.log'
    ],
    
    // إعدادات الأداء
    'performance' => [
        'connection_timeout' => 30,
        'query_timeout' => 60,
        'max_connections' => 100,
        'connection_pool_size' => 10
    ]
];

/**
 * دالة مساعدة للحصول على إعدادات قاعدة البيانات
 * 
 * @param string $key المفتاح المطلوب
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function db_config($key = null, $default = null)
{
    static $config = null;
    
    if ($config === null) {
        $config = include __FILE__;
    }
    
    if ($key === null) {
        return $config;
    }
    
    $keys = explode('.', $key);
    $value = $config;
    
    foreach ($keys as $k) {
        if (is_array($value) && array_key_exists($k, $value)) {
            $value = $value[$k];
        } else {
            return $default;
        }
    }
    
    return $value;
}

/**
 * دالة مساعدة للحصول على إعدادات الاتصال
 * 
 * @param string $connection اسم الاتصال
 * @return array
 */
function get_connection_config($connection = null)
{
    if ($connection === null) {
        $connection = db_config('default', 'mysql');
    }
    
    return db_config("connections.{$connection}", []);
}

/**
 * دالة للتحقق من صحة إعدادات قاعدة البيانات
 * 
 * @param string $connection اسم الاتصال
 * @return array مصفوفة بالأخطاء إن وجدت
 */
function validate_db_config($connection = null)
{
    $config = get_connection_config($connection);
    $errors = [];
    
    $required = ['host', 'database', 'username'];
    
    foreach ($required as $key) {
        if (!isset($config[$key]) || empty($config[$key])) {
            $errors[] = "إعداد قاعدة البيانات المطلوب '{$key}' غير محدد";
        }
    }
    
    // التحقق من رقم المنفذ
    if (isset($config['port'])) {
        $port = $config['port'];
        if (!is_numeric($port) || $port < 1 || $port > 65535) {
            $errors[] = "رقم منفذ قاعدة البيانات غير صحيح";
        }
    }
    
    return $errors;
}

/**
 * دالة لاختبار الاتصال بقاعدة البيانات
 * 
 * @param string $connection اسم الاتصال
 * @return bool|string true إذا نجح الاتصال، رسالة الخطأ إذا فشل
 */
function test_db_connection($connection = null)
{
    try {
        $config = get_connection_config($connection);
        
        if (empty($config)) {
            return "إعدادات الاتصال غير موجودة";
        }
        
        $dsn = sprintf(
            "mysql:host=%s;port=%d;dbname=%s;charset=%s",
            $config['host'],
            $config['port'] ?? 3306,
            $config['database'],
            $config['charset'] ?? 'utf8mb4'
        );
        
        $pdo = new PDO(
            $dsn,
            $config['username'],
            $config['password'],
            $config['options'] ?? []
        );
        
        // اختبار بسيط
        $pdo->query('SELECT 1');
        
        return true;
        
    } catch (PDOException $e) {
        return "فشل الاتصال: " . $e->getMessage();
    } catch (Exception $e) {
        return "خطأ: " . $e->getMessage();
    }
}
