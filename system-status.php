<?php
/**
 * SeaSystem Status Page
 * صفحة حالة النظام
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>حالة النظام - SeaSystem ERP</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }";
echo ".status-good { color: #28a745; }";
echo ".status-warning { color: #ffc107; }";
echo ".status-error { color: #dc3545; }";
echo ".grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }";
echo ".card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }";
echo "table { width: 100%; border-collapse: collapse; margin-top: 15px; }";
echo "th, td { padding: 10px; text-align: right; border-bottom: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn:hover { background: #0056b3; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🚀 SeaSystem ERP - حالة النظام</h1>";
echo "<p>تاريخ التحقق: " . date('Y-m-d H:i:s') . "</p>";

// روابط سريعة
echo "<div style='margin: 20px 0;'>";
echo "<a href='http://localhost:8000' class='btn' target='_blank'>🌐 فتح النظام</a>";
echo "<a href='http://localhost:8000/dashboard' class='btn' target='_blank'>📊 لوحة التحكم</a>";
echo "<a href='file:///C:/xampp/htdocs/seasystem-R1/test-login.html' class='btn' target='_blank'>🧪 اختبار تسجيل الدخول</a>";
echo "</div>";
echo "</div>";

// فحص PHP
echo "<div class='container'>";
echo "<h2>🐘 معلومات PHP</h2>";
echo "<div class='grid'>";

echo "<div class='card'>";
echo "<h3>الإصدار والإضافات</h3>";
echo "<table>";
echo "<tr><td>إصدار PHP</td><td class='status-good'>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>PDO</td><td class='" . (extension_loaded('pdo') ? 'status-good">✅ متوفر' : 'status-error">❌ غير متوفر') . "</td></tr>";
echo "<tr><td>PDO MySQL</td><td class='" . (extension_loaded('pdo_mysql') ? 'status-good'>✅ متوفر' : 'status-error'>❌ غير متوفر') . "</td></tr>";
echo "<tr><td>MBString</td><td class='" . (extension_loaded('mbstring') ? 'status-good'>✅ متوفر' : 'status-error'>❌ غير متوفر') . "</td></tr>";
echo "<tr><td>JSON</td><td class='" . (extension_loaded('json') ? 'status-good'>✅ متوفر' : 'status-error'>❌ غير متوفر') . "</td></tr>";
echo "<tr><td>OpenSSL</td><td class='" . (extension_loaded('openssl') ? 'status-good'>✅ متوفر' : 'status-error'>❌ غير متوفر') . "</td></tr>";
echo "</table>";
echo "</div>";

echo "<div class='card'>";
echo "<h3>إعدادات الذاكرة</h3>";
echo "<table>";
echo "<tr><td>حد الذاكرة</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>وقت التنفيذ الأقصى</td><td>" . ini_get('max_execution_time') . " ثانية</td></tr>";
echo "<tr><td>حجم الملف الأقصى</td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td>حجم POST الأقصى</td><td>" . ini_get('post_max_size') . "</td></tr>";
echo "</table>";
echo "</div>";

echo "</div>";
echo "</div>";

// فحص قاعدة البيانات
echo "<div class='container'>";
echo "<h2>🗄️ قاعدة البيانات</h2>";

try {
    require_once 'core/Environment.php';
    Environment::load();
    
    $host = Environment::get('DB_HOST', 'localhost');
    $port = Environment::get('DB_PORT', 3306);
    $dbname = Environment::get('DB_NAME', 'R1');
    $username = Environment::get('DB_USERNAME', 'root');
    $password = Environment::get('DB_PASSWORD', '');
    
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='grid'>";
    
    echo "<div class='card'>";
    echo "<h3>معلومات الاتصال</h3>";
    echo "<table>";
    echo "<tr><td>الحالة</td><td class='status-good'>✅ متصل</td></tr>";
    echo "<tr><td>الخادم</td><td>{$host}:{$port}</td></tr>";
    echo "<tr><td>قاعدة البيانات</td><td>{$dbname}</td></tr>";
    echo "<tr><td>المستخدم</td><td>{$username}</td></tr>";
    
    // إصدار MySQL
    $version = $pdo->query("SELECT VERSION()")->fetchColumn();
    echo "<tr><td>إصدار MySQL</td><td>{$version}</td></tr>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='card'>";
    echo "<h3>الجداول</h3>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>عدد الجداول: <strong>" . count($tables) . "</strong></p>";
    echo "<table>";
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `{$table}`")->fetchColumn();
        echo "<tr><td>{$table}</td><td>{$count} سجل</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='card'>";
    echo "<h3 class='status-error'>❌ خطأ في قاعدة البيانات</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// فحص الملفات والمجلدات
echo "<div class='container'>";
echo "<h2>📁 الملفات والمجلدات</h2>";

$directories = [
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الملفات المرفوعة',
    'storage/cache' => 'مجلد الكاش',
    'storage/sessions' => 'مجلد الجلسات',
    'storage/backups' => 'مجلد النسخ الاحتياطي'
];

echo "<table>";
echo "<tr><th>المجلد</th><th>الوصف</th><th>الحالة</th><th>الصلاحيات</th></tr>";

foreach ($directories as $dir => $description) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    
    echo "<tr>";
    echo "<td>{$dir}</td>";
    echo "<td>{$description}</td>";
    echo "<td class='" . ($exists ? 'status-good'>✅ موجود' : 'status-error'>❌ غير موجود') . "</td>";
    echo "<td class='" . ($writable ? 'status-good'>✅ قابل للكتابة' : 'status-warning'>⚠️ غير قابل للكتابة') . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// فحص الملفات الأساسية
echo "<div class='container'>";
echo "<h2>📄 الملفات الأساسية</h2>";

$coreFiles = [
    'core/Environment.php' => 'إدارة متغيرات البيئة',
    'core/Database.php' => 'إدارة قاعدة البيانات',
    'core/Auth.php' => 'نظام المصادقة',
    'core/Session.php' => 'إدارة الجلسات',
    'core/RBAC.php' => 'نظام الأدوار والصلاحيات',
    'config/.env' => 'ملف إعدادات البيئة',
    'public/index.php' => 'نقطة الدخول الرئيسية'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الوصف</th><th>الحالة</th></tr>";

foreach ($coreFiles as $file => $description) {
    $exists = file_exists($file);
    echo "<tr>";
    echo "<td>{$file}</td>";
    echo "<td>{$description}</td>";
    echo "<td class='" . ($exists ? 'status-good'>✅ موجود' : 'status-error'>❌ غير موجود') . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// معلومات النظام
echo "<div class='container'>";
echo "<h2>ℹ️ معلومات النظام</h2>";

if (file_exists('system-info.json')) {
    $systemInfo = json_decode(file_get_contents('system-info.json'), true);
    echo "<table>";
    foreach ($systemInfo as $key => $value) {
        echo "<tr><td>" . ucfirst(str_replace('_', ' ', $key)) . "</td><td>" . (is_bool($value) ? ($value ? 'نعم' : 'لا') : htmlspecialchars($value)) . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>ملف معلومات النظام غير موجود. قم بتشغيل <code>php start.php</code> لإنشائه.</p>";
}

echo "</div>";

// خلاصة الحالة
echo "<div class='container'>";
echo "<h2>📋 خلاصة الحالة</h2>";

$allGood = true;
$issues = [];

// فحص PHP
if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
    $allGood = false;
    $issues[] = "إضافات PHP مفقودة";
}

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4", $username, $password);
} catch (Exception $e) {
    $allGood = false;
    $issues[] = "مشكلة في قاعدة البيانات";
}

// فحص الملفات
foreach ($coreFiles as $file => $desc) {
    if (!file_exists($file)) {
        $allGood = false;
        $issues[] = "ملف مفقود: {$file}";
    }
}

if ($allGood) {
    echo "<div class='card' style='border-left-color: #28a745; background: #d4edda;'>";
    echo "<h3 class='status-good'>✅ النظام جاهز للعمل!</h3>";
    echo "<p>جميع المكونات تعمل بشكل صحيح.</p>";
    echo "<a href='http://localhost:8000' class='btn' target='_blank'>🚀 تشغيل النظام</a>";
    echo "</div>";
} else {
    echo "<div class='card' style='border-left-color: #dc3545; background: #f8d7da;'>";
    echo "<h3 class='status-error'>❌ يوجد مشاكل في النظام</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

echo "<div class='container'>";
echo "<p style='text-align: center; color: #666; margin-top: 30px;'>";
echo "تم إنشاء هذا التقرير في " . date('Y-m-d H:i:s') . " | SeaSystem ERP v1.0";
echo "</p>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
