# SeaSystem ERP - Apache Configuration
# إعدادات Apache لنظام SeaSystem

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# توجيه جميع الطلبات إلى public/index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع عرض نوع المحتوى الخاطئ
    Header always set X-Content-Type-Options nosniff
    
    # منع عرض الصفحة في إطار
    Header always set X-Frame-Options DENY
    
    # حماية من XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# منع الوصول للملفات الحساسة
<FilesMatch "\.(env|log|sql|md)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلدات النظام
RedirectMatch 404 /\..*$
RedirectMatch 404 /config/
RedirectMatch 404 /core/
RedirectMatch 404 /database/
RedirectMatch 404 /logs/

# إعدادات الضغط
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات الكاش
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>
