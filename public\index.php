<?php
/**
 * SeaSystem ERP - Entry Point
 * نقطة الدخول الرئيسية لنظام SeaSystem
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

// بدء الجلسة أولاً
session_start();

// بدء قياس الأداء
$startTime = microtime(true);

// تعريف المسارات الأساسية
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('MODULES_PATH', ROOT_PATH . '/modules');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('LOGS_PATH', ROOT_PATH . '/logs');

// تضمين الملفات الأساسية
require_once CORE_PATH . '/Environment.php';
require_once CORE_PATH . '/helpers.php';
require_once CORE_PATH . '/Database.php';
require_once CORE_PATH . '/Session.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/RBAC.php';
require_once CORE_PATH . '/Middleware.php';
require_once CORE_PATH . '/Logger.php';
require_once CONFIG_PATH . '/app.php';

// تحميل متغيرات البيئة
Environment::load();

// إعداد معالجة الأخطاء
set_error_handler('handleError');
set_exception_handler('handleException');
register_shutdown_function('handleShutdown');

// إعداد المنطقة الزمنية
date_default_timezone_set(config('timezone', 'Asia/Riyadh'));

// إعداد الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// إعداد إعدادات PHP للإنتاج
if (config('env') === 'production') {
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOGS_PATH . '/php_errors.log');
} else {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

// بدء الجلسة
Session::start();
Session::initializeSession();

// إنشاء كائنات النظام الأساسية
$auth = new Auth();
$rbac = new RBAC();
$middleware = new Middleware();

// معالجة الطلب
try {
    // الحصول على المسار المطلوب
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    
    // إزالة المسار الأساسي إذا كان التطبيق في مجلد فرعي
    $basePath = dirname($_SERVER['SCRIPT_NAME']);
    if ($basePath !== '/') {
        $path = substr($path, strlen($basePath));
    }
    
    // تنظيف المسار
    $path = trim($path, '/');
    if (empty($path)) {
        $path = 'dashboard';
    }
    
    // تحليل المسار
    $pathParts = explode('/', $path);
    $module = $pathParts[0] ?? 'dashboard';
    $controller = $pathParts[1] ?? 'index';
    $action = $pathParts[2] ?? 'index';
    $id = $pathParts[3] ?? null;
    
    // توجيه الطلبات الخاصة
    switch ($path) {
        case 'login':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                handleAuth('login');
            } else {
                handleAuth('showLoginForm');
            }
            break;

        case 'logout':
            handleAuth('logout');
            break;
            
        case 'dashboard':
            handleDashboard();
            break;
            
        case 'api':
            handleApi();
            break;
            
        default:
            handleModuleRequest($module, $controller, $action, $id);
            break;
    }
    
} catch (Exception $e) {
    handleException($e);
}

// انتهاء قياس الأداء
$endTime = microtime(true);
$executionTime = $endTime - $startTime;

// تسجيل الأداء إذا كان بطيئاً
if ($executionTime > 2) {
    Logger::logPerformance($path, $executionTime, [
        'method' => $requestMethod,
        'memory_peak' => memory_get_peak_usage(true)
    ]);
}

/**
 * معالجة طلبات المصادقة
 */
function handleAuth($action)
{

    require_once MODULES_PATH . '/users/controllers/AuthController.php';
    $controller = new AuthController();

    switch ($action) {
        case 'showLoginForm':
            $controller->showLoginForm();
            break;
        case 'login':
            $controller->login();
            break;
        case 'logout':
            $controller->logout();
            break;
        default:
            show404();
    }
}

/**
 * معالجة لوحة التحكم
 */
function handleDashboard()
{
    global $middleware, $auth;

    // التحقق من تسجيل الدخول
    if (!$middleware->requireAuth()) {
        return;
    }

    // الحصول على بيانات المستخدم الحالي
    $currentUser = $auth->user();

    // الحصول على إحصائيات سريعة
    try {
        $db = Database::getInstance();

        // إحصائيات الموظفين
        $employeeStats = $db->selectOne("SELECT COUNT(*) as total FROM employees WHERE status = 'active'");
        $totalEmployees = $employeeStats['total'] ?? 0;

        // إحصائيات المبيعات (محاكاة)
        $totalSales = 1250000;

        // إحصائيات المخزون (محاكاة)
        $totalProducts = 1847;

        // صافي الربح (محاكاة)
        $netProfit = 325000;

        // تمرير البيانات للعرض
        $dashboardData = [
            'user' => $currentUser,
            'stats' => [
                'employees' => $totalEmployees,
                'sales' => $totalSales,
                'products' => $totalProducts,
                'profit' => $netProfit
            ]
        ];

    } catch (Exception $e) {
        Logger::error("خطأ في تحميل بيانات لوحة التحكم: " . $e->getMessage());
        $dashboardData = [
            'user' => $currentUser,
            'stats' => [
                'employees' => 0,
                'sales' => 0,
                'products' => 0,
                'profit' => 0
            ]
        ];
    }

    // عرض لوحة التحكم
    extract($dashboardData);
    include ROOT_PATH . '/modules/dashboard/views/index.php';
}

/**
 * معالجة طلبات API
 */
function handleApi()
{
    global $middleware;
    
    // إعداد headers للـ API
    header('Content-Type: application/json; charset=utf-8');
    
    // التحقق من تفعيل API
    if (!config('api.enabled', false)) {
        http_response_code(503);
        echo json_encode(['error' => 'API غير متاح'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // معالجة CORS
    if (config('api.enable_cors', false)) {
        $allowedOrigins = config('api.cors_origins', ['*']);
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins)) {
            header("Access-Control-Allow-Origin: $origin");
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        }
    }
    
    // معالجة OPTIONS request
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        return;
    }
    
    // تطبيق middleware للـ API
    if (!$middleware->rateLimit(config('api.rate_limit', 1000), 3600)) {
        return;
    }
    
    // توجيه طلب API
    $pathInfo = $_SERVER['PATH_INFO'] ?? '';
    $apiPath = trim(str_replace('/api', '', $pathInfo), '/');
    
    if (empty($apiPath)) {
        echo json_encode([
            'name' => config('name'),
            'version' => config('api.version', 'v1'),
            'status' => 'active'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    // معالجة طلبات API المختلفة
    $apiParts = explode('/', $apiPath);
    $apiModule = $apiParts[0] ?? '';
    
    switch ($apiModule) {
        case 'auth':
            handleApiAuth();
            break;
        case 'employees':
            handleApiEmployees();
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'API endpoint غير موجود'], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * معالجة طلبات الوحدات
 */
function handleModuleRequest($module, $controller, $action, $id)
{
    global $middleware;
    
    // التحقق من وجود الوحدة
    if (!module_enabled($module)) {
        show404();
        return;
    }
    
    // مسار ملف المتحكم
    $controllerFile = MODULES_PATH . "/{$module}/controllers/" . ucfirst($controller) . "Controller.php";
    
    if (!file_exists($controllerFile)) {
        show404();
        return;
    }
    
    // تضمين المتحكم
    require_once $controllerFile;
    
    $controllerClass = ucfirst($controller) . 'Controller';
    
    if (!class_exists($controllerClass)) {
        show404();
        return;
    }
    
    // إنشاء كائن المتحكم
    $controllerInstance = new $controllerClass();
    
    // التحقق من وجود الدالة
    if (!method_exists($controllerInstance, $action)) {
        show404();
        return;
    }
    
    // تطبيق middleware حسب الحاجة
    $middlewares = getControllerMiddlewares($module, $controller, $action);
    
    if (!Middleware::apply($middlewares)) {
        return;
    }
    
    // تنفيذ الدالة
    if ($id !== null) {
        $controllerInstance->$action($id);
    } else {
        $controllerInstance->$action();
    }
}

/**
 * الحصول على middleware للمتحكم
 */
function getControllerMiddlewares($module, $controller, $action)
{
    $middlewares = ['requireAuth'];
    
    // إضافة middleware حسب الوحدة
    switch ($module) {
        case 'hr':
            $middlewares[] = ['requirePermission', 'hr.employees.view'];
            break;
        case 'finance':
            $middlewares[] = ['requirePermission', 'finance.accounts.view'];
            break;
        case 'inventory':
            $middlewares[] = ['requirePermission', 'inventory.products.view'];
            break;
        case 'sales':
            $middlewares[] = ['requirePermission', 'sales.orders.view'];
            break;
    }
    
    // إضافة middleware حسب العملية
    if (in_array($action, ['store', 'update', 'destroy'])) {
        $middlewares[] = 'verifyCsrf';
    }
    
    return $middlewares;
}

/**
 * عرض صفحة 404
 */
function show404()
{
    http_response_code(404);
    echo "<h1>404 - الصفحة غير موجودة</h1>";
    echo "<p>الصفحة التي تبحث عنها غير موجودة.</p>";
    echo "<a href='/dashboard'>العودة إلى لوحة التحكم</a>";
}

/**
 * معالجة الأخطاء
 */
function handleError($severity, $message, $file, $line)
{
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'trace' => debug_backtrace()
    ];
    
    Logger::error("PHP Error: {$message}", $error);
    
    if (config('debug', false)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb;'>";
        echo "<strong>خطأ:</strong> {$message}<br>";
        echo "<strong>الملف:</strong> {$file}<br>";
        echo "<strong>السطر:</strong> {$line}";
        echo "</div>";
    }
    
    return true;
}

/**
 * معالجة الاستثناءات
 */
function handleException($exception)
{
    Logger::logException($exception);
    
    http_response_code(500);
    
    if (config('debug', false)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border: 1px solid #f5c6cb;'>";
        echo "<h2>استثناء غير معالج</h2>";
        echo "<p><strong>الرسالة:</strong> " . $exception->getMessage() . "</p>";
        echo "<p><strong>الملف:</strong> " . $exception->getFile() . "</p>";
        echo "<p><strong>السطر:</strong> " . $exception->getLine() . "</p>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        echo "<h1>حدث خطأ في النظام</h1>";
        echo "<p>نعتذر، حدث خطأ غير متوقع. يرجى المحاولة لاحقاً.</p>";
    }
}

/**
 * معالجة إغلاق النظام
 */
function handleShutdown()
{
    $error = error_get_last();
    
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        Logger::critical("Fatal Error: {$error['message']}", [
            'file' => $error['file'],
            'line' => $error['line']
        ]);
        
        if (!config('debug', false)) {
            echo "<h1>خطأ فادح في النظام</h1>";
            echo "<p>حدث خطأ فادح. يرجى الاتصال بالدعم الفني.</p>";
        }
    }
}

/**
 * معالجة API المصادقة
 */
function handleApiAuth()
{
    // تنفيذ API المصادقة
    echo json_encode(['message' => 'API المصادقة'], JSON_UNESCAPED_UNICODE);
}

/**
 * معالجة API الموظفين
 */
function handleApiEmployees()
{
    global $middleware;
    
    if (!$middleware->requirePermission('hr.employees.view')) {
        return;
    }
    
    require_once MODULES_PATH . '/hr/models/Employee.php';
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            $employees = Employee::getAllWithDetails();
            echo json_encode($employees, JSON_UNESCAPED_UNICODE);
            break;
            
        case 'POST':
            // إنشاء موظف جديد
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة'], JSON_UNESCAPED_UNICODE);
    }
}
