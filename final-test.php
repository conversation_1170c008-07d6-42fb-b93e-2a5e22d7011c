<?php
/**
 * الاختبار النهائي للنظام
 */

echo "🧪 الاختبار النهائي للنظام\n";
echo "==========================\n\n";

// 1. اختبار قاعدة البيانات
try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ الاتصال بقاعدة البيانات\n";
    
    // عدد الجداول
    $result = $pdo->query('SHOW TABLES');
    $tables = $result->fetchAll(PDO::FETCH_NUM);
    echo "📊 عدد الجداول: " . count($tables) . "\n";
    
    // قائمة الجداول
    echo "\n📋 الجداول الموجودة:\n";
    foreach ($tables as $table) {
        echo "  - " . $table[0] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. اختبار الملفات
$files = [
    'core/Auth.php' => 'نظام المصادقة',
    'core/Database.php' => 'قاعدة البيانات', 
    'core/Controller.php' => 'المتحكم الأساسي',
    'modules/users/controllers/UserController.php' => 'متحكم المستخدمين',
    'modules/sales/controllers/CustomerController.php' => 'متحكم العملاء',
    'modules/inventory/controllers/ProductController.php' => 'متحكم المنتجات',
    'modules/users/views/index.php' => 'عرض المستخدمين',
    'modules/dashboard/views/index.php' => 'لوحة التحكم'
];

echo "📁 اختبار الملفات:\n";
foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}\n";
    } else {
        echo "❌ {$description} - مفقود ({$file})\n";
    }
}

echo "\n🎉 انتهى الاختبار!\n";
?>
