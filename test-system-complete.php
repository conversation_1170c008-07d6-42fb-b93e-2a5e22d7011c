<?php
/**
 * اختبار النظام الكامل
 */

echo "🧪 اختبار النظام الكامل\n";
echo "======================\n\n";

// 1. اختبار قاعدة البيانات
try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ الاتصال بقاعدة البيانات\n";
    
    // عدد الجداول
    $result = $pdo->query('SHOW TABLES');
    $tables = $result->fetchAll(PDO::FETCH_NUM);
    echo "📊 عدد الجداول: " . count($tables) . "\n";
    
    // عدد المستخدمين
    $result = $pdo->query('SELECT COUNT(*) as count FROM users');
    $userCount = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "👥 عدد المستخدمين: {$userCount}\n";
    
    // عدد الصلاحيات
    $result = $pdo->query('SELECT COUNT(*) as count FROM permissions');
    $permissionCount = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "🔐 عدد الصلاحيات: {$permissionCount}\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. اختبار الملفات الأساسية
$coreFiles = [
    'core/Auth.php',
    'core/Database.php', 
    'core/Controller.php',
    'core/Session.php',
    'core/RBAC.php',
    'core/helpers.php'
];

echo "📁 اختبار الملفات الأساسية:\n";
foreach ($coreFiles as $file) {
    if (file_exists($file)) {
        echo "✅ {$file}\n";
    } else {
        echo "❌ {$file} - مفقود\n";
    }
}

echo "\n";

// 3. اختبار Controllers
$controllers = [
    'modules/users/controllers/UserController.php',
    'modules/users/controllers/AuthController.php',
    'modules/sales/controllers/CustomerController.php',
    'modules/inventory/controllers/ProductController.php'
];

echo "🎮 اختبار Controllers:\n";
foreach ($controllers as $controller) {
    if (file_exists($controller)) {
        echo "✅ {$controller}\n";
    } else {
        echo "❌ {$controller} - مفقود\n";
    }
}

echo "\n";

// 4. اختبار Views
$views = [
    'modules/users/views/index.php',
    'modules/dashboard/views/index.php'
];

echo "👁️ اختبار Views:\n";
foreach ($views as $view) {
    if (file_exists($view)) {
        echo "✅ {$view}\n";
    } else {
        echo "❌ {$view} - مفقود\n";
    }
}

echo "\n";

// 5. اختبار إعدادات الوحدات
echo "⚙️ اختبار إعدادات الوحدات:\n";
$configFile = 'config/app.php';
if (file_exists($configFile)) {
    $config = include $configFile;
    $modules = $config['modules'] ?? [];
    
    foreach ($modules as $module => $settings) {
        $status = $settings['enabled'] ? '✅ مفعل' : '❌ معطل';
        echo "{$status} {$module}\n";
    }
} else {
    echo "❌ ملف الإعدادات مفقود\n";
}

echo "\n";

// 6. اختبار الخادم
echo "🌐 اختبار الخادم:\n";
$testUrl = 'http://localhost:8000';
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'GET'
    ]
]);

$result = @file_get_contents($testUrl, false, $context);
if ($result !== false) {
    echo "✅ الخادم يعمل على {$testUrl}\n";
} else {
    echo "❌ الخادم لا يعمل على {$testUrl}\n";
}

echo "\n🎉 انتهى الاختبار!\n";
?>
