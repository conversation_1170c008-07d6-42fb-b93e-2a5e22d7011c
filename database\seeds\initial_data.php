<?php
/**
 * إدراج البيانات الأولية
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🌱 إدراج البيانات الأولية...\n";
    echo "===========================\n\n";

    // إدراج الصلاحيات الأساسية
    $permissions = [
        ['name' => 'dashboard_view', 'description' => 'عرض لوحة التحكم'],
        ['name' => 'users_view', 'description' => 'عرض المستخدمين'],
        ['name' => 'users_create', 'description' => 'إنشاء مستخدمين'],
        ['name' => 'users_edit', 'description' => 'تعديل المستخدمين'],
        ['name' => 'users_delete', 'description' => 'حذف المستخدمين'],
        ['name' => 'employees_view', 'description' => 'عرض الموظفين'],
        ['name' => 'employees_create', 'description' => 'إنشاء موظفين'],
        ['name' => 'employees_edit', 'description' => 'تعديل الموظفين'],
        ['name' => 'employees_delete', 'description' => 'حذف الموظفين'],
        ['name' => 'finance_view', 'description' => 'عرض المالية'],
        ['name' => 'finance_create', 'description' => 'إنشاء عمليات مالية'],
        ['name' => 'finance_edit', 'description' => 'تعديل العمليات المالية'],
        ['name' => 'finance_delete', 'description' => 'حذف العمليات المالية'],
        ['name' => 'inventory_view', 'description' => 'عرض المخزون'],
        ['name' => 'inventory_create', 'description' => 'إنشاء منتجات'],
        ['name' => 'inventory_edit', 'description' => 'تعديل المنتجات'],
        ['name' => 'inventory_delete', 'description' => 'حذف المنتجات'],
        ['name' => 'sales_view', 'description' => 'عرض المبيعات'],
        ['name' => 'sales_create', 'description' => 'إنشاء مبيعات'],
        ['name' => 'sales_edit', 'description' => 'تعديل المبيعات'],
        ['name' => 'sales_delete', 'description' => 'حذف المبيعات'],
        ['name' => 'reports_view', 'description' => 'عرض التقارير'],
        ['name' => 'settings_view', 'description' => 'عرض الإعدادات'],
        ['name' => 'settings_edit', 'description' => 'تعديل الإعدادات']
    ];

    foreach ($permissions as $permission) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO permissions (name, description) VALUES (?, ?)");
        $stmt->execute([$permission['name'], $permission['description']]);
    }
    echo "✅ تم إدراج الصلاحيات الأساسية\n";

    // ربط صلاحيات المدير العام
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'Super Admin'");
    $stmt->execute();
    $superAdminRole = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($superAdminRole) {
        $stmt = $pdo->prepare("SELECT id FROM permissions");
        $stmt->execute();
        $allPermissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($allPermissions as $permission) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
            $stmt->execute([$superAdminRole['id'], $permission['id']]);
        }
        echo "✅ تم ربط جميع الصلاحيات بدور المدير العام\n";
    }

    // إدراج وحدات القياس الأساسية
    $units = [
        ['name' => 'قطعة', 'symbol' => 'قطعة'],
        ['name' => 'كيلوجرام', 'symbol' => 'كجم'],
        ['name' => 'جرام', 'symbol' => 'جم'],
        ['name' => 'لتر', 'symbol' => 'لتر'],
        ['name' => 'متر', 'symbol' => 'م'],
        ['name' => 'سنتيمتر', 'symbol' => 'سم'],
        ['name' => 'صندوق', 'symbol' => 'صندوق'],
        ['name' => 'كرتون', 'symbol' => 'كرتون']
    ];

    foreach ($units as $unit) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO units (name, symbol) VALUES (?, ?)");
        $stmt->execute([$unit['name'], $unit['symbol']]);
    }
    echo "✅ تم إدراج وحدات القياس الأساسية\n";

    // إدراج فئات المنتجات الأساسية
    $categories = [
        ['name' => 'إلكترونيات', 'description' => 'الأجهزة الإلكترونية والكهربائية'],
        ['name' => 'ملابس', 'description' => 'الملابس والأزياء'],
        ['name' => 'أغذية', 'description' => 'المواد الغذائية والمشروبات'],
        ['name' => 'أثاث', 'description' => 'الأثاث والديكور'],
        ['name' => 'كتب', 'description' => 'الكتب والمطبوعات'],
        ['name' => 'رياضة', 'description' => 'المعدات الرياضية'],
        ['name' => 'صحة وجمال', 'description' => 'منتجات الصحة والجمال'],
        ['name' => 'أدوات منزلية', 'description' => 'الأدوات والمعدات المنزلية']
    ];

    foreach ($categories as $category) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO product_categories (name, description) VALUES (?, ?)");
        $stmt->execute([$category['name'], $category['description']]);
    }
    echo "✅ تم إدراج فئات المنتجات الأساسية\n";

    // إدراج دليل الحسابات الأساسي
    $accounts = [
        ['account_code' => '1000', 'account_name' => 'الأصول', 'account_type' => 'asset', 'parent_id' => null],
        ['account_code' => '1100', 'account_name' => 'الأصول المتداولة', 'account_type' => 'asset', 'parent_id' => 1],
        ['account_code' => '1110', 'account_name' => 'النقدية', 'account_type' => 'asset', 'parent_id' => 2],
        ['account_code' => '1120', 'account_name' => 'البنوك', 'account_type' => 'asset', 'parent_id' => 2],
        ['account_code' => '1130', 'account_name' => 'العملاء', 'account_type' => 'asset', 'parent_id' => 2],
        ['account_code' => '1140', 'account_name' => 'المخزون', 'account_type' => 'asset', 'parent_id' => 2],
        
        ['account_code' => '2000', 'account_name' => 'الخصوم', 'account_type' => 'liability', 'parent_id' => null],
        ['account_code' => '2100', 'account_name' => 'الخصوم المتداولة', 'account_type' => 'liability', 'parent_id' => 7],
        ['account_code' => '2110', 'account_name' => 'الموردين', 'account_type' => 'liability', 'parent_id' => 8],
        ['account_code' => '2120', 'account_name' => 'الضرائب المستحقة', 'account_type' => 'liability', 'parent_id' => 8],
        
        ['account_code' => '3000', 'account_name' => 'حقوق الملكية', 'account_type' => 'equity', 'parent_id' => null],
        ['account_code' => '3100', 'account_name' => 'رأس المال', 'account_type' => 'equity', 'parent_id' => 11],
        ['account_code' => '3200', 'account_name' => 'الأرباح المحتجزة', 'account_type' => 'equity', 'parent_id' => 11],
        
        ['account_code' => '4000', 'account_name' => 'الإيرادات', 'account_type' => 'revenue', 'parent_id' => null],
        ['account_code' => '4100', 'account_name' => 'إيرادات المبيعات', 'account_type' => 'revenue', 'parent_id' => 14],
        ['account_code' => '4200', 'account_name' => 'إيرادات أخرى', 'account_type' => 'revenue', 'parent_id' => 14],
        
        ['account_code' => '5000', 'account_name' => 'المصروفات', 'account_type' => 'expense', 'parent_id' => null],
        ['account_code' => '5100', 'account_name' => 'تكلفة البضاعة المباعة', 'account_type' => 'expense', 'parent_id' => 17],
        ['account_code' => '5200', 'account_name' => 'مصروفات التشغيل', 'account_type' => 'expense', 'parent_id' => 17],
        ['account_code' => '5210', 'account_name' => 'الرواتب والأجور', 'account_type' => 'expense', 'parent_id' => 19],
        ['account_code' => '5220', 'account_name' => 'الإيجار', 'account_type' => 'expense', 'parent_id' => 19],
        ['account_code' => '5230', 'account_name' => 'الكهرباء والماء', 'account_type' => 'expense', 'parent_id' => 19]
    ];

    foreach ($accounts as $account) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO accounts (account_code, account_name, account_type, parent_id) VALUES (?, ?, ?, ?)");
        $stmt->execute([$account['account_code'], $account['account_name'], $account['account_type'], $account['parent_id']]);
    }
    echo "✅ تم إدراج دليل الحسابات الأساسي\n";

    // إدراج مخزن افتراضي
    $stmt = $pdo->prepare("INSERT IGNORE INTO warehouses (name, code, address) VALUES (?, ?, ?)");
    $stmt->execute(['المخزن الرئيسي', 'MAIN', 'المخزن الرئيسي للشركة']);
    echo "✅ تم إدراج المخزن الافتراضي\n";

    echo "\n🎉 تم إدراج جميع البيانات الأولية بنجاح!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
