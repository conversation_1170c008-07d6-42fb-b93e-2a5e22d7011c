<?php
/**
 * تنظيف ملفات الاختبار
 */

echo "🧹 تنظيف ملفات الاختبار...\n";
echo "========================\n\n";

$testFiles = [
    'test-auth-simple.php',
    'test-auth-direct.php',
    'test-simple-auth.php',
    'test-login-direct.php',
    'test-login-no-session.php',
    'test-final.php',
    'simple-test.php',
    'simple-login-test.php',
    'direct-login-test.php',
    'test-db-simple.php',
    'create-login-attempts.php',
    'check-activity-logs.php',
    'cleanup.php'
];

$deletedCount = 0;

foreach ($testFiles as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "✅ تم حذف: {$file}\n";
            $deletedCount++;
        } else {
            echo "❌ فشل حذف: {$file}\n";
        }
    }
}

echo "\n📊 النتائج:\n";
echo "تم حذف {$deletedCount} ملف\n";

// حذف مجلد logs إذا كان فارغاً
if (is_dir('logs')) {
    $files = scandir('logs');
    $files = array_diff($files, ['.', '..']);
    
    if (empty($files)) {
        if (rmdir('logs')) {
            echo "✅ تم حذف مجلد logs الفارغ\n";
        }
    } else {
        echo "📁 مجلد logs يحتوي على ملفات، لم يتم حذفه\n";
    }
}

echo "\n🎉 انتهى التنظيف!\n";
?>
