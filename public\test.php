<?php
/**
 * صفحة اختبار النظام
 */

echo "<h1>🔍 اختبار نظام SeaSystem</h1>";

// اختبار PHP
echo "<h2>✅ PHP يعمل بشكل صحيح</h2>";
echo "<p>إصدار PHP: " . PHP_VERSION . "</p>";

// اختبار المسارات
echo "<h2>📁 المسارات:</h2>";
echo "<ul>";
echo "<li>ROOT_PATH: " . dirname(__DIR__) . "</li>";
echo "<li>Current Directory: " . __DIR__ . "</li>";
echo "<li>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "</ul>";

// اختبار الملفات الأساسية
echo "<h2>📄 الملفات الأساسية:</h2>";
$coreFiles = [
    'Environment.php',
    'Database.php', 
    'Session.php',
    'Auth.php',
    'helpers.php'
];

echo "<ul>";
foreach ($coreFiles as $file) {
    $path = dirname(__DIR__) . '/core/' . $file;
    $exists = file_exists($path);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file} - " . ($exists ? 'موجود' : 'غير موجود') . "</li>";
}
echo "</ul>";

// اختبار ملف التكوين
echo "<h2>⚙️ ملفات التكوين:</h2>";
$configFiles = [
    '.env',
    'app.php'
];

echo "<ul>";
foreach ($configFiles as $file) {
    $path = dirname(__DIR__) . '/config/' . $file;
    $exists = file_exists($path);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file} - " . ($exists ? 'موجود' : 'غير موجود') . "</li>";
}
echo "</ul>";

// اختبار لوحة التحكم
echo "<h2>🎛️ ملفات لوحة التحكم:</h2>";
$dashboardFiles = [
    'modules/dashboard/views/index.php'
];

echo "<ul>";
foreach ($dashboardFiles as $file) {
    $path = dirname(__DIR__) . '/' . $file;
    $exists = file_exists($path);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file} - " . ($exists ? 'موجود' : 'غير موجود') . "</li>";
}
echo "</ul>";

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبار قاعدة البيانات:</h2>";

try {
    // تحميل متغيرات البيئة
    $envFile = dirname(__DIR__) . '/config/.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        $lines = explode("\n", $envContent);
        $env = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) continue;
            
            $parts = explode('=', $line, 2);
            if (count($parts) === 2) {
                $env[trim($parts[0])] = trim($parts[1], '"\'');
            }
        }
        
        // محاولة الاتصال بقاعدة البيانات
        $host = $env['DB_HOST'] ?? 'localhost';
        $dbname = $env['DB_NAME'] ?? 'R1';
        $username = $env['DB_USERNAME'] ?? 'root';
        $password = $env['DB_PASSWORD'] ?? '';
        
        echo "<p>🔗 محاولة الاتصال بقاعدة البيانات...</p>";
        echo "<ul>";
        echo "<li>المضيف: {$host}</li>";
        echo "<li>قاعدة البيانات: {$dbname}</li>";
        echo "<li>المستخدم: {$username}</li>";
        echo "</ul>";
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        echo "<p>✅ <strong>تم الاتصال بقاعدة البيانات بنجاح!</strong></p>";
        
        // اختبار الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p>📊 الجداول الموجودة (" . count($tables) . "):</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p>❌ ملف .env غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>خطأ في الاتصال بقاعدة البيانات:</strong></p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

// اختبار الجلسة
echo "<h2>🔐 اختبار الجلسة:</h2>";
session_start();
echo "<p>✅ تم بدء الجلسة بنجاح</p>";
echo "<p>معرف الجلسة: " . session_id() . "</p>";

// روابط الاختبار
echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<ul>";
echo "<li><a href='/'>الصفحة الرئيسية</a></li>";
echo "<li><a href='/dashboard'>لوحة التحكم</a></li>";
echo "<li><a href='/login'>تسجيل الدخول</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>تاريخ الاختبار:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #2c3e50;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin: 5px 0;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
