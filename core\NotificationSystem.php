<?php
/**
 * نظام الإشعارات المتقدم
 * Advanced Notification System
 */

class NotificationSystem
{
    private $db;
    private $channels = ['database', 'email', 'sms', 'push'];
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * إرسال إشعار
     */
    public function send($userId, $title, $message, $type = 'info', $channels = ['database'], $data = [])
    {
        try {
            $notificationId = $this->createNotification($userId, $title, $message, $type, $data);
            
            foreach ($channels as $channel) {
                $this->sendToChannel($channel, $userId, $title, $message, $type, $data);
            }
            
            return $notificationId;
            
        } catch (Exception $e) {
            error_log("Notification error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إشعار في قاعدة البيانات
     */
    private function createNotification($userId, $title, $message, $type, $data)
    {
        return $this->db->insert('notifications', [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * إرسال إلى قناة محددة
     */
    private function sendToChannel($channel, $userId, $title, $message, $type, $data)
    {
        switch ($channel) {
            case 'email':
                $this->sendEmail($userId, $title, $message, $data);
                break;
            case 'sms':
                $this->sendSMS($userId, $message, $data);
                break;
            case 'push':
                $this->sendPushNotification($userId, $title, $message, $data);
                break;
            case 'database':
                // تم إنشاؤه بالفعل
                break;
        }
    }

    /**
     * إرسال بريد إلكتروني
     */
    private function sendEmail($userId, $title, $message, $data)
    {
        $user = $this->db->selectOne("SELECT email, first_name, last_name FROM users WHERE id = ?", [$userId]);
        
        if (!$user || empty($user['email'])) {
            return false;
        }

        $to = $user['email'];
        $subject = $title;
        $body = $this->buildEmailTemplate($user['first_name'] . ' ' . $user['last_name'], $title, $message, $data);
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=utf-8',
            'From: SeaSystem ERP <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    }

    /**
     * بناء قالب البريد الإلكتروني
     */
    private function buildEmailTemplate($userName, $title, $message, $data)
    {
        return "
        <!DOCTYPE html>
        <html dir='rtl' lang='ar'>
        <head>
            <meta charset='utf-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1'>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; background-color: #f8f9fa; margin: 0; padding: 20px; }
                .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
                .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🌊 SeaSystem ERP</h1>
                    <p>نظام إدارة موارد المؤسسات</p>
                </div>
                <div class='content'>
                    <h2>مرحباً {$userName}</h2>
                    <h3>{$title}</h3>
                    <p>{$message}</p>
                    " . (isset($data['action_url']) ? "<a href='{$data['action_url']}' class='btn'>عرض التفاصيل</a>" : "") . "
                </div>
                <div class='footer'>
                    <p>هذا البريد تم إرساله تلقائياً من نظام SeaSystem ERP</p>
                    <p>© " . date('Y') . " SeaSystem. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * إرسال رسالة نصية
     */
    private function sendSMS($userId, $message, $data)
    {
        $user = $this->db->selectOne("SELECT phone FROM users WHERE id = ?", [$userId]);
        
        if (!$user || empty($user['phone'])) {
            return false;
        }

        // هنا يمكن دمج مع خدمة SMS مثل Twilio أو STC
        // مثال بسيط:
        $phone = $user['phone'];
        $smsMessage = strip_tags($message);
        
        // في الإنتاج، استخدم API حقيقي للـ SMS
        error_log("SMS to {$phone}: {$smsMessage}");
        
        return true;
    }

    /**
     * إرسال إشعار فوري
     */
    private function sendPushNotification($userId, $title, $message, $data)
    {
        // يمكن دمج مع Firebase Cloud Messaging أو OneSignal
        // مثال بسيط:
        $payload = [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];
        
        // في الإنتاج، استخدم خدمة push notifications حقيقية
        error_log("Push notification: " . json_encode($payload));
        
        return true;
    }

    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($userId, $limit = 20, $unreadOnly = false)
    {
        $condition = $unreadOnly ? "AND is_read = 0" : "";
        
        return $this->db->select("
            SELECT * FROM notifications 
            WHERE user_id = ? {$condition}
            ORDER BY created_at DESC 
            LIMIT ?
        ", [$userId, $limit]);
    }

    /**
     * عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount($userId)
    {
        return $this->db->selectOne("
            SELECT COUNT(*) as count 
            FROM notifications 
            WHERE user_id = ? AND is_read = 0
        ", [$userId])['count'];
    }

    /**
     * تمييز إشعار كمقروء
     */
    public function markAsRead($notificationId, $userId = null)
    {
        $condition = $userId ? "AND user_id = ?" : "";
        $params = [$notificationId];
        if ($userId) $params[] = $userId;

        return $this->db->update('notifications', 
            ['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')],
            ["id = ? {$condition}" => $params]
        );
    }

    /**
     * تمييز جميع الإشعارات كمقروءة
     */
    public function markAllAsRead($userId)
    {
        return $this->db->update('notifications',
            ['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')],
            ['user_id' => $userId, 'is_read' => 0]
        );
    }

    /**
     * حذف إشعار
     */
    public function deleteNotification($notificationId, $userId = null)
    {
        $conditions = ['id' => $notificationId];
        if ($userId) $conditions['user_id'] = $userId;

        return $this->db->delete('notifications', $conditions);
    }

    /**
     * حذف الإشعارات القديمة
     */
    public function cleanupOldNotifications($days = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return $this->db->delete('notifications', [
            'created_at <' => $cutoffDate,
            'is_read' => 1
        ]);
    }

    /**
     * إشعارات النظام المحددة مسبقاً
     */
    public function sendWelcomeNotification($userId)
    {
        return $this->send(
            $userId,
            'مرحباً بك في SeaSystem ERP',
            'نرحب بك في نظام إدارة موارد المؤسسات. نتمنى لك تجربة ممتعة ومفيدة.',
            'welcome',
            ['database', 'email']
        );
    }

    public function sendLowStockAlert($userId, $productName, $currentStock, $minStock)
    {
        return $this->send(
            $userId,
            'تحذير: مخزون منخفض',
            "المنتج '{$productName}' وصل إلى مستوى مخزون منخفض. المخزون الحالي: {$currentStock}، الحد الأدنى: {$minStock}",
            'warning',
            ['database', 'email'],
            ['product_name' => $productName, 'current_stock' => $currentStock, 'min_stock' => $minStock]
        );
    }

    public function sendOrderConfirmation($userId, $orderNumber, $totalAmount)
    {
        return $this->send(
            $userId,
            'تأكيد الطلب',
            "تم تأكيد طلبك رقم {$orderNumber} بقيمة {$totalAmount} ر.س بنجاح.",
            'success',
            ['database', 'email', 'sms'],
            ['order_number' => $orderNumber, 'total_amount' => $totalAmount]
        );
    }

    public function sendPaymentReminder($userId, $invoiceNumber, $amount, $dueDate)
    {
        return $this->send(
            $userId,
            'تذكير بالدفع',
            "تذكير: الفاتورة رقم {$invoiceNumber} بقيمة {$amount} ر.س مستحقة في {$dueDate}",
            'reminder',
            ['database', 'email'],
            ['invoice_number' => $invoiceNumber, 'amount' => $amount, 'due_date' => $dueDate]
        );
    }

    /**
     * إنشاء جدول الإشعارات
     */
    public static function createNotificationsTable()
    {
        $db = Database::getInstance();
        
        $sql = "
        CREATE TABLE IF NOT EXISTS notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error', 'reminder', 'welcome') DEFAULT 'info',
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        )";
        
        return $db->exec($sql);
    }
}
?>
