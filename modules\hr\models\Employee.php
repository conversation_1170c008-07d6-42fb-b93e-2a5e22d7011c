<?php
/**
 * SeaSystem Employee Model
 * نموذج الموظفين
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once dirname(dirname(dirname(__DIR__))) . '/core/Model.php';

class Employee extends Model
{
    protected $table = 'employees';
    protected $primaryKey = 'id';
    
    protected $fillable = [
        'employee_code', 'user_id', 'first_name', 'last_name', 'email', 'phone',
        'national_id', 'birth_date', 'gender', 'address', 'hire_date',
        'department_id', 'position_id', 'salary', 'status', 'manager_id'
    ];
    
    protected $guarded = ['id', 'created_at', 'updated_at'];
    
    // قواعد التحقق من صحة البيانات
    protected $rules = [
        'employee_code' => 'required|unique:employees,employee_code',
        'first_name' => 'required|min:2|max:50',
        'last_name' => 'required|min:2|max:50',
        'email' => 'email|unique:employees,email',
        'phone' => 'phone',
        'national_id' => 'required|unique:employees,national_id',
        'birth_date' => 'date|before:today',
        'gender' => 'required|in:male,female',
        'hire_date' => 'required|date',
        'department_id' => 'required|exists:departments,id',
        'position_id' => 'required|exists:positions,id',
        'salary' => 'required|numeric|min:0',
        'status' => 'in:active,inactive,terminated',
        'manager_id' => 'exists:employees,id'
    ];
    
    // رسائل التحقق المخصصة
    protected $messages = [
        'employee_code.required' => 'رقم الموظف مطلوب',
        'employee_code.unique' => 'رقم الموظف موجود مسبقاً',
        'first_name.required' => 'الاسم الأول مطلوب',
        'first_name.min' => 'الاسم الأول يجب أن يكون على الأقل حرفين',
        'first_name.max' => 'الاسم الأول يجب ألا يزيد عن 50 حرف',
        'last_name.required' => 'اسم العائلة مطلوب',
        'last_name.min' => 'اسم العائلة يجب أن يكون على الأقل حرفين',
        'last_name.max' => 'اسم العائلة يجب ألا يزيد عن 50 حرف',
        'email.email' => 'البريد الإلكتروني غير صحيح',
        'email.unique' => 'البريد الإلكتروني موجود مسبقاً',
        'national_id.required' => 'رقم الهوية مطلوب',
        'national_id.unique' => 'رقم الهوية موجود مسبقاً',
        'birth_date.date' => 'تاريخ الميلاد غير صحيح',
        'birth_date.before' => 'تاريخ الميلاد يجب أن يكون قبل اليوم',
        'gender.required' => 'الجنس مطلوب',
        'gender.in' => 'الجنس يجب أن يكون ذكر أو أنثى',
        'hire_date.required' => 'تاريخ التوظيف مطلوب',
        'hire_date.date' => 'تاريخ التوظيف غير صحيح',
        'department_id.required' => 'القسم مطلوب',
        'department_id.exists' => 'القسم المحدد غير موجود',
        'position_id.required' => 'المنصب مطلوب',
        'position_id.exists' => 'المنصب المحدد غير موجود',
        'salary.required' => 'الراتب مطلوب',
        'salary.numeric' => 'الراتب يجب أن يكون رقم',
        'salary.min' => 'الراتب يجب أن يكون أكبر من صفر',
        'status.in' => 'حالة الموظف غير صحيحة',
        'manager_id.exists' => 'المدير المحدد غير موجود'
    ];

    /**
     * الحصول على جميع الموظفين مع تفاصيل القسم والمنصب
     * 
     * @param array $filters المرشحات
     * @param int $limit عدد السجلات
     * @param int $offset الإزاحة
     * @return array
     */
    public static function getAllWithDetails($filters = [], $limit = null, $offset = 0)
    {
        $instance = new static();
        
        $query = "
            SELECT 
                e.*,
                d.name as department_name,
                p.title as position_title,
                m.first_name as manager_first_name,
                m.last_name as manager_last_name,
                u.username as username
            FROM {$instance->table} e
            LEFT JOIN departments d ON e.department_id = d.id
            LEFT JOIN positions p ON e.position_id = p.id
            LEFT JOIN employees m ON e.manager_id = m.id
            LEFT JOIN users u ON e.user_id = u.id
        ";
        
        $whereConditions = [];
        $params = [];
        
        // تطبيق المرشحات
        if (!empty($filters['status'])) {
            $whereConditions[] = "e.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['department_id'])) {
            $whereConditions[] = "e.department_id = ?";
            $params[] = $filters['department_id'];
        }
        
        if (!empty($filters['search'])) {
            $whereConditions[] = "(e.first_name LIKE ? OR e.last_name LIKE ? OR e.employee_code LIKE ? OR e.email LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(' AND ', $whereConditions);
        }
        
        $query .= " ORDER BY e.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }
        
        return $instance->db->select($query, $params);
    }

    /**
     * البحث عن موظف برقم الموظف
     * 
     * @param string $employeeCode رقم الموظف
     * @return Employee|null
     */
    public static function findByEmployeeCode($employeeCode)
    {
        $instance = new static();
        $result = $instance->db->selectOne(
            "SELECT * FROM {$instance->table} WHERE employee_code = ?",
            [$employeeCode]
        );
        
        if ($result) {
            $model = new static($result);
            $model->original = $result;
            return $model;
        }
        
        return null;
    }

    /**
     * البحث عن موظف برقم الهوية
     * 
     * @param string $nationalId رقم الهوية
     * @return Employee|null
     */
    public static function findByNationalId($nationalId)
    {
        $instance = new static();
        $result = $instance->db->selectOne(
            "SELECT * FROM {$instance->table} WHERE national_id = ?",
            [$nationalId]
        );
        
        if ($result) {
            $model = new static($result);
            $model->original = $result;
            return $model;
        }
        
        return null;
    }

    /**
     * الحصول على الموظفين حسب القسم
     * 
     * @param int $departmentId معرف القسم
     * @param string $status الحالة
     * @return array
     */
    public static function getByDepartment($departmentId, $status = 'active')
    {
        $conditions = ['department_id' => $departmentId];
        if ($status) {
            $conditions['status'] = $status;
        }
        
        return static::where($conditions);
    }

    /**
     * الحصول على الموظفين تحت إدارة مدير معين
     * 
     * @param int $managerId معرف المدير
     * @param string $status الحالة
     * @return array
     */
    public static function getByManager($managerId, $status = 'active')
    {
        $conditions = ['manager_id' => $managerId];
        if ($status) {
            $conditions['status'] = $status;
        }
        
        return static::where($conditions);
    }

    /**
     * إحصائيات الموظفين
     * 
     * @return array
     */
    public static function getStatistics()
    {
        $instance = new static();
        
        $stats = [];
        
        // إجمالي الموظفين
        $total = $instance->db->selectOne("SELECT COUNT(*) as count FROM {$instance->table}");
        $stats['total'] = $total['count'];
        
        // الموظفين النشطين
        $active = $instance->db->selectOne("SELECT COUNT(*) as count FROM {$instance->table} WHERE status = 'active'");
        $stats['active'] = $active['count'];
        
        // الموظفين غير النشطين
        $inactive = $instance->db->selectOne("SELECT COUNT(*) as count FROM {$instance->table} WHERE status = 'inactive'");
        $stats['inactive'] = $inactive['count'];
        
        // الموظفين المنتهية خدمتهم
        $terminated = $instance->db->selectOne("SELECT COUNT(*) as count FROM {$instance->table} WHERE status = 'terminated'");
        $stats['terminated'] = $terminated['count'];
        
        // الموظفين الجدد هذا الشهر
        $newThisMonth = $instance->db->selectOne("
            SELECT COUNT(*) as count 
            FROM {$instance->table} 
            WHERE YEAR(hire_date) = YEAR(CURDATE()) 
            AND MONTH(hire_date) = MONTH(CURDATE())
        ");
        $stats['new_this_month'] = $newThisMonth['count'];
        
        // متوسط الراتب
        $avgSalary = $instance->db->selectOne("SELECT AVG(salary) as avg_salary FROM {$instance->table} WHERE status = 'active'");
        $stats['average_salary'] = round($avgSalary['avg_salary'], 2);
        
        return $stats;
    }

    /**
     * الحصول على الموظفين حسب الأقسام
     * 
     * @return array
     */
    public static function getByDepartments()
    {
        $instance = new static();
        
        return $instance->db->select("
            SELECT 
                d.name as department_name,
                COUNT(e.id) as employee_count,
                AVG(e.salary) as avg_salary
            FROM departments d
            LEFT JOIN {$instance->table} e ON d.id = e.department_id AND e.status = 'active'
            GROUP BY d.id, d.name
            ORDER BY employee_count DESC
        ");
    }

    /**
     * تحديث حالة الموظف
     * 
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateStatus($status)
    {
        $validStatuses = ['active', 'inactive', 'terminated'];
        
        if (!in_array($status, $validStatuses)) {
            throw new Exception("حالة الموظف غير صحيحة");
        }
        
        $this->status = $status;
        return $this->save();
    }

    /**
     * الحصول على الاسم الكامل
     * 
     * @return string
     */
    public function getFullName()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * الحصول على العمر
     * 
     * @return int|null
     */
    public function getAge()
    {
        if (!$this->birth_date) {
            return null;
        }
        
        $birthDate = new DateTime($this->birth_date);
        $today = new DateTime();
        return $today->diff($birthDate)->y;
    }

    /**
     * الحصول على سنوات الخبرة
     * 
     * @return int
     */
    public function getYearsOfService()
    {
        if (!$this->hire_date) {
            return 0;
        }
        
        $hireDate = new DateTime($this->hire_date);
        $today = new DateTime();
        return $today->diff($hireDate)->y;
    }

    /**
     * التحقق من صحة البيانات
     *
     * @return bool
     */
    protected function validate()
    {
        $errors = [];

        // التحقق من رقم الموظف
        if (empty($this->employee_code)) {
            $errors[] = $this->messages['employee_code.required'];
        } else {
            // التحقق من عدم التكرار
            $existing = static::findByEmployeeCode($this->employee_code);
            if ($existing && (!$this->exists() || $existing->id != $this->id)) {
                $errors[] = $this->messages['employee_code.unique'];
            }
        }

        // التحقق من الاسم الأول
        if (empty($this->first_name)) {
            $errors[] = $this->messages['first_name.required'];
        } elseif (strlen($this->first_name) < 2) {
            $errors[] = $this->messages['first_name.min'];
        } elseif (strlen($this->first_name) > 50) {
            $errors[] = $this->messages['first_name.max'];
        }

        // التحقق من اسم العائلة
        if (empty($this->last_name)) {
            $errors[] = $this->messages['last_name.required'];
        } elseif (strlen($this->last_name) < 2) {
            $errors[] = $this->messages['last_name.min'];
        } elseif (strlen($this->last_name) > 50) {
            $errors[] = $this->messages['last_name.max'];
        }

        // التحقق من البريد الإلكتروني
        if (!empty($this->email)) {
            if (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = $this->messages['email.email'];
            } else {
                // التحقق من عدم التكرار
                $existing = $this->db->selectOne(
                    "SELECT id FROM {$this->table} WHERE email = ? AND id != ?",
                    [$this->email, $this->id ?? 0]
                );
                if ($existing) {
                    $errors[] = $this->messages['email.unique'];
                }
            }
        }

        // التحقق من رقم الهوية
        if (empty($this->national_id)) {
            $errors[] = $this->messages['national_id.required'];
        } else {
            $existing = static::findByNationalId($this->national_id);
            if ($existing && (!$this->exists() || $existing->id != $this->id)) {
                $errors[] = $this->messages['national_id.unique'];
            }
        }

        // التحقق من الجنس
        if (empty($this->gender)) {
            $errors[] = $this->messages['gender.required'];
        } elseif (!in_array($this->gender, ['male', 'female'])) {
            $errors[] = $this->messages['gender.in'];
        }

        // التحقق من تاريخ التوظيف
        if (empty($this->hire_date)) {
            $errors[] = $this->messages['hire_date.required'];
        }

        // التحقق من القسم
        if (empty($this->department_id)) {
            $errors[] = $this->messages['department_id.required'];
        } else {
            $department = $this->db->selectOne("SELECT id FROM departments WHERE id = ?", [$this->department_id]);
            if (!$department) {
                $errors[] = $this->messages['department_id.exists'];
            }
        }

        // التحقق من المنصب
        if (empty($this->position_id)) {
            $errors[] = $this->messages['position_id.required'];
        } else {
            $position = $this->db->selectOne("SELECT id FROM positions WHERE id = ?", [$this->position_id]);
            if (!$position) {
                $errors[] = $this->messages['position_id.exists'];
            }
        }

        // التحقق من الراتب
        if (empty($this->salary)) {
            $errors[] = $this->messages['salary.required'];
        } elseif (!is_numeric($this->salary) || $this->salary < 0) {
            $errors[] = $this->messages['salary.numeric'];
        }

        // التحقق من المدير
        if (!empty($this->manager_id)) {
            $manager = static::find($this->manager_id);
            if (!$manager) {
                $errors[] = $this->messages['manager_id.exists'];
            }
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }

        return true;
    }

    /**
     * تحويل النموذج إلى مصفوفة مع البيانات الإضافية
     *
     * @return array
     */
    public function toArray()
    {
        $data = parent::toArray();

        // إضافة البيانات المحسوبة
        $data['full_name'] = $this->getFullName();
        $data['age'] = $this->getAge();
        $data['years_of_service'] = $this->getYearsOfService();

        return $data;
    }

    /**
     * إنشاء رقم موظف تلقائي
     *
     * @param string $prefix البادئة
     * @return string
     */
    public static function generateEmployeeCode($prefix = 'EMP')
    {
        $instance = new static();

        // الحصول على آخر رقم موظف
        $lastEmployee = $instance->db->selectOne(
            "SELECT employee_code FROM {$instance->table}
             WHERE employee_code LIKE ?
             ORDER BY employee_code DESC LIMIT 1",
            [$prefix . '%']
        );

        if ($lastEmployee) {
            // استخراج الرقم من رقم الموظف الأخير
            $lastNumber = intval(substr($lastEmployee['employee_code'], strlen($prefix)));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
