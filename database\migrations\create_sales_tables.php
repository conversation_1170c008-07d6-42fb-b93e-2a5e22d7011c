<?php
/**
 * إنشاء جداول المبيعات والعملاء
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=R1;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "🛒 إنشاء جداول المبيعات والعملاء...\n";
    echo "=================================\n\n";

    // جدول العملاء
    $sql = "
    CREATE TABLE IF NOT EXISTS customers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        customer_code VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        mobile VARCHAR(50),
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        tax_number VARCHAR(100),
        credit_limit DECIMAL(15,2) DEFAULT 0,
        payment_terms INT DEFAULT 30,
        customer_type ENUM('individual', 'company') DEFAULT 'individual',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer_code (customer_code),
        INDEX idx_name (name),
        INDEX idx_email (email)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول customers\n";

    // جدول عروض الأسعار
    $sql = "
    CREATE TABLE IF NOT EXISTS quotations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        quotation_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id INT NOT NULL,
        quotation_date DATE NOT NULL,
        valid_until DATE,
        subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
        tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'sent', 'accepted', 'rejected', 'expired') DEFAULT 'draft',
        notes TEXT,
        terms_conditions TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_quotation_number (quotation_number),
        INDEX idx_quotation_date (quotation_date),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول quotations\n";

    // جدول تفاصيل عروض الأسعار
    $sql = "
    CREATE TABLE IF NOT EXISTS quotation_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        quotation_id INT NOT NULL,
        product_id INT,
        description VARCHAR(255) NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        discount_percent DECIMAL(5,2) DEFAULT 0,
        total_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL,
        INDEX idx_quotation (quotation_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول quotation_items\n";

    // جدول أوامر البيع
    $sql = "
    CREATE TABLE IF NOT EXISTS sales_orders (
        id INT PRIMARY KEY AUTO_INCREMENT,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id INT NOT NULL,
        quotation_id INT,
        order_date DATE NOT NULL,
        delivery_date DATE,
        subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
        tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        shipping_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'draft',
        shipping_address TEXT,
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
        FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_order_number (order_number),
        INDEX idx_order_date (order_date),
        INDEX idx_status (status)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول sales_orders\n";

    // جدول تفاصيل أوامر البيع
    $sql = "
    CREATE TABLE IF NOT EXISTS sales_order_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        sales_order_id INT NOT NULL,
        product_id INT NOT NULL,
        description VARCHAR(255) NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        discount_percent DECIMAL(5,2) DEFAULT 0,
        total_price DECIMAL(15,2) NOT NULL DEFAULT 0,
        delivered_quantity DECIMAL(10,2) DEFAULT 0,
        FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
        INDEX idx_sales_order (sales_order_id),
        INDEX idx_product (product_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول sales_order_items\n";

    // جدول إشعارات التسليم
    $sql = "
    CREATE TABLE IF NOT EXISTS delivery_notes (
        id INT PRIMARY KEY AUTO_INCREMENT,
        delivery_number VARCHAR(50) UNIQUE NOT NULL,
        sales_order_id INT NOT NULL,
        delivery_date DATE NOT NULL,
        delivered_by VARCHAR(255),
        received_by VARCHAR(255),
        notes TEXT,
        status ENUM('draft', 'delivered', 'confirmed') DEFAULT 'draft',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE RESTRICT,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_delivery_number (delivery_number),
        INDEX idx_delivery_date (delivery_date)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول delivery_notes\n";

    // جدول تفاصيل إشعارات التسليم
    $sql = "
    CREATE TABLE IF NOT EXISTS delivery_note_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        delivery_note_id INT NOT NULL,
        sales_order_item_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (delivery_note_id) REFERENCES delivery_notes(id) ON DELETE CASCADE,
        FOREIGN KEY (sales_order_item_id) REFERENCES sales_order_items(id) ON DELETE RESTRICT,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
        INDEX idx_delivery_note (delivery_note_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول delivery_note_items\n";

    // جدول المرتجعات
    $sql = "
    CREATE TABLE IF NOT EXISTS returns (
        id INT PRIMARY KEY AUTO_INCREMENT,
        return_number VARCHAR(50) UNIQUE NOT NULL,
        return_type ENUM('sales_return', 'purchase_return') NOT NULL,
        reference_id INT NOT NULL,
        return_date DATE NOT NULL,
        reason TEXT,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status ENUM('draft', 'approved', 'processed', 'cancelled') DEFAULT 'draft',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_return_number (return_number),
        INDEX idx_return_date (return_date),
        INDEX idx_return_type (return_type)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول returns\n";

    // جدول تفاصيل المرتجعات
    $sql = "
    CREATE TABLE IF NOT EXISTS return_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        return_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL,
        unit_price DECIMAL(15,2) NOT NULL,
        total_price DECIMAL(15,2) NOT NULL,
        reason TEXT,
        FOREIGN KEY (return_id) REFERENCES returns(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
        INDEX idx_return (return_id)
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول return_items\n";

    echo "\n🎉 تم إنشاء جميع جداول المبيعات والعملاء بنجاح!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
