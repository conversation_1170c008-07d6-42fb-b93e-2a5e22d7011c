<?php
/**
 * ReportsController
 * تحكم في التقارير
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class ReportsController extends Controller
{
    private $auth;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // التحقق من تسجيل الدخول
        $this->requireAuth();
    }

    /**
     * صفحة التقارير الرئيسية
     */
    public function index()
    {
        $this->render('reports/index', [
            'title' => 'التقارير'
        ]);
    }

    /**
     * تقارير المبيعات
     */
    public function sales()
    {
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');
        $customerId = $_GET['customer_id'] ?? null;

        // إحصائيات المبيعات
        $salesStats = $this->getSalesStatistics($dateFrom, $dateTo, $customerId);
        
        // أفضل المنتجات مبيعاً
        $topProducts = $this->getTopSellingProducts($dateFrom, $dateTo, 10);
        
        // أفضل العملاء
        $topCustomers = $this->getTopCustomers($dateFrom, $dateTo, 10);
        
        // مبيعات يومية
        $dailySales = $this->getDailySales($dateFrom, $dateTo);

        $this->render('reports/sales', [
            'title' => 'تقارير المبيعات',
            'salesStats' => $salesStats,
            'topProducts' => $topProducts,
            'topCustomers' => $topCustomers,
            'dailySales' => $dailySales,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'customerId' => $customerId
        ]);
    }

    /**
     * تقارير المخزون
     */
    public function inventory()
    {
        $categoryId = $_GET['category_id'] ?? null;
        $warehouseId = $_GET['warehouse_id'] ?? null;

        // إحصائيات المخزون
        $inventoryStats = $this->getInventoryStatistics($categoryId, $warehouseId);
        
        // المنتجات منخفضة المخزون
        $lowStockProducts = $this->getLowStockProducts();
        
        // أكثر المنتجات حركة
        $mostActiveProducts = $this->getMostActiveProducts(30);
        
        // قيمة المخزون
        $inventoryValue = $this->getInventoryValue($categoryId, $warehouseId);

        $this->render('reports/inventory', [
            'title' => 'تقارير المخزون',
            'inventoryStats' => $inventoryStats,
            'lowStockProducts' => $lowStockProducts,
            'mostActiveProducts' => $mostActiveProducts,
            'inventoryValue' => $inventoryValue,
            'categoryId' => $categoryId,
            'warehouseId' => $warehouseId
        ]);
    }

    /**
     * تقارير العملاء
     */
    public function customers()
    {
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');

        // إحصائيات العملاء
        $customerStats = $this->getCustomerStatistics($dateFrom, $dateTo);
        
        // العملاء الجدد
        $newCustomers = $this->getNewCustomers($dateFrom, $dateTo);
        
        // العملاء الأكثر نشاطاً
        $activeCustomers = $this->getMostActiveCustomers($dateFrom, $dateTo);

        $this->render('reports/customers', [
            'title' => 'تقارير العملاء',
            'customerStats' => $customerStats,
            'newCustomers' => $newCustomers,
            'activeCustomers' => $activeCustomers,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo
        ]);
    }

    /**
     * تقارير مالية
     */
    public function financial()
    {
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');

        // الإيرادات والمصروفات
        $financialSummary = $this->getFinancialSummary($dateFrom, $dateTo);
        
        // تدفق نقدي
        $cashFlow = $this->getCashFlow($dateFrom, $dateTo);
        
        // الفواتير المستحقة
        $overdueInvoices = $this->getOverdueInvoices();

        $this->render('reports/financial', [
            'title' => 'التقارير المالية',
            'financialSummary' => $financialSummary,
            'cashFlow' => $cashFlow,
            'overdueInvoices' => $overdueInvoices,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo
        ]);
    }

    /**
     * تصدير تقرير
     */
    public function export()
    {
        $type = $_GET['type'] ?? 'sales';
        $format = $_GET['format'] ?? 'excel';
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');

        switch ($type) {
            case 'sales':
                $this->exportSalesReport($format, $dateFrom, $dateTo);
                break;
            case 'inventory':
                $this->exportInventoryReport($format);
                break;
            case 'customers':
                $this->exportCustomersReport($format, $dateFrom, $dateTo);
                break;
            case 'financial':
                $this->exportFinancialReport($format, $dateFrom, $dateTo);
                break;
            default:
                $this->showError('نوع التقرير غير صحيح');
        }
    }

    /**
     * الحصول على إحصائيات المبيعات
     */
    private function getSalesStatistics($dateFrom, $dateTo, $customerId = null)
    {
        $customerCondition = $customerId ? "AND customer_id = ?" : "";
        $params = [$dateFrom, $dateTo];
        if ($customerId) $params[] = $customerId;

        return [
            'total_sales' => $this->db->selectOne("
                SELECT COALESCE(SUM(total_amount), 0) as total 
                FROM sales_orders 
                WHERE order_date BETWEEN ? AND ? {$customerCondition}
            ", $params)['total'],
            
            'total_orders' => $this->db->selectOne("
                SELECT COUNT(*) as count 
                FROM sales_orders 
                WHERE order_date BETWEEN ? AND ? {$customerCondition}
            ", $params)['count'],
            
            'average_order' => $this->db->selectOne("
                SELECT COALESCE(AVG(total_amount), 0) as avg 
                FROM sales_orders 
                WHERE order_date BETWEEN ? AND ? {$customerCondition}
            ", $params)['avg']
        ];
    }

    /**
     * الحصول على أفضل المنتجات مبيعاً
     */
    private function getTopSellingProducts($dateFrom, $dateTo, $limit = 10)
    {
        return $this->db->select("
            SELECT p.name, p.sku, SUM(soi.quantity) as total_quantity, 
                   SUM(soi.total_price) as total_sales
            FROM sales_order_items soi
            JOIN products p ON soi.product_id = p.id
            JOIN sales_orders so ON soi.sales_order_id = so.id
            WHERE so.order_date BETWEEN ? AND ?
            GROUP BY p.id, p.name, p.sku
            ORDER BY total_sales DESC
            LIMIT ?
        ", [$dateFrom, $dateTo, $limit]);
    }

    /**
     * الحصول على أفضل العملاء
     */
    private function getTopCustomers($dateFrom, $dateTo, $limit = 10)
    {
        return $this->db->select("
            SELECT c.name, c.customer_code, COUNT(so.id) as total_orders,
                   SUM(so.total_amount) as total_sales
            FROM customers c
            JOIN sales_orders so ON c.id = so.customer_id
            WHERE so.order_date BETWEEN ? AND ?
            GROUP BY c.id, c.name, c.customer_code
            ORDER BY total_sales DESC
            LIMIT ?
        ", [$dateFrom, $dateTo, $limit]);
    }

    /**
     * الحصول على المبيعات اليومية
     */
    private function getDailySales($dateFrom, $dateTo)
    {
        return $this->db->select("
            SELECT DATE(order_date) as date, 
                   COUNT(*) as orders_count,
                   SUM(total_amount) as total_sales
            FROM sales_orders
            WHERE order_date BETWEEN ? AND ?
            GROUP BY DATE(order_date)
            ORDER BY date
        ", [$dateFrom, $dateTo]);
    }

    /**
     * الحصول على إحصائيات المخزون
     */
    private function getInventoryStatistics($categoryId = null, $warehouseId = null)
    {
        $categoryCondition = $categoryId ? "AND p.category_id = ?" : "";
        $warehouseCondition = $warehouseId ? "AND ws.warehouse_id = ?" : "";
        
        $params = [];
        if ($categoryId) $params[] = $categoryId;
        if ($warehouseId) $params[] = $warehouseId;

        return [
            'total_products' => $this->db->selectOne("
                SELECT COUNT(DISTINCT p.id) as count 
                FROM products p
                LEFT JOIN warehouse_stock ws ON p.id = ws.product_id
                WHERE p.is_active = 1 {$categoryCondition} {$warehouseCondition}
            ", $params)['count'],
            
            'total_stock_value' => $this->db->selectOne("
                SELECT COALESCE(SUM(p.cost_price * p.current_stock), 0) as total 
                FROM products p
                WHERE p.is_active = 1 {$categoryCondition}
            ", $categoryId ? [$categoryId] : [])['total'],
            
            'low_stock_count' => $this->db->selectOne("
                SELECT COUNT(*) as count 
                FROM products p
                WHERE p.current_stock <= p.min_stock_level 
                AND p.is_active = 1 {$categoryCondition}
            ", $categoryId ? [$categoryId] : [])['count']
        ];
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    private function getLowStockProducts()
    {
        return $this->db->select("
            SELECT p.name, p.sku, p.current_stock, p.min_stock_level,
                   c.name as category_name
            FROM products p
            LEFT JOIN product_categories c ON p.category_id = c.id
            WHERE p.current_stock <= p.min_stock_level 
            AND p.is_active = 1
            ORDER BY (p.current_stock - p.min_stock_level) ASC
            LIMIT 20
        ");
    }

    /**
     * الحصول على أكثر المنتجات حركة
     */
    private function getMostActiveProducts($days = 30)
    {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->db->select("
            SELECT p.name, p.sku, COUNT(sm.id) as movements_count,
                   SUM(ABS(sm.quantity)) as total_quantity
            FROM products p
            JOIN stock_movements sm ON p.id = sm.product_id
            WHERE sm.movement_date >= ?
            GROUP BY p.id, p.name, p.sku
            ORDER BY movements_count DESC
            LIMIT 20
        ", [$dateFrom]);
    }

    /**
     * الحصول على قيمة المخزون
     */
    private function getInventoryValue($categoryId = null, $warehouseId = null)
    {
        $categoryCondition = $categoryId ? "AND p.category_id = ?" : "";
        $params = $categoryId ? [$categoryId] : [];

        return $this->db->selectOne("
            SELECT 
                SUM(p.cost_price * p.current_stock) as cost_value,
                SUM(p.selling_price * p.current_stock) as selling_value
            FROM products p
            WHERE p.is_active = 1 {$categoryCondition}
        ", $params);
    }

    /**
     * تصدير تقرير المبيعات
     */
    private function exportSalesReport($format, $dateFrom, $dateTo)
    {
        $salesData = $this->db->select("
            SELECT so.order_number, so.order_date, c.name as customer_name,
                   so.total_amount, so.status
            FROM sales_orders so
            JOIN customers c ON so.customer_id = c.id
            WHERE so.order_date BETWEEN ? AND ?
            ORDER BY so.order_date DESC
        ", [$dateFrom, $dateTo]);

        if ($format === 'excel') {
            $this->exportToExcel($salesData, 'sales_report_' . date('Y-m-d'));
        } elseif ($format === 'pdf') {
            $this->exportToPDF($salesData, 'تقرير المبيعات');
        } else {
            $this->exportToCSV($salesData, 'sales_report_' . date('Y-m-d'));
        }
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filename)
    {
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
        
        echo "<table border='1'>";
        if (!empty($data)) {
            // رؤوس الأعمدة
            echo "<tr>";
            foreach (array_keys($data[0]) as $header) {
                echo "<th>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
            
            // البيانات
            foreach ($data as $row) {
                echo "<tr>";
                foreach ($row as $cell) {
                    echo "<td>" . htmlspecialchars($cell) . "</td>";
                }
                echo "</tr>";
            }
        }
        echo "</table>";
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCSV($data, $filename)
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        if (!empty($data)) {
            // رؤوس الأعمدة
            fputcsv($output, array_keys($data[0]));
            
            // البيانات
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPDF($data, $title)
    {
        // يمكن استخدام مكتبة مثل TCPDF أو FPDF
        // هنا مثال بسيط
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="report.pdf"');
        
        // في الإنتاج، يجب استخدام مكتبة PDF حقيقية
        echo "PDF generation requires a PDF library like TCPDF";
        exit;
    }
}
?>
