<?php
/**
 * ProductController
 * تحكم في إدارة المنتجات
 */

require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class ProductController extends Controller
{
    private $auth;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->db = Database::getInstance();
        
        // التحقق من تسجيل الدخول
        if (!$this->auth->check()) {
            $this->redirect('/login');
        }
    }

    /**
     * عرض قائمة المنتجات
     */
    public function index()
    {
        $products = $this->db->select("
            SELECT p.*, c.name as category_name, u.name as unit_name
            FROM products p 
            LEFT JOIN product_categories c ON p.category_id = c.id 
            LEFT JOIN units u ON p.unit_id = u.id 
            ORDER BY p.created_at DESC
        ");

        $this->render('inventory/products/index', [
            'products' => $products,
            'title' => 'إدارة المنتجات'
        ]);
    }

    /**
     * عرض نموذج إضافة منتج جديد
     */
    public function create()
    {
        $categories = $this->db->select("SELECT * FROM product_categories WHERE is_active = 1 ORDER BY name");
        $units = $this->db->select("SELECT * FROM units WHERE is_active = 1 ORDER BY name");

        $this->render('inventory/products/create', [
            'categories' => $categories,
            'units' => $units,
            'title' => 'إضافة منتج جديد'
        ]);
    }

    /**
     * حفظ منتج جديد
     */
    public function store()
    {
        try {
            // التحقق من البيانات
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $categoryId = intval($_POST['category_id'] ?? 0);
            $unitId = intval($_POST['unit_id'] ?? 0);
            $costPrice = floatval($_POST['cost_price'] ?? 0);
            $sellingPrice = floatval($_POST['selling_price'] ?? 0);
            $minStockLevel = intval($_POST['min_stock_level'] ?? 0);
            $barcode = trim($_POST['barcode'] ?? '');

            if (empty($name)) {
                throw new Exception('اسم المنتج مطلوب');
            }

            // إنشاء SKU
            $lastProduct = $this->db->selectOne("SELECT sku FROM products ORDER BY id DESC LIMIT 1");
            $nextNumber = 1;
            if ($lastProduct) {
                $lastNumber = intval(substr($lastProduct['sku'], 4));
                $nextNumber = $lastNumber + 1;
            }
            $sku = 'PROD' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);

            // التحقق من عدم تكرار الباركود
            if (!empty($barcode)) {
                $existingProduct = $this->db->selectOne("SELECT id FROM products WHERE barcode = ?", [$barcode]);
                if ($existingProduct) {
                    throw new Exception('الباركود موجود مسبقاً');
                }
            }

            // إنشاء المنتج
            $productId = $this->db->insert('products', [
                'sku' => $sku,
                'name' => $name,
                'description' => $description,
                'category_id' => $categoryId > 0 ? $categoryId : null,
                'unit_id' => $unitId > 0 ? $unitId : null,
                'cost_price' => $costPrice,
                'selling_price' => $sellingPrice,
                'min_stock_level' => $minStockLevel,
                'barcode' => $barcode,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->jsonResponse(['success' => true, 'message' => 'تم إنشاء المنتج بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * عرض تفاصيل منتج
     */
    public function show($id)
    {
        $product = $this->db->selectOne("
            SELECT p.*, c.name as category_name, u.name as unit_name
            FROM products p 
            LEFT JOIN product_categories c ON p.category_id = c.id 
            LEFT JOIN units u ON p.unit_id = u.id 
            WHERE p.id = ?
        ", [$id]);
        
        if (!$product) {
            $this->showError('المنتج غير موجود');
            return;
        }

        // جلب حركات المخزون الأخيرة
        $movements = $this->db->select("
            SELECT sm.*, w.name as warehouse_name, u.username as created_by_name
            FROM stock_movements sm 
            LEFT JOIN warehouses w ON sm.warehouse_id = w.id 
            LEFT JOIN users u ON sm.created_by = u.id 
            WHERE sm.product_id = ? 
            ORDER BY sm.movement_date DESC 
            LIMIT 20
        ", [$id]);

        // جلب مخزون المنتج في المخازن
        $warehouseStock = $this->db->select("
            SELECT ws.*, w.name as warehouse_name
            FROM warehouse_stock ws 
            LEFT JOIN warehouses w ON ws.warehouse_id = w.id 
            WHERE ws.product_id = ?
        ", [$id]);

        $this->render('inventory/products/show', [
            'product' => $product,
            'movements' => $movements,
            'warehouseStock' => $warehouseStock,
            'title' => 'تفاصيل المنتج'
        ]);
    }

    /**
     * عرض نموذج تعديل منتج
     */
    public function edit($id)
    {
        $product = $this->db->selectOne("SELECT * FROM products WHERE id = ?", [$id]);

        if (!$product) {
            $this->showError('المنتج غير موجود');
            return;
        }

        $categories = $this->db->select("SELECT * FROM product_categories WHERE is_active = 1 ORDER BY name");
        $units = $this->db->select("SELECT * FROM units WHERE is_active = 1 ORDER BY name");

        $this->render('inventory/products/edit', [
            'product' => $product,
            'categories' => $categories,
            'units' => $units,
            'title' => 'تعديل المنتج'
        ]);
    }

    /**
     * تحديث بيانات منتج
     */
    public function update($id)
    {
        try {
            $product = $this->db->selectOne("SELECT * FROM products WHERE id = ?", [$id]);
            if (!$product) {
                throw new Exception('المنتج غير موجود');
            }

            // التحقق من البيانات
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $categoryId = intval($_POST['category_id'] ?? 0);
            $unitId = intval($_POST['unit_id'] ?? 0);
            $costPrice = floatval($_POST['cost_price'] ?? 0);
            $sellingPrice = floatval($_POST['selling_price'] ?? 0);
            $minStockLevel = intval($_POST['min_stock_level'] ?? 0);
            $barcode = trim($_POST['barcode'] ?? '');
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            if (empty($name)) {
                throw new Exception('اسم المنتج مطلوب');
            }

            // التحقق من عدم تكرار الباركود
            if (!empty($barcode)) {
                $existingProduct = $this->db->selectOne("SELECT id FROM products WHERE barcode = ? AND id != ?", [$barcode, $id]);
                if ($existingProduct) {
                    throw new Exception('الباركود موجود مسبقاً');
                }
            }

            // تحديث البيانات
            $this->db->update('products', [
                'name' => $name,
                'description' => $description,
                'category_id' => $categoryId > 0 ? $categoryId : null,
                'unit_id' => $unitId > 0 ? $unitId : null,
                'cost_price' => $costPrice,
                'selling_price' => $sellingPrice,
                'min_stock_level' => $minStockLevel,
                'barcode' => $barcode,
                'is_active' => $isActive,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $id]);

            $this->jsonResponse(['success' => true, 'message' => 'تم تحديث المنتج بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * حذف منتج
     */
    public function delete($id)
    {
        try {
            $product = $this->db->selectOne("SELECT * FROM products WHERE id = ?", [$id]);
            if (!$product) {
                throw new Exception('المنتج غير موجود');
            }

            // التحقق من وجود حركات مخزون
            $hasMovements = $this->db->selectOne("SELECT id FROM stock_movements WHERE product_id = ?", [$id]);
            if ($hasMovements) {
                throw new Exception('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
            }

            // حذف المنتج
            $this->db->delete('warehouse_stock', ['product_id' => $id]);
            $this->db->delete('products', ['id' => $id]);

            $this->jsonResponse(['success' => true, 'message' => 'تم حذف المنتج بنجاح']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * البحث في المنتجات (للـ API)
     */
    public function search()
    {
        $query = $_GET['q'] ?? '';
        
        if (strlen($query) < 2) {
            $this->jsonResponse(['products' => []]);
            return;
        }

        $products = $this->db->select("
            SELECT id, sku, name, selling_price, current_stock 
            FROM products 
            WHERE (name LIKE ? OR sku LIKE ? OR barcode LIKE ?) 
            AND is_active = 1 
            ORDER BY name 
            LIMIT 20
        ", ["%{$query}%", "%{$query}%", "%{$query}%"]);

        $this->jsonResponse(['products' => $products]);
    }
}
?>
