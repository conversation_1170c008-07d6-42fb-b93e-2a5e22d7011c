<?php
/**
 * صفحة تشخيص تسجيل الدخول
 */

// بدء الجلسة
session_start();

// تحميل الملفات الأساسية
require_once 'core/Environment.php';
Environment::load();
require_once 'core/Database.php';
require_once 'core/Session.php';
require_once 'core/Auth.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص تسجيل الدخول</title>";
echo "<style>body{font-family:Arial;padding:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 تشخيص تسجيل الدخول - SeaSystem</h1>";

// معلومات الطلب
echo "<h2>📋 معلومات الطلب:</h2>";
echo "<p><strong>الطريقة:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p><strong>المسار:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>📤 بيانات POST:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    // محاولة تسجيل الدخول
    echo "<h3>🔐 محاولة تسجيل الدخول:</h3>";
    
    try {
        $auth = new Auth();
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        
        echo "<p class='info'>اسم المستخدم: " . htmlspecialchars($username) . "</p>";
        echo "<p class='info'>كلمة المرور: " . str_repeat('*', strlen($password)) . "</p>";
        
        $result = $auth->login($username, $password, false);
        
        echo "<h4>📊 نتيجة تسجيل الدخول:</h4>";
        echo "<pre>";
        print_r($result);
        echo "</pre>";
        
        if ($result['success']) {
            echo "<p class='success'>✅ تم تسجيل الدخول بنجاح!</p>";
            echo "<p><a href='/dashboard'>الذهاب إلى لوحة التحكم</a></p>";
        } else {
            echo "<p class='error'>❌ فشل تسجيل الدخول: " . htmlspecialchars($result['message']) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// نموذج تسجيل الدخول
echo "<h2>📝 نموذج تسجيل الدخول:</h2>";
echo "<form method='POST'>";
echo "<p><label>اسم المستخدم: <input type='text' name='username' value='admin' required></label></p>";
echo "<p><label>كلمة المرور: <input type='password' name='password' value='admin123' required></label></p>";
echo "<p><button type='submit'>تسجيل الدخول</button></p>";
echo "</form>";

// معلومات قاعدة البيانات
echo "<h2>🗄️ معلومات قاعدة البيانات:</h2>";
try {
    $db = Database::getInstance();
    $userCount = $db->selectOne("SELECT COUNT(*) as count FROM users")['count'];
    echo "<p class='success'>✅ متصل بقاعدة البيانات</p>";
    echo "<p>عدد المستخدمين: {$userCount}</p>";
    
    $adminUser = $db->selectOne("SELECT username, email, is_active FROM users WHERE username = 'admin'");
    if ($adminUser) {
        echo "<p class='success'>✅ المستخدم admin موجود</p>";
        echo "<p>البريد الإلكتروني: " . htmlspecialchars($adminUser['email']) . "</p>";
        echo "<p>نشط: " . ($adminUser['is_active'] ? 'نعم' : 'لا') . "</p>";
    } else {
        echo "<p class='error'>❌ المستخدم admin غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// معلومات الجلسة
echo "<h2>🔗 معلومات الجلسة:</h2>";
echo "<p>معرف الجلسة: " . session_id() . "</p>";
echo "<p>بيانات الجلسة:</p>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// روابط مفيدة
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<ul>";
echo "<li><a href='http://localhost:8000'>الصفحة الرئيسية</a></li>";
echo "<li><a href='http://localhost:8000/login'>صفحة تسجيل الدخول</a></li>";
echo "<li><a href='http://localhost:8000/dashboard'>لوحة التحكم</a></li>";
echo "<li><a href='system-status.php'>حالة النظام</a></li>";
echo "</ul>";

echo "</body>";
echo "</html>";
?>
