<?php
/**
 * SeaSystem Database Manager
 * إدارة الاتصال بقاعدة البيانات باستخدام PDO
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 */

require_once 'Environment.php';

class Database
{
    private static $instance = null;
    private $connection = null;
    private $config = [];
    private $queryLog = [];
    private $transactionLevel = 0;

    /**
     * منشئ الكلاس - خاص لتطبيق نمط Singleton
     */
    private function __construct()
    {
        // تحميل متغيرات البيئة
        Environment::load();
        
        // إعداد التكوين
        $this->config = [
            'host' => Environment::get('DB_HOST', 'localhost'),
            'port' => Environment::get('DB_PORT', 3306),
            'database' => Environment::get('DB_NAME', 'R1'),
            'username' => Environment::get('DB_USERNAME', 'root'),
            'password' => Environment::get('DB_PASSWORD', ''),
            'charset' => Environment::get('DB_CHARSET', 'utf8mb4'),
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ];

        $this->connect();
    }

    /**
     * الحصول على مثيل وحيد من الكلاس (Singleton Pattern)
     * 
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     * 
     * @throws Exception
     */
    private function connect()
    {
        try {
            $dsn = sprintf(
                "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                $this->config['host'],
                $this->config['port'],
                $this->config['database'],
                $this->config['charset']
            );

            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $this->config['options']
            );

            // تعيين المنطقة الزمنية
            $timezone = Environment::get('APP_TIMEZONE', 'Asia/Riyadh');
            $this->connection->exec("SET time_zone = '+03:00'");

        } catch (PDOException $e) {
            $this->logError("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
            throw new Exception("فشل الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات قاعدة البيانات.");
        }
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     * 
     * @return PDO
     */
    public function getConnection()
    {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     * 
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return array
     */
    public function select($query, $params = [])
    {
        try {
            error_log("Database::select - Query: {$query}");
            error_log("Database::select - Params: " . print_r($params, true));

            $stmt = $this->prepare($query);
            error_log("Database::select - Statement prepared");

            $stmt->execute($params);
            error_log("Database::select - Statement executed");

            $result = $stmt->fetchAll();
            error_log("Database::select - Results fetched: " . count($result) . " rows");

            $this->logQuery($query, $params);
            return $result;

        } catch (PDOException $e) {
            error_log("Database::select - PDO Error: " . $e->getMessage());
            $this->logError("خطأ في استعلام SELECT: " . $e->getMessage(), $query, $params);
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * تنفيذ استعلام SELECT لسجل واحد
     * 
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return array|null
     */
    public function selectOne($query, $params = [])
    {
        $result = $this->select($query, $params);
        return !empty($result) ? $result[0] : null;
    }

    /**
     * تنفيذ استعلام INSERT
     * 
     * @param string $table اسم الجدول
     * @param array $data البيانات
     * @return int معرف السجل المدرج
     */
    public function insert($table, $data)
    {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            
            $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            $stmt = $this->prepare($query);
            $stmt->execute($data);
            
            $insertId = $this->connection->lastInsertId();
            $this->logQuery($query, $data);
            
            return $insertId;
            
        } catch (PDOException $e) {
            $this->logError("خطأ في استعلام INSERT: " . $e->getMessage(), $query ?? '', $data);
            throw new Exception("خطأ في إدراج البيانات");
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     * 
     * @param string $table اسم الجدول
     * @param array $data البيانات
     * @param array $where شروط التحديث
     * @return int عدد السجلات المحدثة
     */
    public function update($table, $data, $where)
    {
        try {
            $setClause = [];
            foreach ($data as $key => $value) {
                $setClause[] = "{$key} = :{$key}";
            }
            
            $whereClause = [];
            foreach ($where as $key => $value) {
                $whereClause[] = "{$key} = :where_{$key}";
            }
            
            $query = "UPDATE {$table} SET " . implode(', ', $setClause) . 
                     " WHERE " . implode(' AND ', $whereClause);
            
            // دمج المعاملات
            $params = $data;
            foreach ($where as $key => $value) {
                $params["where_{$key}"] = $value;
            }
            
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $rowCount = $stmt->rowCount();
            $this->logQuery($query, $params);
            
            return $rowCount;
            
        } catch (PDOException $e) {
            $this->logError("خطأ في استعلام UPDATE: " . $e->getMessage(), $query ?? '', $params ?? []);
            throw new Exception("خطأ في تحديث البيانات");
        }
    }

    /**
     * تنفيذ استعلام DELETE
     * 
     * @param string $table اسم الجدول
     * @param array $where شروط الحذف
     * @return int عدد السجلات المحذوفة
     */
    public function delete($table, $where)
    {
        try {
            $whereClause = [];
            foreach ($where as $key => $value) {
                $whereClause[] = "{$key} = :{$key}";
            }
            
            $query = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
            $stmt = $this->prepare($query);
            $stmt->execute($where);
            
            $rowCount = $stmt->rowCount();
            $this->logQuery($query, $where);
            
            return $rowCount;
            
        } catch (PDOException $e) {
            $this->logError("خطأ في استعلام DELETE: " . $e->getMessage(), $query ?? '', $where);
            throw new Exception("خطأ في حذف البيانات");
        }
    }

    /**
     * تحضير الاستعلام
     * 
     * @param string $query الاستعلام
     * @return PDOStatement
     */
    public function prepare($query)
    {
        return $this->getConnection()->prepare($query);
    }

    /**
     * تنفيذ استعلام مخصص
     * 
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return PDOStatement
     */
    public function execute($query, $params = [])
    {
        try {
            $stmt = $this->prepare($query);
            $stmt->execute($params);
            
            $this->logQuery($query, $params);
            return $stmt;
            
        } catch (PDOException $e) {
            $this->logError("خطأ في تنفيذ الاستعلام: " . $e->getMessage(), $query, $params);
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction()
    {
        if ($this->transactionLevel === 0) {
            $this->getConnection()->beginTransaction();
        }
        $this->transactionLevel++;
    }

    /**
     * تأكيد المعاملة
     */
    public function commit()
    {
        if ($this->transactionLevel === 1) {
            $this->getConnection()->commit();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback()
    {
        if ($this->transactionLevel === 1) {
            $this->getConnection()->rollback();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
    }

    /**
     * تسجيل الاستعلام
     * 
     * @param string $query الاستعلام
     * @param array $params المعاملات
     */
    private function logQuery($query, $params = [])
    {
        if (Environment::get('ENABLE_QUERY_LOG', false)) {
            $this->queryLog[] = [
                'query' => $query,
                'params' => $params,
                'time' => microtime(true)
            ];
        }
    }

    /**
     * تسجيل الأخطاء
     * 
     * @param string $message رسالة الخطأ
     * @param string $query الاستعلام
     * @param array $params المعاملات
     */
    private function logError($message, $query = '', $params = [])
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'query' => $query,
            'params' => $params
        ];
        
        $logFile = dirname(__DIR__) . '/logs/database_errors.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
    }

    /**
     * الحصول على سجل الاستعلامات
     * 
     * @return array
     */
    public function getQueryLog()
    {
        return $this->queryLog;
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     * 
     * @return bool
     */
    public function testConnection()
    {
        try {
            $this->getConnection()->query('SELECT 1');
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * منع استنساخ الكائن
     */
    private function __clone() {}

    /**
     * منع إلغاء تسلسل الكائن
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}
