<?php
/**
 * Controller Base Class
 * الكلاس الأساسي للمتحكمات
 */

require_once CORE_PATH . '/Auth.php';
require_once CORE_PATH . '/Database.php';

class Controller
{
    protected $data = [];
    protected $auth;
    protected $db;
    
    public function __construct()
    {
        $this->auth = new Auth();
        $this->db = Database::getInstance();
    }
    
    /**
     * إعداد البيانات للعرض
     */
    protected function setData($key, $value = null)
    {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
    }
    
    /**
     * عرض الصفحة
     */
    protected function render($view, $data = [])
    {
        $this->data = array_merge($this->data, $data);
        
        // إضافة متغيرات عامة
        $this->data['auth'] = $this->auth;
        $this->data['user'] = $this->auth->check() ? $this->auth->user() : null;
        
        // تضمين الملف
        $viewFile = MODULES_PATH . '/' . $view . '.php';
        
        if (file_exists($viewFile)) {
            extract($this->data);
            include $viewFile;
        } else {
            throw new Exception("View file not found: {$viewFile}");
        }
    }
    
    /**
     * إعادة التوجيه
     */
    protected function redirect($url)
    {
        header("Location: {$url}");
        exit;
    }
    
    /**
     * إرجاع استجابة JSON
     */
    protected function jsonResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * عرض صفحة خطأ
     */
    protected function showError($message, $code = 404)
    {
        http_response_code($code);
        $this->render('errors/error', [
            'message' => $message,
            'code' => $code
        ]);
    }
    
    /**
     * التحقق من الصلاحية
     */
    protected function hasPermission($permission)
    {
        return $this->auth->hasPermission($permission);
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    protected function requireAuth()
    {
        if (!$this->auth->check()) {
            $this->redirect('/login');
        }
    }
}
?>
